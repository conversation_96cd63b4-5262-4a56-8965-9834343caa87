<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = $_GET['id'];

// Get appointment details
$appointment = null;
$query = "SELECT * FROM appointments WHERE appointment_id = ? AND doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $appointment_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $appointment = $result->fetch_assoc();
} else {
    // Appointment not found or doesn't belong to this doctor
    header("Location: appointments.php");
    exit();
}

// Get all patients
$patients = [];
$patients_query = "SELECT * FROM patients ORDER BY last_name, first_name";
$patients_result = $conn->query($patients_query);
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Get all departments
$departments = [];
$departments_query = "SELECT * FROM departments ORDER BY department_name";
$departments_result = $conn->query($departments_query);
if ($departments_result->num_rows > 0) {
    while ($row = $departments_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $patient_id = $_POST['patient_id'];
    $department_id = $_POST['department_id'];
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $reason = $_POST['reason'];
    $notes = $_POST['notes'];
    $status = $_POST['status'];

    // Validate inputs
    if (empty($patient_id) || empty($department_id) || empty($appointment_date) || empty($appointment_time)) {
        $error_message = "Please fill in all required fields.";
    } else {
        // Check if the appointment time is available (excluding this appointment)
        $check_query = "SELECT * FROM appointments
                        WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
                        AND status != 'cancelled' AND appointment_id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("issi", $doctor_id, $appointment_date, $appointment_time, $appointment_id);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = "This appointment time is already booked. Please select another time.";
        } else {
            // Update appointment
            $update_query = "UPDATE appointments
                            SET patient_id = ?, department_id = ?, appointment_date = ?,
                                appointment_time = ?, reason = ?, notes = ?, status = ?, updated_at = NOW()
                            WHERE appointment_id = ? AND doctor_id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("iisssssii", $patient_id, $department_id, $appointment_date, $appointment_time, $reason, $notes, $status, $appointment_id, $doctor_id);

            if ($update_stmt->execute()) {
                $success_message = "Appointment updated successfully!";
                // Refresh appointment data
                $stmt->execute();
                $result = $stmt->get_result();
                $appointment = $result->fetch_assoc();
            } else {
                $error_message = "Error updating appointment: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-edit"></i> Edit Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Edit Appointment #<?php echo $appointment_id; ?></h3>
                    <div class="actions">
                        <a href="appointments.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                        <a href="view_appointment.php?id=<?php echo $appointment_id; ?>" class="btn btn-primary"><i class="fas fa-eye"></i> View Appointment</a>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form action="edit_appointment.php?id=<?php echo $appointment_id; ?>" method="post">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="patient_id">Patient <span class="required">*</span></label>
                                    <select name="patient_id" id="patient_id" class="form-control" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>" <?php echo ($patient['patient_id'] == $appointment['patient_id']) ? 'selected' : ''; ?>>
                                                <?php echo $patient['last_name'] . ', ' . $patient['first_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="department_id">Department <span class="required">*</span></label>
                                    <select name="department_id" id="department_id" class="form-control" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo ($department['department_id'] == $appointment['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo $department['department_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="appointment_date">Date <span class="required">*</span></label>
                                    <input type="date" name="appointment_date" id="appointment_date" class="form-control" value="<?php echo $appointment['appointment_date']; ?>" required>
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="appointment_time">Time <span class="required">*</span></label>
                                    <input type="time" name="appointment_time" id="appointment_time" class="form-control" value="<?php echo $appointment['appointment_time']; ?>" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reason">Reason for Visit <span class="required">*</span></label>
                                <input type="text" name="reason" id="reason" class="form-control" value="<?php echo $appointment['reason']; ?>" required>
                            </div>

                            <div class="form-group">
                                <label for="notes">Additional Notes</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3"><?php echo $appointment['notes']; ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="status">Status <span class="required">*</span></label>
                                <select name="status" id="status" class="form-control" required>
                                    <option value="pending" <?php echo ($appointment['status'] == 'pending') ? 'selected' : ''; ?>>Pending</option>
                                    <option value="confirmed" <?php echo ($appointment['status'] == 'confirmed') ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="completed" <?php echo ($appointment['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo ($appointment['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                    <option value="no_show" <?php echo ($appointment['status'] == 'no_show') ? 'selected' : ''; ?>>No Show</option>
                                </select>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Update Appointment</button>
                                <a href="appointments.php" class="btn btn-outline">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
