<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if department ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: departments.php");
    exit();
}

$department_id = $_GET['id'];

// Get department information
$query = "SELECT * FROM departments WHERE department_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $department_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: departments.php");
    exit();
}

$department = $result->fetch_assoc();

// Get doctors in this department
$doctors = [];
$doctors_query = "SELECT d.*, u.email 
                 FROM doctors d 
                 JOIN users u ON d.user_id = u.user_id 
                 WHERE d.department_id = ?
                 ORDER BY d.last_name, d.first_name";
$doctors_stmt = $conn->prepare($doctors_query);
$doctors_stmt->bind_param("i", $department_id);
$doctors_stmt->execute();
$doctors_result = $doctors_stmt->get_result();

if ($doctors_result->num_rows > 0) {
    while ($row = $doctors_result->fetch_assoc()) {
        $doctors[] = $row;
    }
}

// Get appointment statistics for this department
$appointment_stats = [];
$stats_query = "SELECT status, COUNT(*) as count 
               FROM appointments 
               WHERE department_id = ? 
               GROUP BY status";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("i", $department_id);
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result();

if ($stats_result->num_rows > 0) {
    while ($row = $stats_result->fetch_assoc()) {
        $appointment_stats[$row['status']] = $row['count'];
    }
}

// Calculate total appointments
$total_appointments = array_sum($appointment_stats);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Department | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-hospital"></i> Department Details</h2>
                </div>
                <div class="header-right">
                    <a href="edit_department.php?id=<?php echo $department_id; ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Department
                    </a>
                    <a href="departments.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Departments
                    </a>
                </div>
            </header>
            
            <div class="content-wrapper">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Department Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="info-group">
                                    <label>Department Name:</label>
                                    <p><?php echo htmlspecialchars($department['department_name']); ?></p>
                                </div>
                                
                                <div class="info-group">
                                    <label>Description:</label>
                                    <p><?php echo nl2br(htmlspecialchars($department['description'])); ?></p>
                                </div>
                                
                                <div class="info-group">
                                    <label>Status:</label>
                                    <p>
                                        <span class="status-badge status-<?php echo strtolower($department['status']); ?>">
                                            <?php echo ucfirst($department['status']); ?>
                                        </span>
                                    </p>
                                </div>
                                
                                <div class="info-group">
                                    <label>Created At:</label>
                                    <p><?php echo date('F d, Y', strtotime($department['created_at'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Department Statistics</h3>
                            </div>
                            <div class="card-body">
                                <div class="stat-item">
                                    <div class="stat-label">Total Doctors</div>
                                    <div class="stat-value"><?php echo count($doctors); ?></div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-label">Total Appointments</div>
                                    <div class="stat-value"><?php echo $total_appointments; ?></div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-label">Pending Appointments</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['pending']) ? $appointment_stats['pending'] : 0; ?></div>
                                </div>
                                
                                <div class="stat-item">
                                    <div class="stat-label">Completed Appointments</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['completed']) ? $appointment_stats['completed'] : 0; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Doctors in this Department</h3>
                    </div>
                    <div class="card-body">
                        <?php if (count($doctors) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>Name</th>
                                            <th>Specialization</th>
                                            <th>Contact</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <tr>
                                                <td><?php echo $doctor['doctor_id']; ?></td>
                                                <td>
                                                    <div class="user-info">
                                                        <img src="../assets/images/<?php echo $doctor['profile_image']; ?>" alt="Doctor" class="user-image-sm">
                                                        <span>Dr. <?php echo htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']); ?></span>
                                                    </div>
                                                </td>
                                                <td><?php echo htmlspecialchars($doctor['specialization']); ?></td>
                                                <td>
                                                    <div><?php echo htmlspecialchars($doctor['email']); ?></div>
                                                    <div><?php echo htmlspecialchars($doctor['phone']); ?></div>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo strtolower($doctor['status']); ?>">
                                                        <?php echo ucfirst($doctor['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="view_doctor.php?id=<?php echo $doctor['doctor_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_doctor.php?id=<?php echo $doctor['doctor_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-user-md empty-icon"></i>
                                <p>No doctors assigned to this department</p>
                                <a href="add_doctor.php" class="btn btn-primary">Add Doctor</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>