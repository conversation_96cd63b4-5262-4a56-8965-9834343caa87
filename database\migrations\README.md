# Database Migrations

This directory contains database migration scripts for the CSUCC Hospital Management System.

## PHP Migration Files

1. **update_medical_fields.php**
   - Updates the medical_records table to add or modify fields
   - Adds fields for storing medical record information
   - Run this script when you need to update the medical_records table structure

2. **update_patients_table.php**
   - Updates the patients table to add or modify fields
   - Adds fields for storing additional patient information
   - Run this script when you need to update the patients table structure

## SQL Migration Files

1. **add_medical_fields.sql**
   - SQL script to add medical fields to the medical_records table
   - Contains SQL statements for altering the table structure

2. **update_appointments_table.sql**
   - SQL script to update the appointments table
   - Adds or modifies fields for storing appointment information

3. **update_patients_table.sql**
   - SQL script to update the patients table
   - Adds or modifies fields for storing patient information

4. **update_prescriptions_table.sql**
   - SQL script to update the prescriptions table
   - Adds or modifies fields for storing prescription information

## How to Run Migrations

To run a migration script, simply access it through your web browser:

```
http://localhost:8000/database/migrations/script_name.php
```

Replace `script_name.php` with the name of the migration script you want to run.

## Important Notes

- Always back up your database before running migration scripts
- Run migrations in the correct order to avoid dependency issues
- Check the output of the migration script to ensure it completed successfully
- Some migrations may require manual intervention if they encounter errors

## Creating New Migrations

When creating new migration scripts, follow these guidelines:

1. Name the file descriptively, e.g., `update_table_name.php` or `add_field_to_table.php`
2. Include a timestamp in the filename if multiple migrations affect the same table
3. Include comments in the script explaining what changes are being made
4. Add error handling to catch and report any issues
5. Add the new migration to this README file with a description of what it does
