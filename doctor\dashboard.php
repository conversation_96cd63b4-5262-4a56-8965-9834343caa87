<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get today's appointments
$today = date('Y-m-d');
$today_appointments = [];
$today_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                dep.department_name
                FROM appointments a
                JOIN patients p ON a.patient_id = p.patient_id
                JOIN departments dep ON a.department_id = dep.department_id
                WHERE a.doctor_id = ? AND a.appointment_date = ?
                ORDER BY a.appointment_time";
$today_stmt = $conn->prepare($today_query);
$today_stmt->bind_param("is", $doctor_id, $today);
$today_stmt->execute();
$today_result = $today_stmt->get_result();
if ($today_result->num_rows > 0) {
    while ($row = $today_result->fetch_assoc()) {
        $today_appointments[] = $row;
    }
}

// Get upcoming appointments (excluding today)
$upcoming_appointments = [];
$upcoming_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                  dep.department_name as department_name
                  FROM appointments a
                  JOIN patients p ON a.patient_id = p.patient_id
                  JOIN departments dep ON a.department_id = dep.department_id
                  WHERE a.doctor_id = ? AND a.appointment_date > ?
                  ORDER BY a.appointment_date, a.appointment_time
                  LIMIT 5";
$upcoming_stmt = $conn->prepare($upcoming_query);
$upcoming_stmt->bind_param("is", $doctor_id, $today);
$upcoming_stmt->execute();
$upcoming_result = $upcoming_stmt->get_result();
if ($upcoming_result->num_rows > 0) {
    while ($row = $upcoming_result->fetch_assoc()) {
        $upcoming_appointments[] = $row;
    }
}

// Get appointment statistics
$total_appointments = 0;
$completed_appointments = 0;
$pending_appointments = 0;
$cancelled_appointments = 0;

// Total appointments
$total_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ?";
$total_stmt = $conn->prepare($total_query);
$total_stmt->bind_param("i", $doctor_id);
$total_stmt->execute();
$total_result = $total_stmt->get_result();
if ($total_result->num_rows > 0) {
    $total_appointments = $total_result->fetch_assoc()['count'];
}

// Completed appointments
$completed_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND status = 'completed'";
$completed_stmt = $conn->prepare($completed_query);
$completed_stmt->bind_param("i", $doctor_id);
$completed_stmt->execute();
$completed_result = $completed_stmt->get_result();
if ($completed_result->num_rows > 0) {
    $completed_appointments = $completed_result->fetch_assoc()['count'];
}

// Pending appointments
$pending_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND (status = 'pending' OR status = 'confirmed')";
$pending_stmt = $conn->prepare($pending_query);
$pending_stmt->bind_param("i", $doctor_id);
$pending_stmt->execute();
$pending_result = $pending_stmt->get_result();
if ($pending_result->num_rows > 0) {
    $pending_appointments = $pending_result->fetch_assoc()['count'];
}

// Cancelled appointments
$cancelled_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND (status = 'cancelled' OR status = 'no_show')";
$cancelled_stmt = $conn->prepare($cancelled_query);
$cancelled_stmt->bind_param("i", $doctor_id);
$cancelled_stmt->execute();
$cancelled_result = $cancelled_stmt->get_result();
if ($cancelled_result->num_rows > 0) {
    $cancelled_appointments = $cancelled_result->fetch_assoc()['count'];
}

// Get recent patients
$recent_patients = [];
$recent_query = "SELECT DISTINCT p.*, MAX(a.appointment_date) as last_visit
                FROM patients p
                JOIN appointments a ON p.patient_id = a.patient_id
                WHERE a.doctor_id = ?
                GROUP BY p.patient_id
                ORDER BY last_visit DESC
                LIMIT 5";
$recent_stmt = $conn->prepare($recent_query);
$recent_stmt->bind_param("i", $doctor_id);
$recent_stmt->execute();
$recent_result = $recent_stmt->get_result();
if ($recent_result->num_rows > 0) {
    while ($row = $recent_result->fetch_assoc()) {
        $recent_patients[] = $row;
    }
}

// Get monthly appointment counts for chart
$monthly_counts = [];
$month_query = "SELECT DATE_FORMAT(appointment_date, '%Y-%m') as month, COUNT(*) as count
               FROM appointments
               WHERE doctor_id = ? AND appointment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
               GROUP BY month
               ORDER BY month";
$month_stmt = $conn->prepare($month_query);
$month_stmt->bind_param("i", $doctor_id);
$month_stmt->execute();
$month_result = $month_stmt->get_result();
if ($month_result->num_rows > 0) {
    while ($row = $month_result->fetch_assoc()) {
        $month_label = date('M Y', strtotime($row['month'] . '-01'));
        $monthly_counts[$month_label] = $row['count'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Dashboard | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-tachometer-alt"></i> Doctor Dashboard</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <!-- Welcome Banner -->
                <div class="welcome-banner">
                    <div class="welcome-content">
                        <h2>Welcome back, Dr. <?php echo $doctor_info['last_name']; ?>!</h2>
                        <p>Here's your schedule and patient information for today.</p>
                    </div>
                    <div class="welcome-actions">
                        <a href="appointments.php" class="btn btn-primary"><i class="fas fa-calendar-check"></i> View All Appointments</a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-card-icon appointment-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_appointments; ?></h3>
                            <p>Total Appointments</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon completed-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $completed_appointments; ?></h3>
                            <p>Completed</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon pending-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $pending_appointments; ?></h3>
                            <p>Pending</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon cancelled-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $cancelled_appointments; ?></h3>
                            <p>Cancelled</p>
                        </div>
                    </div>
                </div>

                <!-- Today's Appointments -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-day"></i> Today's Appointments</h3>
                        <span class="date-badge"><?php echo date('F d, Y'); ?></span>
                    </div>
                    <div class="card-body">
                        <?php if (count($today_appointments) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Time</th>
                                            <th>Patient</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($today_appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td><?php echo $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="update_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="Update">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="add_medical_record.php?appointment_id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="Add Medical Record">
                                                        <i class="fas fa-file-medical"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-day empty-icon"></i>
                                <p>No appointments scheduled for today.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>Monthly Appointments</h3>
                        </div>
                        <div class="chart-body">
                            <canvas id="monthlyAppointmentsChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Appointments -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-alt"></i> Upcoming Appointments</h3>
                        <a href="appointments.php" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (count($upcoming_appointments) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Patient</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td><?php echo $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-alt empty-icon"></i>
                                <p>No upcoming appointments scheduled.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Recent Patients -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-injured"></i> Recent Patients</h3>
                        <a href="patients.php" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (count($recent_patients) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Patient</th>
                                            <th>Gender</th>
                                            <th>Last Visit</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($recent_patients as $patient): ?>
                                            <tr>
                                                <td>
                                                    <div class="user-info">
                                                        <img src="../assets/images/<?php echo $patient['profile_image']; ?>" alt="Patient" class="user-image-sm">
                                                        <span><?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?></span>
                                                    </div>
                                                </td>
                                                <td><?php echo ucfirst($patient['gender']); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($patient['last_visit'])); ?></td>
                                                <td>
                                                    <a href="view_patient.php?id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="medical_records.php?patient_id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="Medical Records">
                                                        <i class="fas fa-file-medical"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-user-injured empty-icon"></i>
                                <p>No patients found.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions-section">
                    <div class="section-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="quick-actions">
                        <a href="add_medical_record.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-file-medical"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Add Medical Record</h4>
                                <p>Create a new medical record</p>
                            </div>
                        </a>

                        <a href="add_prescription.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-prescription"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Write Prescription</h4>
                                <p>Create a new prescription</p>
                            </div>
                        </a>

                        <a href="schedule.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Update Schedule</h4>
                                <p>Manage your availability</p>
                            </div>
                        </a>

                        <a href="profile.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Update Profile</h4>
                                <p>Edit your information</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Chart for Monthly Appointments
        const monthlyCtx = document.getElementById('monthlyAppointmentsChart').getContext('2d');
        const monthlyChart = new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: [
                    <?php
                    foreach ($monthly_counts as $month => $count) {
                        echo "'" . $month . "', ";
                    }
                    ?>
                ],
                datasets: [{
                    label: 'Number of Appointments',
                    data: [
                        <?php
                        foreach ($monthly_counts as $count) {
                            echo $count . ", ";
                        }
                        ?>
                    ],
                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                    borderColor: '#2196F3',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>