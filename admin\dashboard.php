<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: index.php");
    exit();
}

// Get admin information
$admin_id = 0;
$admin_info = [];

$admin_query = "SELECT a.*, u.email, u.last_login
                FROM admins a
                JOIN users u ON a.user_id = u.user_id
                WHERE a.user_id = ?";
$admin_stmt = $conn->prepare($admin_query);
$admin_stmt->bind_param("i", $_SESSION['user_id']);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows > 0) {
    $admin_info = $admin_result->fetch_assoc();
    $admin_id = $admin_info['admin_id'];
}

// Get dashboard statistics
$total_doctors = 0;
$total_patients = 0;
$total_appointments = 0;
$total_departments = 0;

// Count doctors
$doctor_query = "SELECT COUNT(*) as count FROM doctors";
$doctor_result = $conn->query($doctor_query);
if ($doctor_result->num_rows > 0) {
    $total_doctors = $doctor_result->fetch_assoc()['count'];
}

// Count patients
$patient_query = "SELECT COUNT(*) as count FROM patients";
$patient_result = $conn->query($patient_query);
if ($patient_result->num_rows > 0) {
    $total_patients = $patient_result->fetch_assoc()['count'];
}

// Count appointments
$appointment_query = "SELECT COUNT(*) as count FROM appointments";
$appointment_result = $conn->query($appointment_query);
if ($appointment_result->num_rows > 0) {
    $total_appointments = $appointment_result->fetch_assoc()['count'];
}

// Count departments
$department_query = "SELECT COUNT(*) as count FROM departments";
$department_result = $conn->query($department_query);
if ($department_result->num_rows > 0) {
    $total_departments = $department_result->fetch_assoc()['count'];
}

// Get recent appointments
$recent_appointments = [];
$recent_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                dep.department_name
                FROM appointments a
                JOIN patients p ON a.patient_id = p.patient_id
                JOIN doctors d ON a.doctor_id = d.doctor_id
                JOIN departments dep ON a.department_id = dep.department_id
                ORDER BY a.created_at DESC
                LIMIT 5";
$recent_result = $conn->query($recent_query);
if ($recent_result->num_rows > 0) {
    while ($row = $recent_result->fetch_assoc()) {
        $recent_appointments[] = $row;
    }
}

// Get appointment status counts for chart
$status_counts = [];
$status_query = "SELECT status, COUNT(*) as count FROM appointments GROUP BY status";
$status_result = $conn->query($status_query);
if ($status_result->num_rows > 0) {
    while ($row = $status_result->fetch_assoc()) {
        $status_counts[$row['status']] = $row['count'];
    }
}

// Get department appointment counts for chart
$department_counts = [];
$dept_count_query = "SELECT d.department_name, COUNT(a.appointment_id) as count
                    FROM departments d
                    LEFT JOIN appointments a ON d.department_id = a.department_id
                    GROUP BY d.department_id
                    ORDER BY count DESC";
$dept_count_result = $conn->query($dept_count_query);
if ($dept_count_result->num_rows > 0) {
    while ($row = $dept_count_result->fetch_assoc()) {
        $department_counts[$row['department_name']] = $row['count'];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $admin_info['profile_image']; ?>" alt="Admin" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $admin_info['first_name'] . ' ' . $admin_info['last_name']; ?></h4>
                            <p>Administrator</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <!-- Welcome Banner -->
                <div class="welcome-banner">
                    <div class="welcome-content">
                        <h2>Welcome back, <?php echo $admin_info['first_name']; ?>!</h2>
                        <p>Here's what's happening with your hospital today.</p>
                    </div>
                    <div class="welcome-actions">
                        <a href="reports.php" class="btn btn-primary"><i class="fas fa-chart-line"></i> View Reports</a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-card-icon doctor-icon">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_doctors; ?></h3>
                            <p>Doctors</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon patient-icon">
                            <i class="fas fa-user-injured"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_patients; ?></h3>
                            <p>Patients</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon appointment-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_appointments; ?></h3>
                            <p>Appointments</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon department-icon">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_departments; ?></h3>
                            <p>Departments</p>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="charts-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>Appointment Status</h3>
                        </div>
                        <div class="chart-body">
                            <canvas id="appointmentStatusChart"></canvas>
                        </div>
                    </div>

                    <div class="chart-card">
                        <div class="chart-header">
                            <h3>Appointments by Department</h3>
                        </div>
                        <div class="chart-body">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Recent Appointments -->
                <div class="recent-section">
                    <div class="section-header">
                        <h3>Recent Appointments</h3>
                        <a href="appointments.php" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Patient</th>
                                    <th>Doctor</th>
                                    <th>Department</th>
                                    <th>Date & Time</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($recent_appointments) > 0): ?>
                                    <?php foreach ($recent_appointments as $appointment): ?>
                                        <tr>
                                            <td>#<?php echo $appointment['appointment_id']; ?></td>
                                            <td><?php echo $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?></td>
                                            <td>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></td>
                                            <td><?php echo $appointment['department_name']; ?></td>
                                            <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])) . ' at ' . date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                            <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                            <td>
                                                <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="7" class="text-center">No recent appointments found</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="quick-actions-section">
                    <div class="section-header">
                        <h3>Quick Actions</h3>
                    </div>
                    <div class="quick-actions">
                        <a href="add_doctor.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Add Doctor</h4>
                                <p>Register a new doctor</p>
                            </div>
                        </a>

                        <a href="add_patient.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Add Patient</h4>
                                <p>Register a new patient</p>
                            </div>
                        </a>

                        <a href="add_appointment.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-calendar-plus"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Schedule Appointment</h4>
                                <p>Create a new appointment</p>
                            </div>
                        </a>

                        <a href="reports.php" class="quick-action-card">
                            <div class="quick-action-icon">
                                <i class="fas fa-file-medical-alt"></i>
                            </div>
                            <div class="quick-action-text">
                                <h4>Generate Report</h4>
                                <p>Create system reports</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Chart for Appointment Status
        const statusCtx = document.getElementById('appointmentStatusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: [
                    'Pending',
                    'Confirmed',
                    'Completed',
                    'Cancelled'
                ],
                datasets: [{
                    data: [
                        <?php echo isset($status_counts['pending']) ? $status_counts['pending'] : 0; ?>,
                        <?php echo isset($status_counts['confirmed']) ? $status_counts['confirmed'] : 0; ?>,
                        <?php echo isset($status_counts['completed']) ? $status_counts['completed'] : 0; ?>,
                        <?php echo isset($status_counts['cancelled']) ? $status_counts['cancelled'] : 0; ?>
                    ],
                    backgroundColor: [
                        '#FFC107', // Pending - Yellow
                        '#2196F3', // Confirmed - Blue
                        '#4CAF50', // Completed - Green
                        '#F44336'  // Cancelled - Red
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });

        // Chart for Department Appointments
        const deptCtx = document.getElementById('departmentChart').getContext('2d');
        const deptChart = new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: [
                    <?php
                    foreach ($department_counts as $dept => $count) {
                        echo "'" . $dept . "', ";
                    }
                    ?>
                ],
                datasets: [{
                    label: 'Number of Appointments',
                    data: [
                        <?php
                        foreach ($department_counts as $count) {
                            echo $count . ", ";
                        }
                        ?>
                    ],
                    backgroundColor: '#1E88E5',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
    </script>
</body>
</html>