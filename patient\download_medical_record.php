<?php
session_start();
include "../db_connect.php";
require_once('../fpdf/fpdf.php');

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: medical_records.php");
    exit();
}

$record_id = $_GET['id'];

// Get medical record details
$record = null;
$query = "SELECT mr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          d.specialization, d.profile_image as doctor_profile_image,
          a.appointment_date, a.appointment_time, a.appointment_id,
          dep.department_name
          FROM medical_records mr
          JOIN doctors d ON mr.doctor_id = d.doctor_id
          LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
          LEFT JOIN departments dep ON d.department_id = dep.department_id
          WHERE mr.record_id = ? AND mr.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $record_id, $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $record = $result->fetch_assoc();
} else {
    header("Location: medical_records.php");
    exit();
}

// Get hospital settings
$hospital_name = "CSUCC Hospital";
$hospital_address = "123 Main Street, City, Country";
$hospital_phone = "****** 567 890";
$hospital_email = "<EMAIL>";

$settings_query = "SELECT * FROM settings WHERE setting_key IN ('hospital_name', 'hospital_address', 'hospital_phone', 'hospital_email')";
$settings_result = $conn->query($settings_query);
if ($settings_result && $settings_result->num_rows > 0) {
    while ($row = $settings_result->fetch_assoc()) {
        if ($row['setting_key'] == 'hospital_name') {
            $hospital_name = $row['setting_value'];
        } elseif ($row['setting_key'] == 'hospital_address') {
            $hospital_address = $row['setting_value'];
        } elseif ($row['setting_key'] == 'hospital_phone') {
            $hospital_phone = $row['setting_value'];
        } elseif ($row['setting_key'] == 'hospital_email') {
            $hospital_email = $row['setting_value'];
        }
    }
}

// Create PDF
class MedicalRecordPDF extends FPDF {
    public function Header() {
        global $hospital_name, $hospital_address, $hospital_phone, $hospital_email;

        // Logo
        // $this->Image('../assets/images/logo.png', 10, 10, 30);

        // Hospital info
        $this->SetFont('Arial', 'B', 15);
        $this->Cell(0, 10, $hospital_name, 0, 1, 'C');

        $this->SetFont('Arial', '', 10);
        $this->Cell(0, 5, $hospital_address, 0, 1, 'C');
        $this->Cell(0, 5, "Phone: $hospital_phone | Email: $hospital_email", 0, 1, 'C');

        // Title
        $this->Ln(10);
        $this->SetFont('Arial', 'B', 12);
        $this->Cell(0, 10, 'MEDICAL RECORD', 0, 1, 'C');

        // Line break
        $this->Ln(5);
        $this->Line(10, $this->GetY(), 200, $this->GetY());
        $this->Ln(5);
    }

    public function Footer() {
        // Position at 1.5 cm from bottom
        $this->SetY(-15);
        // Arial italic 8
        $this->SetFont('Arial', 'I', 8);
        // Page number
        $this->Cell(0, 10, 'Page ' . $this->PageNo() . '/{nb}', 0, 0, 'C');
    }

    public function SectionTitle($title) {
        $this->SetFont('Arial', 'B', 11);
        $this->Cell(0, 7, $title, 0, 1);
        $this->SetFont('Arial', '', 10);
    }

    public function SectionContent($content) {
        $this->MultiCell(0, 5, $content);
        $this->Ln(3);
    }
}

// Initialize PDF
$pdf = new MedicalRecordPDF();
$pdf->AliasNbPages();
$pdf->AddPage();

// Patient Information
$pdf->SectionTitle('PATIENT INFORMATION:');
$pdf->Cell(40, 6, 'Patient ID:', 0);
$pdf->Cell(60, 6, $patient_info['patient_id'], 0);
$pdf->Cell(40, 6, 'Date of Birth:', 0);
$pdf->Cell(50, 6, date('M d, Y', strtotime($patient_info['date_of_birth'])), 0, 1);

$pdf->Cell(40, 6, 'Name:', 0);
$pdf->Cell(60, 6, $patient_info['first_name'] . ' ' . $patient_info['last_name'], 0);
$pdf->Cell(40, 6, 'Gender:', 0);
$pdf->Cell(50, 6, ucfirst($patient_info['gender']), 0, 1);

$pdf->Cell(40, 6, 'Phone:', 0);
$pdf->Cell(60, 6, $patient_info['phone'], 0);
$pdf->Cell(40, 6, 'Email:', 0);
$pdf->Cell(50, 6, $patient_info['email'], 0, 1);

$pdf->Ln(5);

// Doctor Information
$pdf->SectionTitle('DOCTOR INFORMATION:');
$pdf->Cell(40, 6, 'Doctor:', 0);
$pdf->Cell(60, 6, 'Dr. ' . $record['doctor_first_name'] . ' ' . $record['doctor_last_name'], 0);
$pdf->Cell(40, 6, 'Specialization:', 0);
$pdf->Cell(50, 6, $record['specialization'], 0, 1);

$pdf->Cell(40, 6, 'Department:', 0);
$pdf->Cell(60, 6, $record['department_name'], 0, 1);

$pdf->Ln(5);

// Record Information
$pdf->SectionTitle('RECORD INFORMATION:');
$pdf->Cell(40, 6, 'Record ID:', 0);
$pdf->Cell(60, 6, $record['record_id'], 0);
$pdf->Cell(40, 6, 'Date:', 0);
$pdf->Cell(50, 6, date('M d, Y', strtotime($record['created_at'])), 0, 1);

if (!empty($record['appointment_id'])) {
    $pdf->Cell(40, 6, 'Appointment ID:', 0);
    $pdf->Cell(60, 6, $record['appointment_id'], 0);
    $pdf->Cell(40, 6, 'Appointment Date:', 0);
    $pdf->Cell(50, 6, date('M d, Y', strtotime($record['appointment_date'])), 0, 1);
}

$pdf->Ln(5);

// Medical Details
$pdf->SectionTitle('DIAGNOSIS:');
$pdf->SectionContent($record['diagnosis']);

$pdf->SectionTitle('SYMPTOMS:');
$pdf->SectionContent($record['symptoms']);

$pdf->SectionTitle('TREATMENT PLAN:');
$pdf->SectionContent($record['treatment_plan']);

if (!empty($record['notes'])) {
    $pdf->SectionTitle('ADDITIONAL NOTES:');
    $pdf->SectionContent($record['notes']);
}

if (!empty($record['lab_results'])) {
    $pdf->SectionTitle('LAB RESULTS:');
    $pdf->SectionContent($record['lab_results']);
}

if (!empty($record['vital_signs'])) {
    $pdf->SectionTitle('VITAL SIGNS:');
    $pdf->SectionContent($record['vital_signs']);
}

// Output PDF
$pdf->Output('D', 'Medical_Record_' . $record_id . '.pdf');
exit();
?>
