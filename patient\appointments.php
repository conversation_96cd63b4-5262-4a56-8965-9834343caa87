<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Set default filter values for each section
$upcoming_status_filter = isset($_GET['upcoming_status']) ? $_GET['upcoming_status'] : 'all';
$history_status_filter = isset($_GET['history_status']) ? $_GET['history_status'] : 'all';
$history_date_filter = isset($_GET['history_date']) ? $_GET['history_date'] : 'all';
$medical_month_filter = isset($_GET['medical_month']) ? $_GET['medical_month'] : 'all';

// Build the query for all appointments (we'll filter them in PHP)
$query_conditions = ["a.patient_id = ?"];
$query_params = [$patient_id];
$param_types = "i";

// Get all appointments
$appointments = [];
$query = "SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          dep.department_name
          FROM appointments a
          JOIN doctors d ON a.doctor_id = d.doctor_id
          JOIN departments dep ON a.department_id = dep.department_id
          WHERE " . implode(" AND ", $query_conditions) . "
          ORDER BY a.appointment_date DESC, a.appointment_time DESC";

$stmt = $conn->prepare($query);
$stmt->bind_param($param_types, ...$query_params);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get medical records for the patient with month filter
$medical_records = [];
$records_conditions = ["mr.patient_id = ?"];
$records_params = [$patient_id];
$records_param_types = "i";

// Apply month filter if selected
if ($medical_month_filter != 'all') {
    $records_conditions[] = "MONTH(mr.created_at) = ?";
    $records_params[] = $medical_month_filter;
    $records_param_types .= "i";
}

$records_query = "SELECT mr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                 a.appointment_date, a.appointment_time
                 FROM medical_records mr
                 JOIN doctors d ON mr.doctor_id = d.doctor_id
                 LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
                 WHERE " . implode(" AND ", $records_conditions) . "
                 ORDER BY mr.created_at DESC";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param($records_param_types, ...$records_params);
$records_stmt->execute();
$records_result = $records_stmt->get_result();
if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Appointments | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        html {
            scroll-behavior: smooth;
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
        }

        .filter-container {
            display: flex;
            align-items: center;
        }

        .filter-form {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 0;
        }

        .btn-sm {
            padding: 5px 10px;
            font-size: 12px;
        }

        /* Add some padding to the top of each section to prevent header overlap */
        #upcoming-appointments, #appointment-history, #medical-records {
            scroll-margin-top: 20px;
        }

        .btn-icon.btn-primary {
            background-color: #4a7c59;
            color: white;
        }

        .btn-icon.btn-primary:hover {
            background-color: #3a6c49;
        }

        @media print {
            .sidebar, .header, .action-bar, .filter-container, .card-header {
                display: none;
            }
        }

        .family-history-display {
            line-height: 1.3;
            font-size: 14px;
        }
        .family-history-display p {
            margin: 0.2em 0;
            padding: 0;
        }
        .family-history-display br + br {
            display: none;
        }
        .family-medical-content {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #4a7c59;
        }
        .family-medical-content .medical-section {
            margin-bottom: 12px;
        }
        .family-medical-content .medical-section:last-child {
            margin-bottom: 0;
        }
        .family-medical-content .section-title {
            font-weight: bold;
            color: #4a7c59;
            margin-bottom: 4px;
            font-size: 13px;
            text-transform: uppercase;
        }

        @media (max-width: 768px) {
            .card-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-container {
                margin-top: 10px;
                width: 100%;
            }

            .filter-form {
                flex-direction: column;
                align-items: flex-start;
                width: 100%;
            }

            .form-group {
                margin-bottom: 10px;
                width: 100%;
            }

        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-check"></i> My Appointments</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="action-bar">
                    <div class="actions">
                        <a href="book_appointment.php" class="btn btn-primary"><i class="fas fa-calendar-plus"></i> Book New Appointment</a>
                    </div>
                </div>

                <?php if (isset($_GET['cancelled']) && $_GET['cancelled'] == 1): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> Your appointment has been successfully cancelled.
                    </div>
                <?php endif; ?>

                <!-- Upcoming Appointments -->
                <div class="card" id="upcoming-appointments">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-alt"></i> Upcoming Appointments</h3>
                        <div class="filter-container">
                            <form action="#upcoming-appointments" method="get" class="filter-form">
                                <!-- Preserve other filters -->
                                <input type="hidden" name="history_status" value="<?php echo $history_status_filter; ?>">
                                <input type="hidden" name="history_date" value="<?php echo $history_date_filter; ?>">
                                <input type="hidden" name="medical_month" value="<?php echo $medical_month_filter; ?>">

                                <div class="form-group">
                                    <label for="upcoming_status">Filter by Status:</label>
                                    <select name="upcoming_status" id="upcoming_status" class="filter-select" data-section="upcoming-appointments">
                                        <option value="all" <?php echo $upcoming_status_filter == 'all' ? 'selected' : ''; ?>>All Status</option>
                                        <option value="scheduled" <?php echo $upcoming_status_filter == 'scheduled' ? 'selected' : ''; ?>>Scheduled</option>
                                        <option value="confirmed" <?php echo $upcoming_status_filter == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                        <option value="pending" <?php echo $upcoming_status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    </select>
                                </div>
                                <?php if ($upcoming_status_filter != 'all'): ?>
                                    <a href="?history_status=<?php echo $history_status_filter; ?>&history_date=<?php echo $history_date_filter; ?>&medical_month=<?php echo $medical_month_filter; ?>#upcoming-appointments" class="btn btn-sm btn-secondary">Clear Filter</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php
                        // Filter upcoming appointments
                        $upcoming_appointments = array_filter($appointments, function($appointment) use ($upcoming_status_filter) {
                            $is_upcoming = strtotime($appointment['appointment_date']) >= strtotime(date('Y-m-d'));
                            $status_match = $upcoming_status_filter == 'all' || $appointment['status'] == $upcoming_status_filter;

                            return $is_upcoming &&
                                  ($status_match) &&
                                  in_array($appointment['status'], ['scheduled', 'confirmed', 'pending']);
                        });

                        if (count($upcoming_appointments) > 0):
                        ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Doctor</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></td>
                                                <td><?php echo $appointment['department_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($appointment['status'] == 'scheduled' && strtotime($appointment['appointment_date']) > time()): ?>
                                                        <a href="cancel_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon btn-danger" title="Cancel" onclick="return confirm('Are you sure you want to cancel this appointment?')">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($appointment['status'] == 'confirmed' || $appointment['status'] == 'scheduled'): ?>
                                                        <a href="print_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon btn-primary" title="Print" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-alt empty-icon"></i>
                                <p>No upcoming appointments found.</p>
                                <a href="book_appointment.php" class="btn btn-primary">Book an Appointment</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Appointment History -->
                <div class="card" id="appointment-history">
                    <div class="card-header">
                        <h3><i class="fas fa-history"></i> Appointment History</h3>
                        <div class="filter-container">
                            <form action="#appointment-history" method="get" class="filter-form">
                                <!-- Preserve other filters -->
                                <input type="hidden" name="upcoming_status" value="<?php echo $upcoming_status_filter; ?>">
                                <input type="hidden" name="medical_month" value="<?php echo $medical_month_filter; ?>">

                                <div class="form-group">
                                    <label for="history_status">Status:</label>
                                    <select name="history_status" id="history_status" class="filter-select" data-section="appointment-history">
                                        <option value="all" <?php echo $history_status_filter == 'all' ? 'selected' : ''; ?>>All Status</option>
                                        <option value="completed" <?php echo $history_status_filter == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo $history_status_filter == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="history_date">Date Range:</label>
                                    <select name="history_date" id="history_date" class="filter-select" data-section="appointment-history">
                                        <option value="all" <?php echo $history_date_filter == 'all' ? 'selected' : ''; ?>>All Time</option>
                                        <option value="last_month" <?php echo $history_date_filter == 'last_month' ? 'selected' : ''; ?>>Last Month</option>
                                        <option value="last_3_months" <?php echo $history_date_filter == 'last_3_months' ? 'selected' : ''; ?>>Last 3 Months</option>
                                        <option value="last_year" <?php echo $history_date_filter == 'last_year' ? 'selected' : ''; ?>>Last Year</option>
                                    </select>
                                </div>
                                <?php if ($history_status_filter != 'all' || $history_date_filter != 'all'): ?>
                                    <a href="?upcoming_status=<?php echo $upcoming_status_filter; ?>&medical_month=<?php echo $medical_month_filter; ?>#appointment-history" class="btn btn-sm btn-secondary">Clear Filters</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php
                        // Filter history appointments
                        $history_appointments = array_filter($appointments, function($appointment) use ($history_status_filter, $history_date_filter) {
                            // Status filter
                            $status_match = $history_status_filter == 'all' || $appointment['status'] == $history_status_filter;
                            $is_history = ($appointment['status'] == 'completed' || $appointment['status'] == 'cancelled');

                            // Date filter
                            $date_match = true;
                            $appointment_date = strtotime($appointment['appointment_date']);
                            $now = time();

                            if ($history_date_filter == 'last_month') {
                                $one_month_ago = strtotime('-1 month');
                                $date_match = $appointment_date >= $one_month_ago && $appointment_date <= $now;
                            } elseif ($history_date_filter == 'last_3_months') {
                                $three_months_ago = strtotime('-3 months');
                                $date_match = $appointment_date >= $three_months_ago && $appointment_date <= $now;
                            } elseif ($history_date_filter == 'last_year') {
                                $one_year_ago = strtotime('-1 year');
                                $date_match = $appointment_date >= $one_year_ago && $appointment_date <= $now;
                            }

                            return $is_history && $status_match && $date_match;
                        });

                        if (count($history_appointments) > 0):
                        ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Doctor</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($history_appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></td>
                                                <td><?php echo $appointment['department_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-history empty-icon"></i>
                                <p>No appointment history found.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Medical Records History -->
                <div class="card" id="medical-records">
                    <div class="card-header">
                        <h3><i class="fas fa-file-medical"></i> Medical Records History</h3>
                        <div class="filter-container">
                            <form action="#medical-records" method="get" class="filter-form">
                                <!-- Preserve existing filters if needed -->
                                <input type="hidden" name="upcoming_status" value="<?php echo $upcoming_status_filter; ?>">
                                <input type="hidden" name="history_status" value="<?php echo $history_status_filter; ?>">
                                <input type="hidden" name="history_date" value="<?php echo $history_date_filter; ?>">

                                <div class="form-group">
                                    <label for="medical_month">Filter by Month:</label>
                                    <select name="medical_month" id="medical_month" class="filter-select" data-section="medical-records">
                                        <option value="all" <?php echo $medical_month_filter == 'all' ? 'selected' : ''; ?>>All Months</option>
                                        <option value="1" <?php echo $medical_month_filter == '1' ? 'selected' : ''; ?>>January</option>
                                        <option value="2" <?php echo $medical_month_filter == '2' ? 'selected' : ''; ?>>February</option>
                                        <option value="3" <?php echo $medical_month_filter == '3' ? 'selected' : ''; ?>>March</option>
                                        <option value="4" <?php echo $medical_month_filter == '4' ? 'selected' : ''; ?>>April</option>
                                        <option value="5" <?php echo $medical_month_filter == '5' ? 'selected' : ''; ?>>May</option>
                                        <option value="6" <?php echo $medical_month_filter == '6' ? 'selected' : ''; ?>>June</option>
                                        <option value="7" <?php echo $medical_month_filter == '7' ? 'selected' : ''; ?>>July</option>
                                        <option value="8" <?php echo $medical_month_filter == '8' ? 'selected' : ''; ?>>August</option>
                                        <option value="9" <?php echo $medical_month_filter == '9' ? 'selected' : ''; ?>>September</option>
                                        <option value="10" <?php echo $medical_month_filter == '10' ? 'selected' : ''; ?>>October</option>
                                        <option value="11" <?php echo $medical_month_filter == '11' ? 'selected' : ''; ?>>November</option>
                                        <option value="12" <?php echo $medical_month_filter == '12' ? 'selected' : ''; ?>>December</option>
                                    </select>
                                </div>
                                <?php if ($medical_month_filter != 'all'): ?>
                                    <a href="?upcoming_status=<?php echo $upcoming_status_filter; ?>&history_status=<?php echo $history_status_filter; ?>&history_date=<?php echo $history_date_filter; ?>#medical-records" class="btn btn-sm btn-secondary">Clear Filter</a>
                                <?php endif; ?>
                            </form>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($medical_records) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Doctor</th>
                                            <th>Diagnosis</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($medical_records as $record): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                <td>Dr. <?php echo $record['doctor_first_name'] . ' ' . $record['doctor_last_name']; ?></td>
                                                <td><?php echo substr($record['diagnosis'], 0, 50) . (strlen($record['diagnosis']) > 50 ? '...' : ''); ?></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $record['appointment_id']; ?>" class="btn-icon" title="View Appointment">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-file-medical empty-icon"></i>
                                <p>No medical records found.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Family Medical Record Section -->
                <div class="card" id="family-medical-record">
                    <div class="card-header">
                        <h3><i class="fas fa-dna"></i> Family Medical Record</h3>
                    </div>
                    <div class="card-body">
                        <?php if (isset($patient_info['medical_history']) && !empty($patient_info['medical_history'])): ?>
                            <div class="family-medical-content">
                                <div class="family-history-display">
                                    <?php echo nl2br(htmlspecialchars($patient_info['medical_history'])); ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-dna empty-icon"></i>
                                <p>No family medical history recorded.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Add event listeners to all filter selects
        document.addEventListener('DOMContentLoaded', function() {
            const filterSelects = document.querySelectorAll('.filter-select');

            filterSelects.forEach(select => {
                select.addEventListener('change', function() {
                    // Get the section ID this filter belongs to
                    const sectionId = this.getAttribute('data-section');

                    // Submit the form
                    this.form.submit();

                    // Store the current scroll position in session storage
                    sessionStorage.setItem('scrollPosition', window.scrollY);
                    sessionStorage.setItem('activeSection', sectionId);
                });
            });

            // Restore scroll position if available
            if (sessionStorage.getItem('scrollPosition') && sessionStorage.getItem('activeSection')) {
                const section = document.getElementById(sessionStorage.getItem('activeSection'));
                if (section) {
                    // Scroll to the section after a short delay to ensure page is fully loaded
                    setTimeout(() => {
                        section.scrollIntoView();

                        // Clear the stored position
                        sessionStorage.removeItem('scrollPosition');
                        sessionStorage.removeItem('activeSection');
                    }, 100);
                }
            }

            // If there's a hash in the URL, scroll to that section
            if (window.location.hash) {
                const section = document.querySelector(window.location.hash);
                if (section) {
                    setTimeout(() => {
                        section.scrollIntoView();
                    }, 100);
                }
            }
        });
    </script>
</body>
</html>
