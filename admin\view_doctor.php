<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if doctor ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: doctors.php");
    exit();
}

$doctor_id = $_GET['id'];

// Get doctor information
$query = "SELECT d.*, u.username, u.email, u.last_login, dep.department_name
          FROM doctors d
          JOIN users u ON d.user_id = u.user_id
          LEFT JOIN departments dep ON d.department_id = dep.department_id
          WHERE d.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: doctors.php");
    exit();
}

$doctor = $result->fetch_assoc();

// Get appointments for this doctor
$appointments = [];
$appointments_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                      dep.department_name
                      FROM appointments a
                      JOIN patients p ON a.patient_id = p.patient_id
                      LEFT JOIN departments dep ON a.department_id = dep.department_id
                      WHERE a.doctor_id = ?
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC
                      LIMIT 10";
$appointments_stmt = $conn->prepare($appointments_query);
$appointments_stmt->bind_param("i", $doctor_id);
$appointments_stmt->execute();
$appointments_result = $appointments_stmt->get_result();

if ($appointments_result->num_rows > 0) {
    while ($row = $appointments_result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get appointment statistics
$appointment_stats = [];
$stats_query = "SELECT status, COUNT(*) as count
               FROM appointments
               WHERE doctor_id = ?
               GROUP BY status";
$stats_stmt = $conn->prepare($stats_query);
$stats_stmt->bind_param("i", $doctor_id);
$stats_stmt->execute();
$stats_result = $stats_stmt->get_result();

if ($stats_result->num_rows > 0) {
    while ($row = $stats_result->fetch_assoc()) {
        $appointment_stats[$row['status']] = $row['count'];
    }
}

// Calculate total appointments
$total_appointments = array_sum($appointment_stats);

// Get available days as array
$available_days_array = !empty($doctor['available_days']) ? explode(',', $doctor['available_days']) : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Doctor Details | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li class="active">
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-md"></i> Doctor Details</h2>
                </div>
                <div class="header-right">
                    <a href="edit_doctor.php?id=<?php echo $doctor_id; ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Doctor
                    </a>
                    <a href="doctors.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Doctors
                    </a>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body profile-card">
                                <div class="profile-image">
                                    <img src="../assets/images/<?php echo $doctor['profile_image']; ?>" alt="Doctor">
                                </div>
                                <div class="profile-info">
                                    <h3>Dr. <?php echo htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']); ?></h3>
                                    <p class="text-muted"><?php echo htmlspecialchars($doctor['specialization']); ?></p>
                                    <p class="department"><?php echo htmlspecialchars($doctor['department_name'] ?? 'Not Assigned'); ?></p>
                                    <p class="status-badge status-<?php echo strtolower($doctor['status']); ?>"><?php echo ucfirst($doctor['status']); ?></p>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Contact Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="info-item">
                                    <i class="fas fa-envelope"></i>
                                    <div>
                                        <label>Email</label>
                                        <p><?php echo htmlspecialchars($doctor['email']); ?></p>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <i class="fas fa-phone"></i>
                                    <div>
                                        <label>Phone</label>
                                        <p><?php echo htmlspecialchars($doctor['phone']); ?></p>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <div>
                                        <label>Address</label>
                                        <p><?php echo htmlspecialchars($doctor['address']); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Appointment Statistics</h3>
                            </div>
                            <div class="card-body">
                                <div class="stat-item">
                                    <div class="stat-label">Total Appointments</div>
                                    <div class="stat-value"><?php echo $total_appointments; ?></div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-label">Pending</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['pending']) ? $appointment_stats['pending'] : 0; ?></div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-label">Confirmed</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['confirmed']) ? $appointment_stats['confirmed'] : 0; ?></div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-label">Completed</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['completed']) ? $appointment_stats['completed'] : 0; ?></div>
                                </div>

                                <div class="stat-item">
                                    <div class="stat-label">Cancelled</div>
                                    <div class="stat-value"><?php echo isset($appointment_stats['cancelled']) ? $appointment_stats['cancelled'] : 0; ?></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Professional Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Specialization:</label>
                                            <p><?php echo htmlspecialchars($doctor['specialization']); ?></p>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Qualification:</label>
                                            <p><?php echo htmlspecialchars($doctor['qualification']); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Experience:</label>
                                            <p><?php echo $doctor['experience'] ? $doctor['experience'] . ' years' : 'Not specified'; ?></p>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Consultation Fee:</label>
                                            <p>$<?php echo number_format($doctor['consultation_fee'], 2); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <div class="info-group">
                                    <label>Available Days:</label>
                                    <p>
                                        <?php
                                        if (!empty($available_days_array)) {
                                            echo implode(', ', $available_days_array);
                                        } else {
                                            echo 'Not specified';
                                        }
                                        ?>
                                    </p>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Available Time:</label>
                                            <p>
                                                <?php
                                                if ($doctor['available_time_start'] && $doctor['available_time_end']) {
                                                    echo date('h:i A', strtotime($doctor['available_time_start'])) . ' - ' . date('h:i A', strtotime($doctor['available_time_end']));
                                                } else {
                                                    echo 'Not specified';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="info-group">
                                            <label>Last Login:</label>
                                            <p>
                                                <?php
                                                if ($doctor['last_login']) {
                                                    echo date('M d, Y h:i A', strtotime($doctor['last_login']));
                                                } else {
                                                    echo 'Never';
                                                }
                                                ?>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-header">
                                <h3>Recent Appointments</h3>
                                <a href="appointments.php?doctor_id=<?php echo $doctor_id; ?>" class="btn btn-sm btn-outline">View All</a>
                            </div>
                            <div class="card-body">
                                <?php if (count($appointments) > 0): ?>
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>ID</th>
                                                    <th>Patient</th>
                                                    <th>Date & Time</th>
                                                    <th>Status</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($appointments as $appointment): ?>
                                                    <tr>
                                                        <td>#<?php echo $appointment['appointment_id']; ?></td>
                                                        <td><?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?></td>
                                                        <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])) . ' at ' . date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                        <td>
                                                            <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                                                <?php echo ucfirst($appointment['status']); ?>
                                                            </span>
                                                        </td>
                                                        <td>
                                                            <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                <?php else: ?>
                                    <div class="empty-state">
                                        <i class="fas fa-calendar-check empty-icon"></i>
                                        <p>No appointments found for this doctor</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>