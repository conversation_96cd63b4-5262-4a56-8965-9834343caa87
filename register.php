<?php
session_start();
include "db_connect.php";

// Check if user is already logged in
if (isset($_SESSION['user_id'])) {
    // Redirect based on role
    switch ($_SESSION['role']) {
        case 'admin':
            header("Location: admin/dashboard.php");
            break;
        case 'doctor':
            header("Location: doctor/dashboard.php");
            break;
        case 'patient':
            header("Location: patient/dashboard.php");
            break;
    }
    exit();
}

$error = "";
$success = "";

// Get departments for doctor registration
$departments = [];
$dept_result = $conn->query("SELECT * FROM departments WHERE status = 'active' ORDER BY department_name");
if ($dept_result->num_rows > 0) {
    while ($row = $dept_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Process registration form
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['register'])) {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $role = $_POST['role'];

    // Basic validation
    if (empty($username) || empty($email) || empty($password) || empty($role)) {
        $error = "All fields are required";
    } elseif ($password !== $confirm_password) {
        $error = "Passwords do not match";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } elseif ($role === 'patient') {
        // Additional validation for patient fields
        if (empty($_POST['first_name']) || empty($_POST['last_name']) || empty($_POST['date_of_birth']) ||
            empty($_POST['gender']) || empty($_POST['phone']) || empty($_POST['blood_group']) ||
            empty($_POST['address']) || empty($_POST['allergies']) ||
            empty($_POST['current_medications']) || empty($_POST['medical_history'])) {
            $error = "All patient information fields are required";
        }
    } else {
        // Check if username or email already exists
        $check_stmt = $conn->prepare("SELECT * FROM users WHERE username = ? OR email = ?");
        $check_stmt->bind_param("ss", $username, $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Username or email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);

                // Insert into users table
                $user_stmt = $conn->prepare("INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, ?)");
                $user_stmt->bind_param("ssss", $username, $hashed_password, $email, $role);
                $user_stmt->execute();

                // Get the new user ID
                $user_id = $conn->insert_id;

                // Insert into role-specific table
                switch ($role) {
                    case 'admin':
                        $admin_stmt = $conn->prepare("INSERT INTO admins (user_id, first_name, last_name, phone) VALUES (?, ?, ?, ?)");
                        $first_name = $_POST['first_name'];
                        $last_name = $_POST['last_name'];
                        $phone = $_POST['phone'];
                        $admin_stmt->bind_param("isss", $user_id, $first_name, $last_name, $phone);
                        $admin_stmt->execute();
                        break;

                    case 'doctor':
                        // Check if department exists before inserting
                        $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : NULL;

                        if (!empty($department_id)) {
                            // Verify department exists
                            $dept_check = $conn->prepare("SELECT department_id FROM departments WHERE department_id = ?");
                            $dept_check->bind_param("i", $department_id);
                            $dept_check->execute();
                            $dept_result = $dept_check->get_result();

                            if ($dept_result->num_rows == 0) {
                                // Department doesn't exist, set to NULL
                                $department_id = NULL;
                            }
                        }

                        $doctor_stmt = $conn->prepare("INSERT INTO doctors (user_id, department_id, first_name, last_name, specialization, phone) VALUES (?, ?, ?, ?, ?, ?)");
                        $first_name = $_POST['first_name'];
                        $last_name = $_POST['last_name'];
                        $specialization = $_POST['specialization'];
                        $phone = $_POST['phone'];
                        $doctor_stmt->bind_param("iissss", $user_id, $department_id, $first_name, $last_name, $specialization, $phone);
                        $doctor_stmt->execute();
                        break;

                    case 'patient':
                        $patient_stmt = $conn->prepare("INSERT INTO patients (user_id, first_name, last_name, date_of_birth, gender, phone, blood_group, address, allergies, current_medications, medical_history) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                        $first_name = $_POST['first_name'];
                        $last_name = $_POST['last_name'];
                        $date_of_birth = !empty($_POST['date_of_birth']) ? $_POST['date_of_birth'] : NULL;
                        $gender = $_POST['gender'];
                        $phone = $_POST['phone'];
                        $blood_group = $_POST['blood_group'];
                        $address = $_POST['address'];
                        $allergies = $_POST['allergies'];
                        $current_medications = $_POST['current_medications'];
                        $medical_history = $_POST['medical_history'];
                        $patient_stmt->bind_param("issssssssss", $user_id, $first_name, $last_name, $date_of_birth, $gender, $phone, $blood_group, $address, $allergies, $current_medications, $medical_history);
                        $patient_stmt->execute();
                        break;
                }

                // Commit transaction
                $conn->commit();

                $success = "Registration successful! You can now login.";
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Registration failed: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register | CSUCC Hospital</title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .required {
            color: #e74c3c;
            margin-left: 3px;
        }
        textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }
    </style>
    <script>
        // Function to show/hide role-specific fields and set required attributes
        function toggleRoleFields() {
            const role = document.getElementById('role').value;
            document.getElementById('admin-fields').style.display = 'none';
            document.getElementById('doctor-fields').style.display = 'none';
            document.getElementById('patient-fields').style.display = 'none';

            // Remove required attribute from all role-specific fields
            document.querySelectorAll('#admin-fields input, #admin-fields select, #admin-fields textarea').forEach(el => {
                el.removeAttribute('required');
            });
            document.querySelectorAll('#doctor-fields input, #doctor-fields select, #doctor-fields textarea').forEach(el => {
                el.removeAttribute('required');
            });
            document.querySelectorAll('#patient-fields input, #patient-fields select, #patient-fields textarea').forEach(el => {
                el.removeAttribute('required');
            });

            if (role) {
                document.getElementById(role + '-fields').style.display = 'block';

                // Add required attribute to visible fields that should be required
                if (role === 'patient') {
                    document.querySelectorAll('#patient-fields input[required], #patient-fields select[required], #patient-fields textarea[required]').forEach(el => {
                        el.setAttribute('required', 'required');
                    });
                } else if (role === 'doctor') {
                    document.querySelectorAll('#doctor-fields input[required], #doctor-fields select[required]').forEach(el => {
                        el.setAttribute('required', 'required');
                    });
                } else if (role === 'admin') {
                    document.querySelectorAll('#admin-fields input[required], #admin-fields select[required]').forEach(el => {
                        el.setAttribute('required', 'required');
                    });
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            toggleRoleFields();
        });
    </script>
</head>
<body>
    <div class="container">
        <div class="register-container">
            <div class="register-header">
                <h2><i class="fas fa-hospital"></i> CSUCC Hospital</h2>
                <p>Create a new account</p>
            </div>

            <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                </div>
            <?php endif; ?>

            <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    <p>Redirecting to login page in 5 seconds...</p>
                    <script>
                        setTimeout(function() {
                            window.location.href = "index.php";
                        }, 5000);
                    </script>
                </div>
            <?php endif; ?>

            <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="register-form">
                <div class="form-group">
                    <label for="role"><i class="fas fa-user-tag"></i> Register as</label>
                    <select id="role" name="role" required onchange="toggleRoleFields()">
                        <option value="">Select Role</option>
                        <option value="admin">Administrator</option>
                        <option value="doctor">Doctor</option>
                        <option value="patient">Patient</option>
                    </select>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="username"><i class="fas fa-user"></i> Username</label>
                        <input type="text" id="username" name="username" placeholder="Choose a username" required>
                    </div>

                    <div class="form-group">
                        <label for="email"><i class="fas fa-envelope"></i> Email</label>
                        <input type="email" id="email" name="email" placeholder="Enter your email" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="password"><i class="fas fa-lock"></i> Password</label>
                        <input type="password" id="password" name="password" placeholder="Create a password" required>
                    </div>

                    <div class="form-group">
                        <label for="confirm_password"><i class="fas fa-lock"></i> Confirm Password</label>
                        <input type="password" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                    </div>
                </div>

                <!-- Admin-specific fields -->
                <div id="admin-fields" style="display: none;">
                    <h3>Administrator Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="admin_first_name"><i class="fas fa-user"></i> First Name</label>
                            <input type="text" id="admin_first_name" name="first_name" placeholder="Enter first name">
                        </div>

                        <div class="form-group">
                            <label for="admin_last_name"><i class="fas fa-user"></i> Last Name</label>
                            <input type="text" id="admin_last_name" name="last_name" placeholder="Enter last name">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="admin_phone"><i class="fas fa-phone"></i> Phone Number</label>
                        <input type="text" id="admin_phone" name="phone" placeholder="Enter phone number">
                    </div>
                </div>

                <!-- Doctor-specific fields -->
                <div id="doctor-fields" style="display: none;">
                    <h3>Doctor Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="doctor_first_name"><i class="fas fa-user-md"></i> First Name</label>
                            <input type="text" id="doctor_first_name" name="first_name" placeholder="Enter first name">
                        </div>

                        <div class="form-group">
                            <label for="doctor_last_name"><i class="fas fa-user-md"></i> Last Name</label>
                            <input type="text" id="doctor_last_name" name="last_name" placeholder="Enter last name">
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="department_id"><i class="fas fa-hospital"></i> Department</label>
                            <select id="department_id" name="department_id">
                                <option value="">Select Department</option>
                                <?php foreach ($departments as $dept): ?>
                                    <option value="<?php echo $dept['department_id']; ?>"><?php echo htmlspecialchars($dept['department_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="specialization"><i class="fas fa-stethoscope"></i> Specialization</label>
                            <input type="text" id="specialization" name="specialization" placeholder="Enter specialization">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="doctor_phone"><i class="fas fa-phone"></i> Phone Number</label>
                        <input type="text" id="doctor_phone" name="phone" placeholder="Enter phone number">
                    </div>
                </div>

                <!-- Patient-specific fields -->
                <div id="patient-fields" style="display: none;">
                    <h3>Patient Information</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="patient_first_name"><i class="fas fa-user"></i> First Name <span class="required">*</span></label>
                            <input type="text" id="patient_first_name" name="first_name" placeholder="Enter first name" required>
                        </div>

                        <div class="form-group">
                            <label for="patient_last_name"><i class="fas fa-user"></i> Last Name <span class="required">*</span></label>
                            <input type="text" id="patient_last_name" name="last_name" placeholder="Enter last name" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="date_of_birth"><i class="fas fa-calendar"></i> Date of Birth <span class="required">*</span></label>
                            <input type="date" id="date_of_birth" name="date_of_birth" required>
                        </div>

                        <div class="form-group">
                            <label for="gender"><i class="fas fa-venus-mars"></i> Gender <span class="required">*</span></label>
                            <select id="gender" name="gender" required>
                                <option value="">Select Gender</option>
                                <option value="male">Male</option>
                                <option value="female">Female</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="patient_phone"><i class="fas fa-phone"></i> Phone Number <span class="required">*</span></label>
                            <input type="text" id="patient_phone" name="phone" placeholder="Enter phone number" required>
                        </div>

                        <div class="form-group">
                            <label for="blood_group"><i class="fas fa-tint"></i> Blood Group <span class="required">*</span></label>
                            <select id="blood_group" name="blood_group" required>
                                <option value="">Select Blood Group</option>
                                <option value="A+">A+</option>
                                <option value="A-">A-</option>
                                <option value="B+">B+</option>
                                <option value="B-">B-</option>
                                <option value="AB+">AB+</option>
                                <option value="AB-">AB-</option>
                                <option value="O+">O+</option>
                                <option value="O-">O-</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="address"><i class="fas fa-map-marker-alt"></i> Address <span class="required">*</span></label>
                        <textarea id="address" name="address" rows="2" placeholder="Enter your address" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="allergies"><i class="fas fa-exclamation-triangle"></i> Allergies <span class="required">*</span></label>
                        <textarea id="allergies" name="allergies" rows="2" placeholder="List any allergies (or type 'None' if none)" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="current_medications"><i class="fas fa-pills"></i> Current Medications <span class="required">*</span></label>
                        <textarea id="current_medications" name="current_medications" rows="2" placeholder="List any current medications (or type 'None' if none)" required></textarea>
                    </div>

                </div>

                <div class="form-group">
                    <button type="submit" name="register" class="btn btn-primary btn-block">
                        <i class="fas fa-user-plus"></i> Register
                    </button>
                </div>

                <div class="form-footer">
                    <p>Already have an account? <a href="index.php">Login</a></p>
                </div>
            </form>
        </div>
    </div>

    <footer class="footer">
        <p>&copy; <?php echo date('Y'); ?> CSUCC Hospital. All rights reserved.</p>
    </footer>
</body>
</html>