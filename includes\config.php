<?php
/**
 * Configuration file for the Hospital Management System
 * Contains email settings and other system configurations
 */

// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
define('SMTP_AUTH', true);
define('SMTP_USERNAME', '<EMAIL>'); // Sender email
define('SMTP_PASSWORD', 'dieivjpbildcybvi'); // New app password without spaces
define('SMTP_FROM_EMAIL', '<EMAIL>'); // Sender email
define('SMTP_FROM_NAME', 'CSUCC Hospital');

// System Configuration
define('SITE_NAME', 'CSUCC Hospital Management System');
define('SITE_URL', 'http://localhost/hospital_management_system');

// Appointment Configuration
define('APPOINTMENT_CONFIRMATION_ENABLED', true);
define('APPOINTMENT_REMINDER_ENABLED', true);
define('APPOINTMENT_REMINDER_HOURS', 24); // Send reminder 24 hours before appointment

// Prescription Configuration
define('PRESCRIPTION_NOTIFICATION_ENABLED', true);

// File Upload Configuration
define('MAX_UPLOAD_SIZE', 2 * 1024 * 1024); // 2MB
define('ALLOWED_IMAGE_TYPES', ['image/jpeg', 'image/png', 'image/gif']);
define('UPLOAD_PATH', '../assets/images/');

// Database Configuration
// Note: Main database connection is in db_connect.php
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'hospital_management_system');

// Session Configuration
define('SESSION_TIMEOUT', 1800); // 30 minutes

// Error Reporting
// Uncomment for production
// error_reporting(0);
// ini_set('display_errors', 0);
?>


