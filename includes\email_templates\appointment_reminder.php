<?php
/**
 * Email template for appointment reminder
 * 
 * Available variables:
 * $patient_name - Full name of the patient
 * $doctor_name - Full name of the doctor with Dr. prefix
 * $appointment_date - Formatted date of the appointment
 * $appointment_time - Formatted time of the appointment
 * $appointment_type - Type of appointment (Regular, Follow-up, etc.)
 * $department_name - Name of the department
 * $hospital_name - Name of the hospital
 * $hospital_address - Address of the hospital
 * $hospital_phone - Contact phone number of the hospital
 * $appointment_id - Unique ID of the appointment
 */

// Prevent direct access
if (!defined('SITE_NAME')) {
    exit('Direct script access denied.');
}

// HTML email template
$subject = "Appointment Reminder - $hospital_name";

$message = '
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Reminder</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
        }
        .header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
            background-color: #f9f9f9;
        }
        .appointment-details {
            background-color: white;
            border: 1px solid #ddd;
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .appointment-details h3 {
            margin-top: 0;
            color: #3498db;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .detail-row {
            margin-bottom: 10px;
        }
        .detail-label {
            font-weight: bold;
            color: #555;
        }
        .footer {
            text-align: center;
            padding: 15px;
            font-size: 12px;
            color: #777;
            border-top: 1px solid #ddd;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Appointment Reminder</h1>
    </div>
    
    <div class="content">
        <p>Dear ' . $patient_name . ',</p>
        
        <p>This is a friendly reminder about your upcoming appointment at ' . $hospital_name . ':</p>
        
        <div class="appointment-details">
            <h3>Appointment Information</h3>
            
            <div class="detail-row">
                <span class="detail-label">Appointment ID:</span> ' . $appointment_id . '
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Doctor:</span> ' . $doctor_name . '
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Department:</span> ' . $department_name . '
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Date:</span> ' . $appointment_date . '
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Time:</span> ' . $appointment_time . '
            </div>
            
            <div class="detail-row">
                <span class="detail-label">Type:</span> ' . $appointment_type . '
            </div>
        </div>
        
        <p><strong>Location:</strong><br>
        ' . $hospital_name . '<br>
        ' . $hospital_address . '<br>
        Phone: ' . $hospital_phone . '</p>
        
        <p>Please remember to arrive 15 minutes before your scheduled appointment time. If you need to reschedule or cancel your appointment, please log in to your patient portal or contact us as soon as possible.</p>
        
        <p>You can view and manage your appointments by logging into your account:</p>
        
        <p style="text-align: center;">
            <a href="' . SITE_URL . '/patient/appointments.php" class="button">View Appointment</a>
        </p>
    </div>
    
    <div class="footer">
        <p>This is an automated message, please do not reply to this email.</p>
        <p>&copy; ' . date('Y') . ' ' . $hospital_name . '. All rights reserved.</p>
    </div>
</body>
</html>
';

// Plain text alternative for email clients that don't support HTML
$text_message = "
Dear $patient_name,

This is a friendly reminder about your upcoming appointment at $hospital_name:

APPOINTMENT INFORMATION
----------------------
Appointment ID: $appointment_id
Doctor: $doctor_name
Department: $department_name
Date: $appointment_date
Time: $appointment_time
Type: $appointment_type

LOCATION
--------
$hospital_name
$hospital_address
Phone: $hospital_phone

Please remember to arrive 15 minutes before your scheduled appointment time. If you need to reschedule or cancel your appointment, please log in to your patient portal or contact us as soon as possible.

You can view and manage your appointments by logging into your account at:
" . SITE_URL . "/patient/appointments.php

This is an automated message, please do not reply to this email.

© " . date('Y') . " $hospital_name. All rights reserved.
";
?>
