<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$error = "";
$success = "";

// Get departments for dropdown
$departments = [];
$dept_query = "SELECT * FROM departments WHERE status = 'active' ORDER BY department_name";
$dept_result = $conn->query($dept_query);
if ($dept_result->num_rows > 0) {
    while ($row = $dept_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = trim($_POST['username']);
    $email = trim($_POST['email']);
    $password = $_POST['password'];
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : null;
    $specialization = trim($_POST['specialization']);
    $qualification = trim($_POST['qualification']);
    $experience = !empty($_POST['experience']) ? $_POST['experience'] : null;
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $consultation_fee = !empty($_POST['consultation_fee']) ? $_POST['consultation_fee'] : 0;
    $available_days = !empty($_POST['available_days']) ? implode(',', $_POST['available_days']) : '';
    $available_time_start = !empty($_POST['available_time_start']) ? $_POST['available_time_start'] : null;
    $available_time_end = !empty($_POST['available_time_end']) ? $_POST['available_time_end'] : null;
    $status = $_POST['status'];
    
    // Validate input
    if (empty($username) || empty($email) || empty($password) || empty($first_name) || empty($last_name)) {
        $error = "Required fields cannot be empty";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if username or email already exists
        $check_query = "SELECT * FROM users WHERE username = ? OR email = ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("ss", $username, $email);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $error = "Username or email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Hash password
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                
                // Insert into users table
                $user_query = "INSERT INTO users (username, password, email, role) VALUES (?, ?, ?, 'doctor')";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("sss", $username, $hashed_password, $email);
                $user_stmt->execute();
                
                // Get the new user ID
                $user_id = $conn->insert_id;
                
                // Insert into doctors table
                $doctor_query = "INSERT INTO doctors (user_id, department_id, first_name, last_name, specialization, qualification, experience, phone, address, consultation_fee, available_days, available_time_start, available_time_end, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $doctor_stmt = $conn->prepare($doctor_query);
                $doctor_stmt->bind_param("iisssssssdssss", $user_id, $department_id, $first_name, $last_name, $specialization, $qualification, $experience, $phone, $address, $consultation_fee, $available_days, $available_time_start, $available_time_end, $status);
                $doctor_stmt->execute();
                
                // Commit transaction
                $conn->commit();
                
                $success = "Doctor added successfully";
                // Clear form data
                $username = $email = $first_name = $last_name = $specialization = $qualification = $phone = $address = $available_days = "";
                $department_id = $experience = null;
                $consultation_fee = 0;
                $available_time_start = $available_time_end = null;
                $status = "active";
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Error adding doctor: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Doctor | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li class="active">
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-md"></i> Add Doctor</h2>
                </div>
                <div class="header-right">
                    <a href="doctors.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Doctors
                    </a>
                </div>
            </header>
            
            <div class="content-wrapper">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Doctor Information</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                            <h4>Account Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="username">Username <span class="required">*</span></label>
                                    <input type="text" id="username" name="username" value="<?php echo isset($username) ? htmlspecialchars($username) : ''; ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email <span class="required">*</span></label>
                                    <input type="email" id="email" name="email" value="<?php echo isset($email) ? htmlspecialchars($email) : ''; ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="password">Password <span class="required">*</span></label>
                                <input type="password" id="password" name="password" required>
                            </div>
                            
                            <h4>Personal Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name">First Name <span class="required">*</span></label>
                                    <input type="text" id="first_name" name="first_name" value="<?php echo isset($first_name) ? htmlspecialchars($first_name) : ''; ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="last_name">Last Name <span class="required">*</span></label>
                                    <input type="text" id="last_name" name="last_name" value="<?php echo isset($last_name) ? htmlspecialchars($last_name) : ''; ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="text" id="phone" name="phone" value="<?php echo isset($phone) ? htmlspecialchars($phone) : ''; ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="department_id">Department</label>
                                    <select id="department_id" name="department_id">
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo (isset($department_id) && $department_id == $department['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea id="address" name="address" rows="2"><?php echo isset($address) ? htmlspecialchars($address) : ''; ?></textarea>
                            </div>
                            
                            <h4>Professional Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="specialization">Specialization</label>
                                    <input type="text" id="specialization" name="specialization" value="<?php echo isset($specialization) ? htmlspecialchars($specialization) : ''; ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="qualification">Qualification</label>
                                    <input type="text" id="qualification" name="qualification" value="<?php echo isset($qualification) ? htmlspecialchars($qualification) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="experience">Experience (Years)</label>
                                    <input type="number" id="experience" name="experience" min="0" value="<?php echo isset($experience) ? htmlspecialchars($experience) : ''; ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="consultation_fee">Consultation Fee</label>
                                    <input type="number" id="consultation_fee" name="consultation_fee" min="0" step="0.01" value="<?php echo isset($consultation_fee) ? htmlspecialchars($consultation_fee) : '0.00'; ?>">
                                </div>
                            </div>
                            
                            <h4>Schedule Information</h4>
                            <div class="form-group">
                                <label>Available Days</label>
                                <div class="checkbox-group">
                                    <?php
                                    $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                    $selected_days = isset($available_days) ? explode(',', $available_days) : [];
                                    
                                    foreach ($days as $day) {
                                        $checked = in_array($day, $selected_days) ? 'checked' : '';
                                        echo '<label class="checkbox-label">';
                                        echo '<input type="checkbox" name="available_days[]" value="' . $day . '" ' . $checked . '> ' . $day;
                                        echo '</label>';
                                    }
                                    ?>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="available_time_start">Available Time Start</label>
                                    <input type="time" id="available_time_start" name="available_time_start" value="<?php echo isset($available_time_start) ? htmlspecialchars($available_time_start) : ''; ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="available_time_end">Available Time End</label>
                                    <input type="time" id="available_time_end" name="available_time_end" value="<?php echo isset($available_time_end) ? htmlspecialchars($available_time_end) : ''; ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="active" <?php echo (isset($status) && $status == 'active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo (isset($status) && $status == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="on_leave" <?php echo (isset($status) && $status == 'on_leave') ? 'selected' : ''; ?>>On Leave</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Doctor
                                </button>
                                <a href="doctors.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>