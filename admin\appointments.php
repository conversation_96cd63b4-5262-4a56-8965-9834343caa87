<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Process delete appointment
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $delete_id = $_GET['delete'];

    // Delete appointment
    $delete_query = "DELETE FROM appointments WHERE appointment_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("i", $delete_id);

    if ($delete_stmt->execute()) {
        $success = "Appointment deleted successfully.";
    } else {
        $error = "Error deleting appointment: " . $conn->error;
    }
}

// Process filter
$where_clause = "";
$params = [];
$types = "";

if (isset($_GET['filter'])) {
    $filters = [];

    // Filter by doctor
    if (!empty($_GET['doctor_id'])) {
        $filters[] = "a.doctor_id = ?";
        $params[] = $_GET['doctor_id'];
        $types .= "i";
    }

    // Filter by patient
    if (!empty($_GET['patient_id'])) {
        $filters[] = "a.patient_id = ?";
        $params[] = $_GET['patient_id'];
        $types .= "i";
    }

    // Filter by department
    if (!empty($_GET['department_id'])) {
        $filters[] = "a.department_id = ?";
        $params[] = $_GET['department_id'];
        $types .= "i";
    }

    // Filter by status
    if (!empty($_GET['status'])) {
        $filters[] = "a.status = ?";
        $params[] = $_GET['status'];
        $types .= "s";
    }

    // Filter by date range
    if (!empty($_GET['date_from']) && !empty($_GET['date_to'])) {
        $filters[] = "a.appointment_date BETWEEN ? AND ?";
        $params[] = $_GET['date_from'];
        $params[] = $_GET['date_to'];
        $types .= "ss";
    } elseif (!empty($_GET['date_from'])) {
        $filters[] = "a.appointment_date >= ?";
        $params[] = $_GET['date_from'];
        $types .= "s";
    } elseif (!empty($_GET['date_to'])) {
        $filters[] = "a.appointment_date <= ?";
        $params[] = $_GET['date_to'];
        $types .= "s";
    }

    if (!empty($filters)) {
        $where_clause = " WHERE " . implode(" AND ", $filters);
    }
}

// Get all appointments
$appointments = [];
$query = "SELECT a.*,
          p.first_name as patient_first_name, p.last_name as patient_last_name,
          d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          dep.department_name
          FROM appointments a
          JOIN patients p ON a.patient_id = p.patient_id
          JOIN doctors d ON a.doctor_id = d.doctor_id
          LEFT JOIN departments dep ON a.department_id = dep.department_id
          $where_clause
          ORDER BY a.appointment_date DESC, a.appointment_time DESC";

if (!empty($params)) {
    $stmt = $conn->prepare($query);
    $stmt->bind_param($types, ...$params);
    $stmt->execute();
    $result = $stmt->get_result();
} else {
    $result = $conn->query($query);
}

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get doctors for filter dropdown
$doctors = [];
$doctors_query = "SELECT doctor_id, first_name, last_name FROM doctors ORDER BY last_name, first_name";
$doctors_result = $conn->query($doctors_query);
if ($doctors_result->num_rows > 0) {
    while ($row = $doctors_result->fetch_assoc()) {
        $doctors[] = $row;
    }
}

// Get patients for filter dropdown
$patients = [];
$patients_query = "SELECT patient_id, first_name, last_name FROM patients ORDER BY last_name, first_name";
$patients_result = $conn->query($patients_query);
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Get departments for filter dropdown
$departments = [];
$departments_query = "SELECT department_id, department_name FROM departments ORDER BY department_name";
$departments_result = $conn->query($departments_query);
if ($departments_result->num_rows > 0) {
    while ($row = $departments_result->fetch_assoc()) {
        $departments[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointments | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-check"></i> Appointments</h2>
                </div>
                <div class="header-right">
                    <a href="add_appointment.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Appointment
                    </a>
                </div>
            </header>

            <div class="content-wrapper">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Filter Appointments</h3>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="appointments.php" class="filter-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="doctor_id">Doctor</label>
                                    <select id="doctor_id" name="doctor_id">
                                        <option value="">All Doctors</option>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <option value="<?php echo $doctor['doctor_id']; ?>" <?php echo (isset($_GET['doctor_id']) && $_GET['doctor_id'] == $doctor['doctor_id']) ? 'selected' : ''; ?>>
                                                Dr. <?php echo htmlspecialchars($doctor['first_name'] . ' ' . $doctor['last_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="patient_id">Patient</label>
                                    <select id="patient_id" name="patient_id">
                                        <option value="">All Patients</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>" <?php echo (isset($_GET['patient_id']) && $_GET['patient_id'] == $patient['patient_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="department_id">Department</label>
                                    <select id="department_id" name="department_id">
                                        <option value="">All Departments</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo (isset($_GET['department_id']) && $_GET['department_id'] == $department['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select id="status" name="status">
                                        <option value="">All Status</option>
                                        <option value="pending" <?php echo (isset($_GET['status']) && $_GET['status'] == 'pending') ? 'selected' : ''; ?>>Pending</option>
                                        <option value="confirmed" <?php echo (isset($_GET['status']) && $_GET['status'] == 'confirmed') ? 'selected' : ''; ?>>Confirmed</option>
                                        <option value="completed" <?php echo (isset($_GET['status']) && $_GET['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo (isset($_GET['status']) && $_GET['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="date_from">Date From</label>
                                    <input type="date" id="date_from" name="date_from" value="<?php echo isset($_GET['date_from']) ? $_GET['date_from'] : ''; ?>">
                                </div>

                                <div class="form-group">
                                    <label for="date_to">Date To</label>
                                    <input type="date" id="date_to" name="date_to" value="<?php echo isset($_GET['date_to']) ? $_GET['date_to'] : ''; ?>">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="filter" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filter
                                </button>
                                <a href="appointments.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Clear Filter
                                </a>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>All Appointments</h3>
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="Search appointments...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="appointmentsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Patient</th>
                                        <th>Doctor</th>
                                        <th>Department</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($appointments) > 0): ?>
                                        <?php foreach ($appointments as $appointment): ?>
                                            <tr>
                                                <td>#<?php echo $appointment['appointment_id']; ?></td>
                                                <td><?php echo htmlspecialchars($appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']); ?></td>
                                                <td>Dr. <?php echo htmlspecialchars($appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']); ?></td>
                                                <td><?php echo htmlspecialchars($appointment['department_name']); ?></td>
                                                <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])) . ' at ' . date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                                        <?php echo ucfirst($appointment['status']); ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="appointments.php?delete=<?php echo $appointment['appointment_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this appointment?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No appointments found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('appointmentsTable');
            const rows = table.getElementsByTagName('tr');

            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;

                for (let j = 0; j < cells.length - 1; j++) {
                    const cellText = cells[j].textContent.toLowerCase();
                    if (cellText.includes(searchValue)) {
                        found = true;
                        break;
                    }
                }

                row.style.display = found ? '' : 'none';
            }
        });
    </script>
</body>
</html>