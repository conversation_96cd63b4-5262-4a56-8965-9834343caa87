<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get patients who have had appointments with this doctor
$patients = [];
$query = "SELECT DISTINCT p.*, MAX(a.appointment_date) as last_visit
          FROM patients p
          JOIN appointments a ON p.patient_id = a.patient_id
          WHERE a.doctor_id = ?
          GROUP BY p.patient_id
          ORDER BY last_visit DESC";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $doctor_id);
$stmt->execute();
$result = $stmt->get_result();
if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $patients[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Patients | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> My Patients</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Patient List</h3>
                    <div class="search-group">
                        <input type="text" id="search-input" class="form-control" placeholder="Search patients...">
                        <button id="search-btn" class="btn btn-outline"><i class="fas fa-search"></i></button>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Patient</th>
                                        <th>Gender</th>
                                        <th>Age</th>
                                        <th>Contact</th>
                                        <th>Last Visit</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($patients) > 0): ?>
                                        <?php foreach ($patients as $patient): ?>
                                            <tr>
                                                <td>
                                                    <div class="user-info">
                                                        <img src="../assets/images/<?php echo $patient['profile_image']; ?>" alt="Patient" class="user-image-sm">
                                                        <div>
                                                            <span class="user-name"><?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?></span>
                                                            <span class="user-id">ID: <?php echo $patient['patient_id']; ?></span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td><?php echo ucfirst($patient['gender']); ?></td>
                                                <td><?php echo date_diff(date_create($patient['date_of_birth']), date_create('today'))->y; ?></td>
                                                <td>
                                                    <div class="contact-info">
                                                        <div><i class="fas fa-phone"></i> <?php echo $patient['phone']; ?></div>
                                                        <div><i class="fas fa-envelope"></i> <?php echo $patient['email']; ?></div>
                                                    </div>
                                                </td>
                                                <td><?php echo date('M d, Y', strtotime($patient['last_visit'])); ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="view_patient.php?id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="medical_records.php?patient_id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="Medical Records">
                                                            <i class="fas fa-file-medical"></i>
                                                        </a>
                                                        <a href="add_appointment.php?patient_id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="Schedule Appointment">
                                                            <i class="fas fa-calendar-plus"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No patients found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');
            const tableRows = document.querySelectorAll('tbody tr');

            function performSearch() {
                const searchValue = searchInput.value.toLowerCase();

                tableRows.forEach(row => {
                    const patientName = row.querySelector('.user-name').textContent.toLowerCase();
                    const patientId = row.querySelector('.user-id').textContent.toLowerCase();
                    const contactInfo = row.querySelector('.contact-info').textContent.toLowerCase();

                    if (patientName.includes(searchValue) || patientId.includes(searchValue) || contactInfo.includes(searchValue)) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
    </script>
</body>
</html>
