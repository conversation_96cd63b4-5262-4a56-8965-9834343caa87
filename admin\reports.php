<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Set default date range (last 30 days)
$endDate = date('Y-m-d');
$startDate = date('Y-m-d', strtotime('-30 days'));

// Process filter form
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['filter'])) {
    $startDate = $_POST['start_date'];
    $endDate = $_POST['end_date'];
}

// Get appointment statistics by status
$statusQuery = "SELECT status, COUNT(*) as count 
               FROM appointments 
               WHERE appointment_date BETWEEN ? AND ? 
               GROUP BY status";
$stmt = $conn->prepare($statusQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$statusResult = $stmt->get_result();

$statusLabels = [];
$statusCounts = [];
$totalAppointments = 0;

while ($row = $statusResult->fetch_assoc()) {
    $statusLabels[] = $row['status'];
    $statusCounts[] = $row['count'];
    $totalAppointments += $row['count'];
}

// Get appointment statistics by department
$deptQuery = "SELECT d.department_name as department, COUNT(a.appointment_id) as count 
             FROM appointments a 
             JOIN doctors doc ON a.doctor_id = doc.doctor_id 
             JOIN departments d ON doc.department_id = d.department_id 
             WHERE a.appointment_date BETWEEN ? AND ? 
             GROUP BY d.department_id";
$stmt = $conn->prepare($deptQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$deptResult = $stmt->get_result();

$deptLabels = [];
$deptCounts = [];

while ($row = $deptResult->fetch_assoc()) {
    $deptLabels[] = $row['department'];
    $deptCounts[] = $row['count'];
}

// Get top doctors by appointment count
$doctorQuery = "SELECT d.doctor_id, CONCAT(d.first_name, ' ', d.last_name) as doctor_name, 
                d.specialization, COUNT(a.appointment_id) as appointment_count 
               FROM doctors d 
               JOIN appointments a ON d.doctor_id = a.doctor_id 
               WHERE a.appointment_date BETWEEN ? AND ? 
               GROUP BY d.doctor_id 
               ORDER BY appointment_count DESC 
               LIMIT 5";
$stmt = $conn->prepare($doctorQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$doctorResult = $stmt->get_result();

$topDoctors = [];
while ($row = $doctorResult->fetch_assoc()) {
    $topDoctors[] = $row;
}

// Get daily appointment counts for the selected period
$dailyQuery = "SELECT appointment_date, COUNT(*) as count 
              FROM appointments 
              WHERE appointment_date BETWEEN ? AND ? 
              GROUP BY appointment_date 
              ORDER BY appointment_date";
$stmt = $conn->prepare($dailyQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$dailyResult = $stmt->get_result();

$dailyDates = [];
$dailyCounts = [];

while ($row = $dailyResult->fetch_assoc()) {
    $dailyDates[] = date('M d', strtotime($row['appointment_date']));
    $dailyCounts[] = $row['count'];
}

// Get payment statistics (assuming you have a payment field in appointments)
// If you don't have this field, you can comment out or remove this section
$paymentQuery = "SELECT 
                    CASE 
                        WHEN payment_status IS NULL OR payment_status = '' THEN 'Not Specified'
                        ELSE payment_status 
                    END as payment, 
                    COUNT(*) as count 
                FROM appointments 
                WHERE appointment_date BETWEEN ? AND ? 
                GROUP BY payment";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$paymentResult = $stmt->get_result();

$paymentLabels = [];
$paymentCounts = [];

while ($row = $paymentResult->fetch_assoc()) {
    $paymentLabels[] = $row['payment'];
    $paymentCounts[] = $row['count'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reports | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body>
<div class="dashboard-container">
    <!-- Sidebar -->
    <aside class="sidebar">
        <div class="sidebar-header">
            <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
        </div>
        <nav class="sidebar-nav">
            <ul>
                <li>
                    <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                </li>
                <li>
                    <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                </li>
                <li>
                    <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                </li>
                <li>
                    <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                </li>
                <li>
                    <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                </li>
                <li class="active">
                    <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                </li>
                <li>
                    <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                </li>
                <li>
                    <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                </li>
            </ul>
        </nav>
    </aside>
    
    <!-- Main Content -->
    <main class="main-content">
        <header class="header">
            <div class="header-left">
                <h2><i class="fas fa-chart-bar"></i> Reports</h2>
            </div>
            <div class="header-right">
                <div class="dropdown">
                    <button class="btn btn-primary dropdown-toggle">
                        <i class="fas fa-download"></i> Export
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="dropdown-menu">
                        <a href="export_reports.php?type=csv&start=<?php echo $startDate; ?>&end=<?php echo $endDate; ?>" class="dropdown-item">
                            <i class="fas fa-file-csv"></i> CSV
                        </a>
                        <a href="export_reports.php?type=pdf&start=<?php echo $startDate; ?>&end=<?php echo $endDate; ?>" class="dropdown-item">
                            <i class="fas fa-file-pdf"></i> PDF
                        </a>
                    </div>
                </div>
                <button class="btn btn-outline" onclick="window.print()">
                    <i class="fas fa-print"></i> Print
                </button>
            </div>
        </header>
        
        <div class="content-wrapper">
            <!-- Date Range Filter -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-filter"></i> Filter Reports</h3>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" class="filter-form">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="start_date">Start Date:</label>
                                <input type="date" id="start_date" name="start_date" value="<?php echo $startDate; ?>">
                            </div>
                            
                            <div class="form-group">
                                <label for="end_date">End Date:</label>
                                <input type="date" id="end_date" name="end_date" value="<?php echo $endDate; ?>">
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" name="filter" class="btn btn-primary">
                                    <i class="fas fa-filter"></i> Apply Filter
                                </button>
                                <a href="reports.php" class="btn btn-secondary">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Summary Statistics -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-info-circle"></i> Summary</h3>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-card-icon primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-card-info">
                                <h4><?php echo $totalAppointments; ?></h4>
                                <p>Total Appointments</p>
                            </div>
                        </div>
                        
                        <?php foreach ($statusLabels as $index => $label): ?>
                            <div class="stat-card">
                                <div class="stat-card-icon 
                                    <?php 
                                    if ($label == 'confirmed') echo 'success';
                                    elseif ($label == 'pending') echo 'warning';
                                    elseif ($label == 'cancelled') echo 'danger';
                                    elseif ($label == 'completed') echo 'info';
                                    else echo 'secondary';
                                    ?>">
                                    <?php if ($label == 'confirmed'): ?>
                                        <i class="fas fa-check-circle"></i>
                                    <?php elseif ($label == 'pending'): ?>
                                        <i class="fas fa-clock"></i>
                                    <?php elseif ($label == 'cancelled'): ?>
                                        <i class="fas fa-times-circle"></i>
                                    <?php elseif ($label == 'completed'): ?>
                                        <i class="fas fa-check-double"></i>
                                    <?php else: ?>
                                        <i class="fas fa-question-circle"></i>
                                    <?php endif; ?>
                                </div>
                                <div class="stat-card-info">
                                    <h4><?php echo $statusCounts[$index]; ?></h4>
                                    <p><?php echo ucfirst($label); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
            
            <!-- Charts -->
            <div class="charts-grid">
                <div class="card">
                    <div class="card-header">
                        <h3>Appointment Status Distribution</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Appointments by Department</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="departmentChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Daily Appointment Trends</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="dailyChart"></canvas>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Payment Status</h3>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="paymentChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Top Doctors Table -->
            <div class="card">
                <div class="card-header">
                    <h3><i class="fas fa-user-md"></i> Top Doctors by Appointment Count</h3>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Doctor Name</th>
                                    <th>Specialization</th>
                                    <th>Appointment Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (count($topDoctors) > 0): ?>
                                    <?php foreach ($topDoctors as $doctor): ?>
                                        <tr>
                                            <td>Dr. <?php echo htmlspecialchars($doctor['doctor_name']); ?></td>
                                            <td><?php echo htmlspecialchars($doctor['specialization']); ?></td>
                                            <td><?php echo $doctor['appointment_count']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="3" class="text-center">No data available</td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </main>
</div>

<script>
    // Chart.js initialization
    document.addEventListener('DOMContentLoaded', function() {
        // Appointment Status Chart
        const statusCtx = document.getElementById('statusChart').getContext('2d');
        const statusChart = new Chart(statusCtx, {
            type: 'doughnut',
            data: {
                labels: <?php echo json_encode(array_map('ucfirst', $statusLabels)); ?>,
                datasets: [{
                    data: <?php echo json_encode($statusCounts); ?>,
                    backgroundColor: [
                        '#4CAF50', // Confirmed
                        '#FFC107', // Pending
                        '#F44336', // Cancelled
                        '#2196F3'  // Completed
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
        
        // Department Chart
        const deptCtx = document.getElementById('departmentChart').getContext('2d');
        const deptChart = new Chart(deptCtx, {
            type: 'bar',
            data: {
                labels: <?php echo json_encode($deptLabels); ?>,
                datasets: [{
                    label: 'Number of Appointments',
                    data: <?php echo json_encode($deptCounts); ?>,
                    backgroundColor: '#1e3a56',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Daily Chart
        const dailyCtx = document.getElementById('dailyChart').getContext('2d');
        const dailyChart = new Chart(dailyCtx, {
            type: 'line',
            data: {
                labels: <?php echo json_encode($dailyDates); ?>,
                datasets: [{
                    label: 'Number of Appointments',
                    data: <?php echo json_encode($dailyCounts); ?>,
                    backgroundColor: 'rgba(30, 58, 86, 0.2)',
                    borderColor: '#1e3a56',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });
        
        // Payment Chart
        const paymentCtx = document.getElementById('paymentChart').getContext('2d');
        const paymentChart = new Chart(paymentCtx, {
            type: 'pie',
            data: {
                labels: <?php echo json_encode($paymentLabels); ?>,
                datasets: [{
                    data: <?php echo json_encode($paymentCounts); ?>,
                    backgroundColor: [
                        '#4CAF50', // Paid
                        '#FFC107', // Pending
                        '#2196F3', // Free
                        '#9E9E9E'  // Not Specified
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    });
    
    // Dropdown toggle
    document.addEventListener('DOMContentLoaded', function() {
        const dropdownToggle = document.querySelector('.dropdown-toggle');
        const dropdownMenu = document.querySelector('.dropdown-menu');
        
        if (dropdownToggle && dropdownMenu) {
            dropdownToggle.addEventListener('click', function() {
                dropdownMenu.classList.toggle('show');
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(event) {
                if (!event.target.closest('.dropdown')) {
                    dropdownMenu.classList.remove('show');
                }
            });
        }
    });
</script>

</body>
</html>
