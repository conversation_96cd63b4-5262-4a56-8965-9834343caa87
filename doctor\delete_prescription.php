<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor ID
$doctor_id = 0;
$doctor_query = "SELECT doctor_id FROM doctors WHERE user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_id = $doctor_result->fetch_assoc()['doctor_id'];
}

// Check if prescription ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: prescriptions.php");
    exit();
}

$prescription_id = $_GET['id'];

// Verify that the prescription belongs to this doctor
$check_query = "SELECT * FROM prescriptions WHERE prescription_id = ? AND doctor_id = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("si", $prescription_id, $doctor_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows === 0) {
    // Prescription not found or doesn't belong to this doctor
    header("Location: prescriptions.php?error=permission");
    exit();
}

// Delete prescription medications first
$delete_medications_query = "DELETE FROM prescription_medications WHERE prescription_id = ?";
$delete_medications_stmt = $conn->prepare($delete_medications_query);
$delete_medications_stmt->bind_param("s", $prescription_id);
$delete_medications_result = $delete_medications_stmt->execute();

// Delete prescription
$delete_query = "DELETE FROM prescriptions WHERE prescription_id = ? AND doctor_id = ?";
$delete_stmt = $conn->prepare($delete_query);
$delete_stmt->bind_param("si", $prescription_id, $doctor_id);
$delete_result = $delete_stmt->execute();

if ($delete_result) {
    // Log activity
    $activity_query = "INSERT INTO activity_logs (user_id, activity_type, description, created_at) 
                      VALUES (?, 'prescription', 'Deleted prescription: $prescription_id', NOW())";
    $activity_stmt = $conn->prepare($activity_query);
    $activity_stmt->bind_param("i", $_SESSION['user_id']);
    $activity_stmt->execute();
    
    // Redirect with success message
    header("Location: prescriptions.php?success=deleted");
} else {
    // Redirect with error message
    header("Location: prescriptions.php?error=delete_failed");
}
exit();
?>
