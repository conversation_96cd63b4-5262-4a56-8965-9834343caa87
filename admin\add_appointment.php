<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get admin information
$admin_id = 0;
$admin_info = [];

$admin_query = "SELECT a.*, u.email, u.last_login
                FROM admins a
                JOIN users u ON a.user_id = u.user_id
                WHERE a.user_id = ?";
$admin_stmt = $conn->prepare($admin_query);
$admin_stmt->bind_param("i", $_SESSION['user_id']);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows > 0) {
    $admin_info = $admin_result->fetch_assoc();
    $admin_id = $admin_info['admin_id'];
}

// Get all patients
$patients = [];
$patients_query = "SELECT patient_id, first_name, last_name FROM patients ORDER BY last_name, first_name";
$patients_result = $conn->query($patients_query);
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Get all departments
$departments = [];
$departments_query = "SELECT department_id, department_name FROM departments ORDER BY department_name";
$departments_result = $conn->query($departments_query);
if ($departments_result->num_rows > 0) {
    while ($row = $departments_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Get all doctors with their departments
$doctors = [];
$doctors_query = "SELECT d.doctor_id, d.first_name, d.last_name, d.department_id, dep.department_name
                 FROM doctors d
                 JOIN departments dep ON d.department_id = dep.department_id
                 ORDER BY d.last_name, d.first_name";
$doctors_result = $conn->query($doctors_query);
if ($doctors_result->num_rows > 0) {
    while ($row = $doctors_result->fetch_assoc()) {
        $doctors[] = $row;
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['add_appointment'])) {
    $patient_id = $_POST['patient_id'];
    $doctor_id = $_POST['doctor_id'];
    $department_id = $_POST['department_id'];
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $reason = $_POST['reason'];
    $status = $_POST['status'];

    // Insert appointment
    $insert_query = "INSERT INTO appointments (patient_id, doctor_id, department_id, appointment_date, appointment_time, reason, status, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, NOW())";
    $insert_stmt = $conn->prepare($insert_query);
    $insert_stmt->bind_param("iiissss", $patient_id, $doctor_id, $department_id, $appointment_date, $appointment_time, $reason, $status);

    if ($insert_stmt->execute()) {
        $appointment_id = $conn->insert_id;
        header("Location: view_appointment.php?id=" . $appointment_id . "&created=1");
        exit();
    } else {
        $error = "Error creating appointment: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-plus"></i> Add Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $admin_info['profile_image']; ?>" alt="Admin" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $admin_info['first_name'] . ' ' . $admin_info['last_name']; ?></h4>
                            <p>Administrator</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="card">
                    <div class="card-header">
                        <h3>New Appointment</h3>
                        <div class="card-actions">
                            <a href="appointments.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (isset($error)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <form action="" method="post" class="form">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="patient_id">Patient</label>
                                    <select name="patient_id" id="patient_id" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>">
                                                <?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="department_id">Department</label>
                                    <select name="department_id" id="department_id" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>">
                                                <?php echo $department['department_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="doctor_id">Doctor</label>
                                    <select name="doctor_id" id="doctor_id" required>
                                        <option value="">Select Doctor</option>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <option value="<?php echo $doctor['doctor_id']; ?>" data-department-id="<?php echo $doctor['department_id']; ?>">
                                                Dr. <?php echo $doctor['first_name'] . ' ' . $doctor['last_name']; ?> (<?php echo $doctor['department_name']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="status">Status</label>
                                    <select name="status" id="status" required>
                                        <option value="pending">Pending</option>
                                        <option value="confirmed">Confirmed</option>
                                        <option value="completed">Completed</option>
                                        <option value="cancelled">Cancelled</option>
                                        <option value="no_show">No Show</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="appointment_date">Date</label>
                                    <input type="date" name="appointment_date" id="appointment_date" required>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="appointment_time">Time</label>
                                    <input type="time" name="appointment_time" id="appointment_time" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reason">Reason for Visit</label>
                                <textarea name="reason" id="reason" rows="3" required></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="add_appointment" class="btn btn-primary">Create Appointment</button>
                                <a href="appointments.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Filter doctors based on selected department
        document.getElementById('department_id').addEventListener('change', function() {
            const departmentId = this.value;
            const doctorSelect = document.getElementById('doctor_id');

            // Reset doctor selection
            doctorSelect.value = '';

            // Enable all options first
            for (let i = 0; i < doctorSelect.options.length; i++) {
                const option = doctorSelect.options[i];
                option.style.display = 'block';

                // If this is not the placeholder and department doesn't match, hide it
                if (option.value !== '' && option.getAttribute('data-department-id') !== departmentId) {
                    option.style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>
