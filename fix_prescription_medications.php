<?php
// <PERSON><PERSON><PERSON> to fix the prescription_medications table structure
include "db_connect.php";

echo "<h2>Fixing Prescription Medications Table</h2>";

// Create the prescription_medications table with the correct structure
$create_table_sql = "
CREATE TABLE IF NOT EXISTS prescription_medications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    prescription_id VARCHAR(20) NOT NULL,
    medication_name VARCHAR(255) NOT NULL,
    dosage VARCHAR(100) NOT NULL,
    frequency VARCHAR(100) NOT NULL,
    duration VARCHAR(100) NOT NULL,
    instructions TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

if ($conn->query($create_table_sql)) {
    echo "Table prescription_medications created or already exists.<br>";
} else {
    echo "Error creating table: " . $conn->error . "<br>";
}

// Check if the foreign key constraint exists
$check_fk_sql = "
SELECT COUNT(*) as count FROM information_schema.TABLE_CONSTRAINTS 
WHERE CONSTRAINT_SCHEMA = DATABASE() 
AND TABLE_NAME = 'prescription_medications' 
AND CONSTRAINT_NAME = 'fk_prescription_id'";

$result = $conn->query($check_fk_sql);
$row = $result->fetch_assoc();

// If the foreign key doesn't exist, add it
if ($row['count'] == 0) {
    // First check if the column has the correct type
    $check_column_sql = "
    SELECT COLUMN_TYPE FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'prescription_medications' 
    AND COLUMN_NAME = 'prescription_id'";
    
    $column_result = $conn->query($check_column_sql);
    $column_row = $column_result->fetch_assoc();
    
    // If the column type is not VARCHAR, modify it
    if ($column_row && strpos($column_row['COLUMN_TYPE'], 'varchar') === false) {
        $modify_column_sql = "
        ALTER TABLE prescription_medications 
        MODIFY COLUMN prescription_id VARCHAR(20) NOT NULL";
        
        if ($conn->query($modify_column_sql)) {
            echo "Column prescription_id modified to VARCHAR(20).<br>";
        } else {
            echo "Error modifying column: " . $conn->error . "<br>";
        }
    }
    
    // Now add the foreign key constraint
    $add_fk_sql = "
    ALTER TABLE prescription_medications 
    ADD CONSTRAINT fk_prescription_id 
    FOREIGN KEY (prescription_id) 
    REFERENCES prescriptions(prescription_id) 
    ON DELETE CASCADE";
    
    if ($conn->query($add_fk_sql)) {
        echo "Foreign key constraint added successfully.<br>";
    } else {
        echo "Error adding foreign key constraint: " . $conn->error . "<br>";
    }
} else {
    echo "Foreign key constraint already exists.<br>";
}

echo "<h3>Table fix completed</h3>";
echo "<p><a href='index.php'>Return to Home</a></p>";
?>
