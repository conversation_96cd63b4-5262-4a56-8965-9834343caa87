<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name as department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get patient ID from URL if provided
$patient_id = isset($_GET['patient_id']) ? intval($_GET['patient_id']) : 0;
$patient_info = [];

if ($patient_id > 0) {
    $patient_query = "SELECT * FROM patients WHERE patient_id = ?";
    $patient_stmt = $conn->prepare($patient_query);
    $patient_stmt->bind_param("i", $patient_id);
    $patient_stmt->execute();
    $patient_result = $patient_stmt->get_result();
    if ($patient_result->num_rows > 0) {
        $patient_info = $patient_result->fetch_assoc();
    }
}

// Get all patients for dropdown
$patients = [];
$patients_query = "SELECT p.* FROM patients p
                  JOIN appointments a ON p.patient_id = a.patient_id
                  WHERE a.doctor_id = ?
                  GROUP BY p.patient_id
                  ORDER BY p.last_name, p.first_name";
$patients_stmt = $conn->prepare($patients_query);
$patients_stmt->bind_param("i", $doctor_id);
$patients_stmt->execute();
$patients_result = $patients_stmt->get_result();
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Process form submission
$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $patient_id = isset($_POST['patient_id']) ? intval($_POST['patient_id']) : 0;
    $prescription_date = isset($_POST['prescription_date']) ? $_POST['prescription_date'] : '';
    $medications = isset($_POST['medications']) ? trim($_POST['medications']) : '';
    $dosage = isset($_POST['dosage']) ? trim($_POST['dosage']) : '';
    $frequency = isset($_POST['frequency']) ? trim($_POST['frequency']) : '';
    $duration = isset($_POST['duration']) ? trim($_POST['duration']) : '';
    $instructions = isset($_POST['instructions']) ? trim($_POST['instructions']) : '';
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    $status = 'active'; // Default status

    // Validate required fields
    if ($patient_id === 0) {
        $errors[] = "Please select a patient";
    }

    if (empty($prescription_date)) {
        $errors[] = "Prescription date is required";
    }

    if (empty($medications)) {
        $errors[] = "Medications are required";
    }

    if (empty($dosage)) {
        $errors[] = "Dosage is required";
    }

    if (empty($frequency)) {
        $errors[] = "Frequency is required";
    }

    if (empty($duration)) {
        $errors[] = "Duration is required";
    }

    // If no errors, insert into database
    if (empty($errors)) {
        // Generate prescription ID (format: PRE-YYYYMMDD-XXXX)
        $date_part = date('Ymd');
        $random_part = mt_rand(1000, 9999);
        $prescription_id = "PRE-{$date_part}-{$random_part}";

        // Insert prescription
        $insert_query = "INSERT INTO prescriptions (prescription_id, patient_id, doctor_id, prescription_date,
                        notes, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("siiiss", $prescription_id, $patient_id, $doctor_id, $prescription_date, $notes, $status);

        if ($insert_stmt->execute()) {
            // Get the last inserted prescription ID
            $last_prescription_id = $prescription_id;

            // Process medications (split by new line)
            $medication_list = explode("\n", $medications);
            $success = true;

            foreach ($medication_list as $medication) {
                $medication = trim($medication);
                if (!empty($medication)) {
                    // Insert into prescription_medications table
                    $med_query = "INSERT INTO prescription_medications (prescription_id, medication_name, dosage, frequency, duration, instructions)
                                VALUES (?, ?, ?, ?, ?, ?)";
                    $med_stmt = $conn->prepare($med_query);
                    $med_stmt->bind_param("ssssss", $prescription_id, $medication, $dosage, $frequency, $duration, $instructions);

                    if (!$med_stmt->execute()) {
                        $success = false;
                        $errors[] = "Error adding medication: " . $conn->error;
                        break;
                    }
                }
            }

            if ($success) {
                $success_message = "Prescription added successfully!";

                // Activity logging is commented out until activity_logs table is created
                /*
                $activity_query = "INSERT INTO activity_logs (user_id, activity_type, description, created_at)
                                VALUES (?, 'prescription', 'Added new prescription: $prescription_id', NOW())";
                $activity_stmt = $conn->prepare($activity_query);
                $activity_stmt->bind_param("i", $_SESSION['user_id']);
                $activity_stmt->execute();
                */
            }

            // Clear form data
            $patient_id = 0;
            $prescription_date = '';
            $medications = '';
            $dosage = '';
            $frequency = '';
            $duration = '';
            $instructions = '';
            $notes = '';
        } else {
            $errors[] = "Error adding prescription: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Prescription | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li class="active">
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-prescription"></i> Add New Prescription</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a> /
                    <a href="prescriptions.php">Prescriptions</a> /
                    <span>Add Prescription</span>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert error">
                        <i class="fas fa-exclamation-circle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Prescription Details</h3>
                    </div>
                    <div class="card-body">
                        <form action="add_prescription.php" method="POST">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="patient_id">Patient <span class="required">*</span></label>
                                    <select name="patient_id" id="patient_id" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>" <?php echo ($patient_id == $patient['patient_id']) ? 'selected' : ''; ?>>
                                                <?php echo $patient['last_name'] . ', ' . $patient['first_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="prescription_date">Prescription Date <span class="required">*</span></label>
                                    <input type="date" name="prescription_date" id="prescription_date" value="<?php echo isset($prescription_date) ? $prescription_date : date('Y-m-d'); ?>" required>
                                </div>
                            </div>



                            <div class="form-group">
                                <label for="medications">Medications <span class="required">*</span></label>
                                <textarea name="medications" id="medications" rows="3" required><?php echo isset($medications) ? $medications : ''; ?></textarea>
                                <div class="form-help">Enter each medication on a new line</div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="dosage">Dosage <span class="required">*</span></label>
                                    <input type="text" name="dosage" id="dosage" value="<?php echo isset($dosage) ? $dosage : ''; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="frequency">Frequency <span class="required">*</span></label>
                                    <input type="text" name="frequency" id="frequency" value="<?php echo isset($frequency) ? $frequency : ''; ?>" required>
                                    <div class="form-help">E.g., "3 times a day", "Every 8 hours"</div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="duration">Duration <span class="required">*</span></label>
                                    <input type="text" name="duration" id="duration" value="<?php echo isset($duration) ? $duration : ''; ?>" required>
                                    <div class="form-help">E.g., "7 days", "2 weeks"</div>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="instructions">Instructions</label>
                                <textarea name="instructions" id="instructions" rows="3"><?php echo isset($instructions) ? $instructions : ''; ?></textarea>
                                <div class="form-help">Special instructions for taking medications</div>
                            </div>

                            <div class="form-group">
                                <label for="notes">Additional Notes</label>
                                <textarea name="notes" id="notes" rows="3"><?php echo isset($notes) ? $notes : ''; ?></textarea>
                            </div>

                            <div class="form-actions">
                                <a href="prescriptions.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Prescription</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Auto-populate patient data if coming from patient page
        document.addEventListener('DOMContentLoaded', function() {
            const patientSelect = document.getElementById('patient_id');

            // If patient is pre-selected, trigger any necessary actions
            if (patientSelect.value) {
                // You can add additional functionality here if needed
            }
        });
    </script>
</body>
</html>
