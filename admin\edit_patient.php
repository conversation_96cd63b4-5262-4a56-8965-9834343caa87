<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if patient ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: patients.php");
    exit();
}

$patient_id = $_GET['id'];
$error = "";
$success = "";

// Get patient details
$patient = null;
$query = "SELECT p.*, u.email, u.username
          FROM patients p
          JOIN users u ON p.user_id = u.user_id
          WHERE p.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $patient = $result->fetch_assoc();
} else {
    header("Location: patients.php");
    exit();
}

// Check if medical fields exist in the patients table and add them if they don't
$fields_to_check = ['allergies', 'current_medications', 'medical_history'];
foreach ($fields_to_check as $field) {
    $check_field_query = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
                         WHERE TABLE_SCHEMA = DATABASE()
                         AND TABLE_NAME = 'patients'
                         AND COLUMN_NAME = ?";
    $check_field_stmt = $conn->prepare($check_field_query);
    $check_field_stmt->bind_param("s", $field);
    $check_field_stmt->execute();
    $check_field_result = $check_field_stmt->get_result();
    $field_exists = $check_field_result->fetch_assoc()['count'];

    if ($field_exists == 0) {
        // Add the missing field
        $add_field_query = "ALTER TABLE patients ADD COLUMN $field TEXT DEFAULT NULL";
        $conn->query($add_field_query);
    }
}

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $gender = $_POST['gender'];
    $date_of_birth = $_POST['date_of_birth'];
    $blood_group = $_POST['blood_group'];
    $address = trim($_POST['address']);
    $allergies = trim($_POST['allergies']);
    $current_medications = trim($_POST['current_medications']);
    $medical_history = trim($_POST['medical_history']);

    // Validate input
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error = "First name, last name, and email are required fields";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email already exists (excluding current user)
        $check_query = "SELECT * FROM users WHERE email = ? AND user_id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $patient['user_id']);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Update user email
                $user_query = "UPDATE users SET email = ? WHERE user_id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("si", $email, $patient['user_id']);
                $user_stmt->execute();

                // Update patient information
                $patient_query = "UPDATE patients SET
                                first_name = ?,
                                last_name = ?,
                                gender = ?,
                                date_of_birth = ?,
                                blood_group = ?,
                                phone = ?,
                                address = ?,
                                allergies = ?,
                                current_medications = ?,
                                medical_history = ?
                                WHERE patient_id = ?";
                $patient_stmt = $conn->prepare($patient_query);
                $patient_stmt->bind_param("ssssssssssi",
                                        $first_name,
                                        $last_name,
                                        $gender,
                                        $date_of_birth,
                                        $blood_group,
                                        $phone,
                                        $address,
                                        $allergies,
                                        $current_medications,
                                        $medical_history,
                                        $patient_id);
                $patient_stmt->execute();

                // Handle profile image upload
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    $max_size = 2 * 1024 * 1024; // 2MB

                    if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
                        throw new Exception("Invalid file type. Only JPG, PNG, and GIF are allowed.");
                    }

                    if ($_FILES['profile_image']['size'] > $max_size) {
                        throw new Exception("File size too large. Maximum size is 2MB.");
                    }

                    // Generate unique filename
                    $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'patient_' . $patient_id . '_' . time() . '.' . $file_extension;
                    $upload_path = '../assets/images/' . $new_filename;

                    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                        // Update profile image in database
                        $image_query = "UPDATE patients SET profile_image = ? WHERE patient_id = ?";
                        $image_stmt = $conn->prepare($image_query);
                        $image_stmt->bind_param("si", $new_filename, $patient_id);
                        $image_stmt->execute();
                    } else {
                        throw new Exception("Failed to upload image.");
                    }
                }

                // Commit transaction
                $conn->commit();

                $success = "Patient information updated successfully";

                // Refresh patient data
                $stmt = $conn->prepare($query);
                $stmt->bind_param("i", $patient_id);
                $stmt->execute();
                $result = $stmt->get_result();
                $patient = $result->fetch_assoc();

            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Error updating patient information: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Patient | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> Edit Patient</h2>
                </div>
                <div class="header-right">
                    <a href="view_patient.php?id=<?php echo $patient_id; ?>" class="btn btn-outline">
                        <i class="fas fa-eye"></i> View Patient
                    </a>
                    <a href="patients.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Patients
                    </a>
                </div>
            </header>

            <div class="content-wrapper">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Edit Patient Information</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"] . '?id=' . $patient_id); ?>" enctype="multipart/form-data">
                            <div class="form-section">
                                <h4>Profile Image</h4>
                                <div class="profile-image-upload">
                                    <div class="current-image">
                                        <img src="../assets/images/<?php echo $patient['profile_image'] ?: 'default-patient.jpg'; ?>" alt="Patient" id="profileImagePreview">
                                    </div>
                                    <div class="upload-controls">
                                        <label for="profile_image" class="btn btn-outline">
                                            <i class="fas fa-upload"></i> Upload New Image
                                        </label>
                                        <input type="file" id="profile_image" name="profile_image" accept="image/*" style="display: none;">
                                        <p class="help-text">Allowed formats: JPG, PNG, GIF. Max size: 2MB</p>
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Personal Information</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="first_name">First Name <span class="required">*</span></label>
                                        <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($patient['first_name']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="last_name">Last Name <span class="required">*</span></label>
                                        <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($patient['last_name']); ?>" required>
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="email">Email <span class="required">*</span></label>
                                        <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($patient['email']); ?>" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="phone">Phone Number</label>
                                        <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($patient['phone']); ?>">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="gender">Gender</label>
                                        <select id="gender" name="gender">
                                            <option value="">Select Gender</option>
                                            <option value="male" <?php echo ($patient['gender'] == 'male') ? 'selected' : ''; ?>>Male</option>
                                            <option value="female" <?php echo ($patient['gender'] == 'female') ? 'selected' : ''; ?>>Female</option>
                                            <option value="other" <?php echo ($patient['gender'] == 'other') ? 'selected' : ''; ?>>Other</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="date_of_birth">Date of Birth</label>
                                        <input type="date" id="date_of_birth" name="date_of_birth" value="<?php echo $patient['date_of_birth']; ?>">
                                    </div>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="blood_group">Blood Group</label>
                                        <select id="blood_group" name="blood_group">
                                            <option value="">Select Blood Group</option>
                                            <option value="A+" <?php echo ($patient['blood_group'] == 'A+') ? 'selected' : ''; ?>>A+</option>
                                            <option value="A-" <?php echo ($patient['blood_group'] == 'A-') ? 'selected' : ''; ?>>A-</option>
                                            <option value="B+" <?php echo ($patient['blood_group'] == 'B+') ? 'selected' : ''; ?>>B+</option>
                                            <option value="B-" <?php echo ($patient['blood_group'] == 'B-') ? 'selected' : ''; ?>>B-</option>
                                            <option value="AB+" <?php echo ($patient['blood_group'] == 'AB+') ? 'selected' : ''; ?>>AB+</option>
                                            <option value="AB-" <?php echo ($patient['blood_group'] == 'AB-') ? 'selected' : ''; ?>>AB-</option>
                                            <option value="O+" <?php echo ($patient['blood_group'] == 'O+') ? 'selected' : ''; ?>>O+</option>
                                            <option value="O-" <?php echo ($patient['blood_group'] == 'O-') ? 'selected' : ''; ?>>O-</option>
                                        </select>
                                    </div>

                                    <div class="form-group">
                                        <label for="address">Address</label>
                                        <input type="text" id="address" name="address" value="<?php echo htmlspecialchars($patient['address'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Medical Information</h4>
                                <div class="form-group">
                                    <label for="allergies">Allergies</label>
                                    <textarea id="allergies" name="allergies" rows="2"><?php echo htmlspecialchars($patient['allergies'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="current_medications">Current Medications</label>
                                    <textarea id="current_medications" name="current_medications" rows="2"><?php echo htmlspecialchars($patient['current_medications'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="medical_history">Medical History</label>
                                    <textarea id="medical_history" name="medical_history" rows="3"><?php echo htmlspecialchars($patient['medical_history'] ?? ''); ?></textarea>
                                </div>
                            </div>

                            <div class="form-section">
                                <h4>Account Information</h4>
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input type="text" id="username" value="<?php echo htmlspecialchars($patient['username']); ?>" disabled>
                                    <small class="form-text">Username cannot be changed</small>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Save Changes
                                </button>
                                <a href="view_patient.php?id=<?php echo $patient_id; ?>" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Preview uploaded image
        document.getElementById('profile_image').addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    document.getElementById('profileImagePreview').src = e.target.result;
                }
                reader.readAsDataURL(file);
            }
        });
    </script>
</body>
</html>
