<?php
/**
 * Database Update Script
 * 
 * This script updates the database schema by running the SQL scripts in the database/migrations directory.
 * It adds the necessary columns for email notifications to the appointments and prescriptions tables.
 */

// Include database connection
require_once 'db_connect.php';

echo "<h1>Database Update Script</h1>";

// Function to run SQL file
function runSQLFile($conn, $file_path) {
    echo "<h2>Running SQL file: $file_path</h2>";
    
    if (!file_exists($file_path)) {
        echo "<p style='color: red;'>Error: File not found: $file_path</p>";
        return false;
    }
    
    $sql = file_get_contents($file_path);
    
    // Split SQL file into individual statements
    $statements = explode(';', $sql);
    
    $success = true;
    
    foreach ($statements as $statement) {
        $statement = trim($statement);
        
        if (empty($statement)) {
            continue;
        }
        
        // Skip MySQL variables and prepared statements
        if (strpos($statement, 'SET @') === 0 || 
            strpos($statement, 'PREPARE') === 0 || 
            strpos($statement, 'EXECUTE') === 0 || 
            strpos($statement, 'DEALLOCATE') === 0) {
            continue;
        }
        
        try {
            $result = $conn->query($statement);
            if ($result === false) {
                echo "<p style='color: red;'>Error executing statement: " . $conn->error . "</p>";
                echo "<pre>$statement</pre>";
                $success = false;
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>Exception: " . $e->getMessage() . "</p>";
            echo "<pre>$statement</pre>";
            $success = false;
        }
    }
    
    if ($success) {
        echo "<p style='color: green;'>SQL file executed successfully!</p>";
    } else {
        echo "<p style='color: orange;'>SQL file executed with some errors.</p>";
    }
    
    return $success;
}

// Check if appointments table has reminder_sent column
$check_column_query = "SHOW COLUMNS FROM appointments LIKE 'reminder_sent'";
$check_column_result = $conn->query($check_column_query);
$reminder_column_exists = $check_column_result->num_rows > 0;

if ($reminder_column_exists) {
    echo "<p>The appointments table already has the reminder_sent column.</p>";
} else {
    echo "<p>The appointments table needs to be updated with email notification columns.</p>";
    
    // Run the SQL script to update the appointments table
    $appointments_sql_file = 'database/migrations/update_appointments_table.sql';
    if (!file_exists($appointments_sql_file)) {
        $appointments_sql_file = 'sql/update_appointments_table.sql';
    }
    
    if (file_exists($appointments_sql_file)) {
        // For this specific file, we need to run it directly with mysqli_multi_query
        echo "<h2>Running SQL file: $appointments_sql_file</h2>";
        $sql = file_get_contents($appointments_sql_file);
        
        if ($conn->multi_query($sql)) {
            echo "<p style='color: green;'>SQL file executed successfully!</p>";
            
            // Process all result sets to clear them
            do {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            } while ($conn->more_results() && $conn->next_result());
        } else {
            echo "<p style='color: red;'>Error executing SQL file: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error: SQL file not found: database/migrations/update_appointments_table.sql or sql/update_appointments_table.sql</p>";
    }
}

// Check if prescriptions table has notification_sent column
$check_column_query = "SHOW COLUMNS FROM prescriptions LIKE 'notification_sent'";
$check_column_result = $conn->query($check_column_query);
$notification_column_exists = $check_column_result->num_rows > 0;

if ($notification_column_exists) {
    echo "<p>The prescriptions table already has the notification_sent column.</p>";
} else {
    echo "<p>The prescriptions table needs to be updated with email notification columns.</p>";
    
    // Run the SQL script to update the prescriptions table
    $prescriptions_sql_file = 'database/migrations/update_prescriptions_table.sql';
    if (!file_exists($prescriptions_sql_file)) {
        $prescriptions_sql_file = 'sql/update_prescriptions_table.sql';
    }
    
    if (file_exists($prescriptions_sql_file)) {
        // For this specific file, we need to run it directly with mysqli_multi_query
        echo "<h2>Running SQL file: $prescriptions_sql_file</h2>";
        $sql = file_get_contents($prescriptions_sql_file);
        
        if ($conn->multi_query($sql)) {
            echo "<p style='color: green;'>SQL file executed successfully!</p>";
            
            // Process all result sets to clear them
            do {
                if ($result = $conn->store_result()) {
                    $result->free();
                }
            } while ($conn->more_results() && $conn->next_result());
        } else {
            echo "<p style='color: red;'>Error executing SQL file: " . $conn->error . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Error: SQL file not found: database/migrations/update_prescriptions_table.sql or sql/update_prescriptions_table.sql</p>";
    }
}

// Verify the columns were added
echo "<h2>Verifying Database Updates</h2>";

// Check appointments table columns
$check_columns_query = "DESCRIBE appointments";
$check_columns_result = $conn->query($check_columns_query);

echo "<h3>Appointments Table Columns</h3>";
echo "<ul>";
while ($column = $check_columns_result->fetch_assoc()) {
    $column_name = $column['Field'];
    $column_type = $column['Type'];
    $column_null = $column['Null'];
    $column_default = $column['Default'];
    
    echo "<li><strong>$column_name</strong> - Type: $column_type, Null: $column_null, Default: $column_default</li>";
}
echo "</ul>";

// Check prescriptions table columns
$check_columns_query = "DESCRIBE prescriptions";
$check_columns_result = $conn->query($check_columns_query);

echo "<h3>Prescriptions Table Columns</h3>";
echo "<ul>";
while ($column = $check_columns_result->fetch_assoc()) {
    $column_name = $column['Field'];
    $column_type = $column['Type'];
    $column_null = $column['Null'];
    $column_default = $column['Default'];
    
    echo "<li><strong>$column_name</strong> - Type: $column_type, Null: $column_null, Default: $column_default</li>";
}
echo "</ul>";

echo "<p>Database update completed!</p>";
?>
