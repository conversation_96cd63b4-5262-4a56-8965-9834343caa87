# SQL Scripts for Hospital Management System

This directory contains SQL scripts for updating and maintaining the database structure of the Hospital Management System.

## Available Scripts

1. **create_prescription_medications_table.sql**
   - Creates or updates the prescription_medications table with the correct structure
   - Ensures the prescription_id column is VARCHAR(20) to match the prescriptions table
   - Adds a foreign key constraint to link with the prescriptions table

2. **update_prescriptions_table.sql**
   - Adds notification tracking columns to the prescriptions table
   - Adds indexes for faster queries

3. **update_appointments_table.sql**
   - Adds email notification tracking columns to the appointments table
   - Adds indexes for faster queries

4. **update_patients_table.sql**
   - Updates the patients table structure with additional fields

5. **add_medical_fields.sql**
   - Adds medical fields to the patients table (allergies, current_medications, medical_history)

## How to Run

You can run these scripts in several ways:

1. **Using the PHP Update Scripts**
   - Navigate to the root directory and run `update_prescription_medications.php`
   - This will execute the SQL scripts and update your database

2. **Using phpMyAdmin**
   - Open phpMyAdmin
   - Select your database
   - Go to the SQL tab
   - Copy and paste the content of the SQL file
   - Click "Go" to execute

3. **Using MySQL Command Line**
   - Open MySQL command line
   - Connect to your database
   - Run: `source path/to/script.sql`

## Important Notes

- Always backup your database before running these scripts
- These scripts are designed to be idempotent (can be run multiple times without causing issues)
- The scripts check if tables/columns already exist before attempting to create them
