<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$error = "";
$success = "";

// Check if doctor ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: doctors.php");
    exit();
}

$doctor_id = $_GET['id'];

// Get departments for dropdown
$departments = [];
$dept_query = "SELECT * FROM departments WHERE status = 'active' ORDER BY department_name";
$dept_result = $conn->query($dept_query);
if ($dept_result->num_rows > 0) {
    while ($row = $dept_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Get doctor information
$query = "SELECT d.*, u.username, u.email 
          FROM doctors d 
          JOIN users u ON d.user_id = u.user_id 
          WHERE d.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    header("Location: doctors.php");
    exit();
}

$doctor = $result->fetch_assoc();

// Process form submission
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $department_id = !empty($_POST['department_id']) ? $_POST['department_id'] : null;
    $specialization = trim($_POST['specialization']);
    $qualification = trim($_POST['qualification']);
    $experience = !empty($_POST['experience']) ? $_POST['experience'] : null;
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);
    $consultation_fee = !empty($_POST['consultation_fee']) ? $_POST['consultation_fee'] : 0;
    $available_days = !empty($_POST['available_days']) ? implode(',', $_POST['available_days']) : '';
    $available_time_start = !empty($_POST['available_time_start']) ? $_POST['available_time_start'] : null;
    $available_time_end = !empty($_POST['available_time_end']) ? $_POST['available_time_end'] : null;
    $status = $_POST['status'];
    
    // Validate input
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error = "Required fields cannot be empty";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email already exists (excluding current user)
        $check_query = "SELECT * FROM users WHERE email = ? AND user_id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $doctor['user_id']);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();
        
        if ($check_result->num_rows > 0) {
            $error = "Email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();
            
            try {
                // Update user email
                $user_query = "UPDATE users SET email = ? WHERE user_id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("si", $email, $doctor['user_id']);
                $user_stmt->execute();
                
                // Update doctor information
                $doctor_query = "UPDATE doctors SET department_id = ?, first_name = ?, last_name = ?, specialization = ?, qualification = ?, experience = ?, phone = ?, address = ?, consultation_fee = ?, available_days = ?, available_time_start = ?, available_time_end = ?, status = ? WHERE doctor_id = ?";
                $doctor_stmt = $conn->prepare($doctor_query);
                $doctor_stmt->bind_param("isssssssdssssi", $department_id, $first_name, $last_name, $specialization, $qualification, $experience, $phone, $address, $consultation_fee, $available_days, $available_time_start, $available_time_end, $status, $doctor_id);
                $doctor_stmt->execute();
                
                // Commit transaction
                $conn->commit();
                
                $success = "Doctor updated successfully";
                
                // Refresh doctor data
                $stmt->execute();
                $result = $stmt->get_result();
                $doctor = $result->fetch_assoc();
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Error updating doctor: " . $e->getMessage();
            }
        }
    }
}

// Get available days as array
$available_days_array = !empty($doctor['available_days']) ? explode(',', $doctor['available_days']) : [];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Doctor | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li class="active">
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-md"></i> Edit Doctor</h2>
                </div>
                <div class="header-right">
                    <a href="doctors.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Doctors
                    </a>
                </div>
            </header>
            
            <div class="content-wrapper">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h3>Doctor Information</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]) . '?id=' . $doctor_id; ?>">
                            <h4>Account Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="username">Username</label>
                                    <input type="text" id="username" value="<?php echo htmlspecialchars($doctor['username']); ?>" disabled>
                                    <small class="form-text">Username cannot be changed</small>
                                </div>
                                
                                <div class="form-group">
                                    <label for="email">Email <span class="required">*</span></label>
                                    <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($doctor['email']); ?>" required>
                                </div>
                            </div>
                            
                            <h4>Personal Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first_name">First Name <span class="required">*</span></label>
                                    <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($doctor['first_name']); ?>" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="last_name">Last Name <span class="required">*</span></label>
                                    <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($doctor['last_name']); ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="phone">Phone Number</label>
                                    <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($doctor['phone']); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="department_id">Department</label>
                                    <select id="department_id" name="department_id">
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo ($doctor['department_id'] == $department['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($department['department_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea id="address" name="address" rows="2"><?php echo htmlspecialchars($doctor['address']); ?></textarea>
                            </div>
                            
                            <h4>Professional Information</h4>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="specialization">Specialization</label>
                                    <input type="text" id="specialization" name="specialization" value="<?php echo htmlspecialchars($doctor['specialization']); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="qualification">Qualification</label>
                                    <input type="text" id="qualification" name="qualification" value="<?php echo htmlspecialchars($doctor['qualification']); ?>">
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="experience">Experience (Years)</label>
                                    <input type="number" id="experience" name="experience" min="0" value="<?php echo htmlspecialchars($doctor['experience']); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="consultation_fee">Consultation Fee</label>
                                    <input type="number" id="consultation_fee" name="consultation_fee" min="0" step="0.01" value="<?php echo htmlspecialchars($doctor['consultation_fee']); ?>">
                                </div>
                            </div>
                            
                            <h4>Schedule Information</h4>
                            <div class="form-group">
                                <label>Available Days</label>
                                <div class="checkbox-group">
                                    <?php
                                    $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
                                    
                                    foreach ($days as $day) {
                                        $checked = in_array($day, $available_days_array) ? 'checked' : '';
                                        echo '<label class="checkbox-label">';
                                        echo '<input type="checkbox" name="available_days[]" value="' . $day . '" ' . $checked . '> ' . $day;
                                        echo '</label>';
                                    }
                                    ?>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="available_time_start">Available Time Start</label>
                                    <input type="time" id="available_time_start" name="available_time_start" value="<?php echo htmlspecialchars($doctor['available_time_start']); ?>">
                                </div>
                                
                                <div class="form-group">
                                    <label for="available_time_end">Available Time End</label>
                                    <input type="time" id="available_time_end" name="available_time_end" value="<?php echo htmlspecialchars($doctor['available_time_end']); ?>">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="status">Status</label>
                                <select id="status" name="status">
                                    <option value="active" <?php echo ($doctor['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo ($doctor['status'] == 'inactive') ? 'selected' : ''; ?>>Inactive</option>
                                    <option value="on_leave" <?php echo ($doctor['status'] == 'on_leave') ? 'selected' : ''; ?>>On Leave</option>
                                </select>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Doctor
                                </button>
                                <a href="doctors.php" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> Cancel
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>