<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

// Check if patient_capacity column exists in doctor_schedules table
$check_column_query = "SHOW COLUMNS FROM doctor_schedules LIKE 'patient_capacity'";
$check_column_result = $conn->query($check_column_query);
$capacity_column_exists = $check_column_result->num_rows > 0;

// If the column doesn't exist, add it
if (!$capacity_column_exists) {
    $add_column_query = "ALTER TABLE doctor_schedules ADD COLUMN patient_capacity INT DEFAULT 10 COMMENT 'Maximum number of patients for this time slot'";
    $conn->query($add_column_query);
}

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get doctor's schedule
$schedules = [];
$query = "SELECT * FROM doctor_schedules WHERE doctor_id = ? ORDER BY day_of_week, start_time";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $schedules[] = $row;
    }
}

// Get days of week for display
$days_of_week = [
    1 => 'Monday',
    2 => 'Tuesday',
    3 => 'Wednesday',
    4 => 'Thursday',
    5 => 'Friday',
    6 => 'Saturday',
    7 => 'Sunday'
];
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Schedule | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li class="active">
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-clock"></i> My Schedule</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Weekly Schedule</h3>
                    <div class="actions">
                        <a href="add_schedule.php" class="btn btn-primary"><i class="fas fa-plus"></i> Add Schedule Slot</a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="schedule-grid">
                            <?php foreach ($days_of_week as $day_num => $day_name): ?>
                                <div class="schedule-day">
                                    <div class="schedule-day-header">
                                        <h4><?php echo $day_name; ?></h4>
                                    </div>
                                    <div class="schedule-day-slots">
                                        <?php
                                        $day_slots = array_filter($schedules, function($schedule) use ($day_num) {
                                            return $schedule['day_of_week'] == $day_num;
                                        });

                                        if (count($day_slots) > 0):
                                            foreach ($day_slots as $slot):
                                        ?>
                                            <div class="schedule-slot">
                                                <div class="schedule-slot-time">
                                                    <?php echo date('h:i A', strtotime($slot['start_time'])); ?> -
                                                    <?php echo date('h:i A', strtotime($slot['end_time'])); ?>
                                                </div>
                                                <div class="schedule-slot-capacity">
                                                    <i class="fas fa-users"></i> <?php echo isset($slot['patient_capacity']) ? $slot['patient_capacity'] : 10; ?> patients
                                                </div>
                                                <div class="schedule-slot-status">
                                                    <span class="status-badge status-<?php echo $slot['is_active'] ? 'confirmed' : 'cancelled'; ?>">
                                                        <?php echo $slot['is_active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </div>
                                                <div class="schedule-slot-actions">
                                                    <a href="edit_schedule.php?id=<?php echo $slot['schedule_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_schedule.php?id=<?php echo $slot['schedule_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this schedule slot?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </div>
                                            </div>
                                        <?php
                                            endforeach;
                                        else:
                                        ?>
                                            <div class="schedule-slot empty">
                                                <p>No slots scheduled</p>
                                            </div>
                                        <?php endif; ?>

                                        <div class="schedule-slot-add">
                                            <a href="add_schedule.php?day=<?php echo $day_num; ?>" class="btn btn-sm btn-outline">
                                                <i class="fas fa-plus"></i> Add Slot
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Schedule List</h3>
                    </div>
                    <div class="card-body">
                        <?php if (count($schedules) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Day</th>
                                            <th>Start Time</th>
                                            <th>End Time</th>
                                            <th>Patient Capacity</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($schedules as $schedule): ?>
                                            <tr>
                                                <td><?php echo $days_of_week[$schedule['day_of_week']]; ?></td>
                                                <td><?php echo date('h:i A', strtotime($schedule['start_time'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($schedule['end_time'])); ?></td>
                                                <td>
                                                    <i class="fas fa-users"></i> <?php echo isset($schedule['patient_capacity']) ? $schedule['patient_capacity'] : 10; ?> patients
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $schedule['is_active'] ? 'confirmed' : 'cancelled'; ?>">
                                                        <?php echo $schedule['is_active'] ? 'Active' : 'Inactive'; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <a href="edit_schedule.php?id=<?php echo $schedule['schedule_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_schedule.php?id=<?php echo $schedule['schedule_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this schedule slot?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-clock empty-icon"></i>
                                <p>No schedule slots found.</p>
                                <a href="add_schedule.php" class="btn btn-primary">Add Schedule Slot</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
