-- SQL script to update the patients table to add blood_type column

-- Check if the blood_type column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'patients' AND COLUMN_NAME = 'blood_type';

-- Add blood_type column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE patients ADD COLUMN blood_type VARCHAR(5) DEFAULT NULL COMMENT "Patient blood type"',
    'SELECT "Column blood_type already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Update the patient with ID 3 to have a blood type
UPDATE patients SET blood_type = 'O+' WHERE patient_id = 3;
