<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

// Check if daily_patient_capacity column exists in doctors table
$check_column_query = "SHOW COLUMNS FROM doctors LIKE 'daily_patient_capacity'";
$check_column_result = $conn->query($check_column_query);
$capacity_column_exists = $check_column_result->num_rows > 0;

// If the column doesn't exist, add it
if (!$capacity_column_exists) {
    $add_column_query = "ALTER TABLE doctors ADD COLUMN daily_patient_capacity INT DEFAULT 10 COMMENT 'Maximum number of patients per day'";
    $conn->query($add_column_query);
}

$doctor_query = "SELECT d.*, u.email, u.username, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Basic information update
    if (isset($_POST['update_info'])) {
        $first_name = $_POST['first_name'];
        $last_name = $_POST['last_name'];
        $phone = $_POST['phone'];
        $address = $_POST['address'];
        $bio = $_POST['bio'];

        // Validate inputs
        if (empty($first_name) || empty($last_name) || empty($phone)) {
            $error_message = "Please fill in all required fields.";
        } else {
            // Update doctor information
            $update_query = "UPDATE doctors SET
                            first_name = ?,
                            last_name = ?,
                            phone = ?,
                            address = ?,
                            bio = ?,
                            updated_at = NOW()
                            WHERE doctor_id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("sssssi", $first_name, $last_name, $phone, $address, $bio, $doctor_id);

            if ($update_stmt->execute()) {
                $success_message = "Profile information updated successfully!";

                // Refresh doctor data
                $doctor_stmt->execute();
                $doctor_result = $doctor_stmt->get_result();
                $doctor_info = $doctor_result->fetch_assoc();
            } else {
                $error_message = "Error updating profile: " . $conn->error;
            }
        }
    }

    // Daily patient capacity update
    if (isset($_POST['update_capacity'])) {
        $daily_capacity = $_POST['daily_patient_capacity'];

        // Validate capacity input
        if (empty($daily_capacity) || !is_numeric($daily_capacity) || $daily_capacity < 1) {
            $error_message = "Please enter a valid daily patient capacity (minimum 1).";
        } else {
            // Update daily patient capacity
            $update_capacity_query = "UPDATE doctors SET
                                     daily_patient_capacity = ?,
                                     updated_at = NOW()
                                     WHERE doctor_id = ?";
            $update_capacity_stmt = $conn->prepare($update_capacity_query);
            $update_capacity_stmt->bind_param("ii", $daily_capacity, $doctor_id);

            if ($update_capacity_stmt->execute()) {
                $success_message = "Daily patient capacity updated successfully!";

                // Refresh doctor data
                $doctor_stmt->execute();
                $doctor_result = $doctor_stmt->get_result();
                $doctor_info = $doctor_result->fetch_assoc();
            } else {
                $error_message = "Error updating daily patient capacity: " . $conn->error;
            }
        }
    }

    // Password update
    if (isset($_POST['update_password'])) {
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validate inputs
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            $error_message = "Please fill in all password fields.";
        } elseif ($new_password !== $confirm_password) {
            $error_message = "New password and confirmation do not match.";
        } elseif (strlen($new_password) < 8) {
            $error_message = "New password must be at least 8 characters long.";
        } else {
            // Verify current password
            $password_query = "SELECT password FROM users WHERE user_id = ?";
            $password_stmt = $conn->prepare($password_query);
            $password_stmt->bind_param("i", $_SESSION['user_id']);
            $password_stmt->execute();
            $password_result = $password_stmt->get_result();
            $user_data = $password_result->fetch_assoc();

            if (!password_verify($current_password, $user_data['password'])) {
                $error_message = "Current password is incorrect.";
            } else {
                // Update password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $update_query = "UPDATE users SET password = ? WHERE user_id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("si", $hashed_password, $_SESSION['user_id']);

                if ($update_stmt->execute()) {
                    $success_message = "Password updated successfully!";
                } else {
                    $error_message = "Error updating password: " . $conn->error;
                }
            }
        }
    }

    // Profile image update
    if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] === 0) {
        $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
        $max_size = 2 * 1024 * 1024; // 2MB

        if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
            $error_message = "Only JPG, PNG, and GIF images are allowed.";
        } elseif ($_FILES['profile_image']['size'] > $max_size) {
            $error_message = "Image size must be less than 2MB.";
        } else {
            $upload_dir = "../assets/images/";
            $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
            $file_name = "doctor_" . $doctor_id . "_" . time() . "." . $file_extension;
            $upload_path = $upload_dir . $file_name;

            if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                // Update profile image in database
                $update_query = "UPDATE doctors SET profile_image = ? WHERE doctor_id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("si", $file_name, $doctor_id);

                if ($update_stmt->execute()) {
                    $success_message = "Profile image updated successfully!";

                    // Refresh doctor data
                    $doctor_stmt->execute();
                    $doctor_result = $doctor_stmt->get_result();
                    $doctor_info = $doctor_result->fetch_assoc();
                } else {
                    $error_message = "Error updating profile image: " . $conn->error;
                }
            } else {
                $error_message = "Error uploading image. Please try again.";
            }
        }
    }
}

// Get statistics
$total_patients = 0;
$total_appointments = 0;
$completed_appointments = 0;
$pending_appointments = 0;

// Total unique patients
$patients_query = "SELECT COUNT(DISTINCT patient_id) as count
                  FROM appointments
                  WHERE doctor_id = ?";
$patients_stmt = $conn->prepare($patients_query);
$patients_stmt->bind_param("i", $doctor_id);
$patients_stmt->execute();
$patients_result = $patients_stmt->get_result();
$total_patients = $patients_result->fetch_assoc()['count'];

// Total appointments
$appointments_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ?";
$appointments_stmt = $conn->prepare($appointments_query);
$appointments_stmt->bind_param("i", $doctor_id);
$appointments_stmt->execute();
$appointments_result = $appointments_stmt->get_result();
$total_appointments = $appointments_result->fetch_assoc()['count'];

// Completed appointments
$completed_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND status = 'completed'";
$completed_stmt = $conn->prepare($completed_query);
$completed_stmt->bind_param("i", $doctor_id);
$completed_stmt->execute();
$completed_result = $completed_stmt->get_result();
$completed_appointments = $completed_result->fetch_assoc()['count'];

// Pending appointments
$pending_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND status IN ('pending', 'confirmed') AND appointment_date >= CURDATE()";
$pending_stmt = $conn->prepare($pending_query);
$pending_stmt->bind_param("i", $doctor_id);
$pending_stmt->execute();
$pending_result = $pending_stmt->get_result();
$pending_appointments = $pending_result->fetch_assoc()['count'];

// Set default profile image if none exists
if (empty($doctor_info['profile_image'])) {
    $doctor_info['profile_image'] = 'default_doctor.png';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li class="active">
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-md"></i> My Profile</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <div class="profile-header">
                    <div class="profile-image">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor">
                        <form method="POST" action="" enctype="multipart/form-data" id="imageForm">
                            <label for="profile_image" class="image-upload-btn">
                                <i class="fas fa-camera"></i> Change Photo
                            </label>
                            <input type="file" name="profile_image" id="profile_image" accept="image/*" style="display: none;" onchange="document.getElementById('imageForm').submit();">
                        </form>
                    </div>
                    <div class="profile-info">
                        <h2>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h2>
                        <p><i class="fas fa-envelope"></i> <?php echo $doctor_info['email']; ?></p>
                        <p><i class="fas fa-phone"></i> <?php echo $doctor_info['phone']; ?></p>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-user-injured"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $total_patients; ?></h3>
                            <p>Total Patients</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $total_appointments; ?></h3>
                            <p>Total Appointments</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $completed_appointments; ?></h3>
                            <p>Completed Appointments</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-content">
                            <h3><?php echo $pending_appointments; ?></h3>
                            <p>Pending Appointments</p>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Personal Information</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="first_name">First Name <span class="required">*</span></label>
                                    <input type="text" name="first_name" id="first_name" class="form-control"
                                           value="<?php echo $doctor_info['first_name']; ?>"
                                           required>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="last_name">Last Name <span class="required">*</span></label>
                                    <input type="text" name="last_name" id="last_name" class="form-control"
                                           value="<?php echo $doctor_info['last_name']; ?>"
                                           required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="email">Email</label>
                                    <input type="email" id="email" class="form-control"
                                           value="<?php echo $doctor_info['email']; ?>"
                                           readonly>
                                    <small class="form-text text-muted">Email cannot be changed. Contact administrator for assistance.</small>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="phone">Phone <span class="required">*</span></label>
                                    <input type="text" name="phone" id="phone" class="form-control"
                                           value="<?php echo $doctor_info['phone']; ?>"
                                           required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="address">Address</label>
                                <textarea name="address" id="address" class="form-control" rows="2"><?php echo $doctor_info['address']; ?></textarea>
                            </div>


                            <div class="form-actions">
                                <button type="submit" name="update_info" class="btn btn-primary">
                                    <i class="fas fa-save"></i> Update Information
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Daily Patient Capacity</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="form-group">
                                <label for="daily_patient_capacity">Maximum Patients Per Day <span class="required">*</span></label>
                                <input type="number" name="daily_patient_capacity" id="daily_patient_capacity" class="form-control"
                                       value="<?php echo isset($doctor_info['daily_patient_capacity']) ? $doctor_info['daily_patient_capacity'] : 10; ?>"
                                       min="1" max="100" required>
                                <small class="form-text text-muted">Set the maximum number of patients you can see per day (1-100).</small>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="update_capacity" class="btn btn-primary">
                                    <i class="fas fa-users"></i> Update Capacity
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Change Password</h3>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="">
                            <div class="form-group">
                                <label for="current_password">Current Password <span class="required">*</span></label>
                                <input type="password" name="current_password" id="current_password" class="form-control" required>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="new_password">New Password <span class="required">*</span></label>
                                    <input type="password" name="new_password" id="new_password" class="form-control" required>
                                    <small class="form-text text-muted">Password must be at least 8 characters long.</small>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="confirm_password">Confirm New Password <span class="required">*</span></label>
                                    <input type="password" name="confirm_password" id="confirm_password" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="update_password" class="btn btn-primary">
                                    <i class="fas fa-key"></i> Change Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Account Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="info-grid">
                            <div class="info-section">
                                <div class="info-row">
                                    <div class="info-label">Username:</div>
                                    <div class="info-value"><?php echo $doctor_info['username']; ?></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Department:</div>
                                    <div class="info-value"><?php echo $doctor_info['department_name']; ?></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Specialization:</div>
                                    <div class="info-value"><?php echo $doctor_info['specialization']; ?></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Last Login:</div>
                                    <div class="info-value"><?php echo date('F d, Y h:i A', strtotime($doctor_info['last_login'])); ?></div>
                                </div>
                                <div class="info-row">
                                    <div class="info-label">Daily Patient Capacity:</div>
                                    <div class="info-value"><?php echo isset($doctor_info['daily_patient_capacity']) ? $doctor_info['daily_patient_capacity'] : 10; ?> patients per day</div>
                                </div>
                            </div>
                        </div>
                        <p class="mt-3">To update department, specialization, or license information, please contact the hospital administrator.</p>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Password validation
        document.getElementById('new_password').addEventListener('input', function() {
            const password = this.value;
            const confirmPassword = document.getElementById('confirm_password').value;

            if (password.length < 8) {
                this.setCustomValidity('Password must be at least 8 characters long');
            } else {
                this.setCustomValidity('');

                if (confirmPassword && password !== confirmPassword) {
                    document.getElementById('confirm_password').setCustomValidity('Passwords do not match');
                } else {
                    document.getElementById('confirm_password').setCustomValidity('');
                }
            }
        });

        document.getElementById('confirm_password').addEventListener('input', function() {
            const password = document.getElementById('new_password').value;
            const confirmPassword = this.value;

            if (password !== confirmPassword) {
                this.setCustomValidity('Passwords do not match');
            } else {
                this.setCustomValidity('');
            }
        });
    </script>
</body>
</html>