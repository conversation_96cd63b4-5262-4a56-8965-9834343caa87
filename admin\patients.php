<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Process delete patient
if (isset($_GET['delete']) && !empty($_GET['delete'])) {
    $delete_id = $_GET['delete'];
    
    // Check if patient has appointments
    $check_query = "SELECT COUNT(*) as count FROM appointments WHERE patient_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("i", $delete_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    $appointment_count = $check_result->fetch_assoc()['count'];
    
    if ($appointment_count > 0) {
        $error = "Cannot delete patient. There are appointments associated with this patient.";
    } else {
        // Start transaction
        $conn->begin_transaction();
        
        try {
            // Get user_id for the patient
            $user_query = "SELECT user_id FROM patients WHERE patient_id = ?";
            $user_stmt = $conn->prepare($user_query);
            $user_stmt->bind_param("i", $delete_id);
            $user_stmt->execute();
            $user_result = $user_stmt->get_result();
            
            if ($user_result->num_rows > 0) {
                $user_id = $user_result->fetch_assoc()['user_id'];
                
                // Delete patient
                $delete_patient_query = "DELETE FROM patients WHERE patient_id = ?";
                $delete_patient_stmt = $conn->prepare($delete_patient_query);
                $delete_patient_stmt->bind_param("i", $delete_id);
                $delete_patient_stmt->execute();
                
                // Delete user
                $delete_user_query = "DELETE FROM users WHERE user_id = ?";
                $delete_user_stmt = $conn->prepare($delete_user_query);
                $delete_user_stmt->bind_param("i", $user_id);
                $delete_user_stmt->execute();
                
                // Commit transaction
                $conn->commit();
                $success = "Patient deleted successfully.";
            } else {
                throw new Exception("Patient not found.");
            }
        } catch (Exception $e) {
            // Rollback transaction on error
            $conn->rollback();
            $error = "Error deleting patient: " . $e->getMessage();
        }
    }
}

// Get all patients
$patients = [];
$query = "SELECT p.*, u.email 
          FROM patients p 
          JOIN users u ON p.user_id = u.user_id 
          ORDER BY p.last_name, p.first_name";
$result = $conn->query($query);

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $patients[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patients | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> Patients</h2>
                </div>
                <div class="header-right">
                    <a href="add_patient.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Patient
                    </a>
                </div>
            </header>
            
            <div class="content-wrapper">
                <?php if (isset($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>
                
                <?php if (isset($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>
                
                <div class="card">
                    <div class="card-header">
                        <h3>All Patients</h3>
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="Search patients...">
                            <i class="fas fa-search"></i>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table" id="patientsTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Name</th>
                                        <th>Gender</th>
                                        <th>Age</th>
                                        <th>Contact</th>
                                        <th>Blood Group</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($patients) > 0): ?>
                                        <?php foreach ($patients as $patient): ?>
                                            <tr>
                                                <td><?php echo $patient['patient_id']; ?></td>
                                                <td>
                                                    <div class="user-info">
                                                        <img src="../assets/images/<?php echo $patient['profile_image']; ?>" alt="Patient" class="user-image-sm">
                                                        <span><?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?></span>
                                                    </div>
                                                </td>
                                                <td><?php echo ucfirst(htmlspecialchars($patient['gender'] ?? 'Not specified')); ?></td>
                                                <td>
                                                    <?php 
                                                    if ($patient['date_of_birth']) {
                                                        $dob = new DateTime($patient['date_of_birth']);
                                                        $now = new DateTime();
                                                        $age = $now->diff($dob)->y;
                                                        echo $age . ' years';
                                                    } else {
                                                        echo 'Not specified';
                                                    }
                                                    ?>
                                                </td>
                                                <td>
                                                    <div><?php echo htmlspecialchars($patient['email']); ?></div>
                                                    <div><?php echo htmlspecialchars($patient['phone']); ?></div>
                                                </td>
                                                <td><?php echo htmlspecialchars($patient['blood_group'] ?? 'Not specified'); ?></td>
                                                <td>
                                                    <a href="view_patient.php?id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_patient.php?id=<?php echo $patient['patient_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="patients.php?delete=<?php echo $patient['patient_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this patient?')">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No patients found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('patientsTable');
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                const row = rows[i];
                const cells = row.getElementsByTagName('td');
                let found = false;
                
                for (let j = 0; j < cells.length - 1; j++) {
                    const cellText = cells[j].textContent.toLowerCase();
                    if (cellText.includes(searchValue)) {
                        found = true;
                        break;
                    }
                }
                
                row.style.display = found ? '' : 'none';
            }
        });
    </script>
</body>
</html>