<?php
/**
 * Send Appointment Reminders
 *
 * This script sends reminder emails to patients about their upcoming appointments.
 * It should be run as a cron job, typically once per day.
 *
 * Example cron job (runs daily at 8 AM):
 * 0 8 * * * php /path/to/hospital_management_system/cron/send_appointment_reminders.php
 */

// Set the correct path to your application
$app_path = dirname(__DIR__);

// Include necessary files
require_once $app_path . '/includes/config.php';
require_once $app_path . '/includes/database.php';
require_once $app_path . '/includes/mailer.php';

// Define constants if not already defined
if (!defined('APPOINTMENT_REMINDERS_ENABLED')) {
    define('APPOINTMENT_REMINDERS_ENABLED', true);
}

// Define how many days in advance to send reminders
$reminder_days = defined('REMINDER_DAYS_BEFORE') ? REMINDER_DAYS_BEFORE : 1;

// Get the date for which to send reminders
$reminder_date = date('Y-m-d', strtotime("+{$reminder_days} days"));

// Log start of process
echo "Starting appointment reminder process for appointments on {$reminder_date}...\n";

// Connect to the database
$db = new Database();
$conn = $db->getConnection();

// Create mailer instance
$mailer = new Mailer();

// Check if reminder_sent column exists in appointments table
$check_column_query = "SHOW COLUMNS FROM appointments LIKE 'reminder_sent'";
$check_column_result = $conn->query($check_column_query);
$reminder_column_exists = $check_column_result->num_rows > 0;

// Query to get all appointments for the reminder date
$query = "SELECT a.*,
          p.first_name AS patient_first_name, p.last_name AS patient_last_name, p.email AS patient_email,
          d.first_name AS doctor_first_name, d.last_name AS doctor_last_name, d.department_id,
          dept.department_name
          FROM appointments a
          JOIN patients p ON a.patient_id = p.patient_id
          JOIN doctors d ON a.doctor_id = d.doctor_id
          LEFT JOIN departments dept ON d.department_id = dept.department_id
          WHERE a.appointment_date = ?
          AND a.status = 'confirmed'";

// Add reminder_sent condition if the column exists
if ($reminder_column_exists) {
    $query .= " AND (a.reminder_sent = 0 OR a.reminder_sent IS NULL)";
}

$stmt = $conn->prepare($query);
$stmt->bind_param("s", $reminder_date);
$stmt->execute();
$result = $stmt->get_result();

$count = 0;
$success_count = 0;

// Process each appointment
while ($row = $result->fetch_assoc()) {
    $count++;

    // Prepare data for email
    $appointment = [
        'appointment_id' => $row['appointment_id'],
        'appointment_date' => $row['appointment_date'],
        'appointment_time' => $row['appointment_time'],
        'status' => $row['status'],
        'reason' => $row['reason'],
        'appointment_type' => $row['appointment_type'] ?? 'Regular'
    ];

    $patient = [
        'first_name' => $row['patient_first_name'],
        'last_name' => $row['patient_last_name'],
        'email' => $row['patient_email']
    ];

    $doctor = [
        'first_name' => $row['doctor_first_name'],
        'last_name' => $row['doctor_last_name'],
        'department_id' => $row['department_id']
    ];

    $department = [
        'department_name' => $row['department_name']
    ];

    // Send reminder email
    $email_sent = $mailer->sendAppointmentReminder($appointment, $patient, $doctor, $department);

    if ($email_sent) {
        // Update the appointment record to mark reminder as sent if the columns exist
        if ($reminder_column_exists) {
            // Check if reminder_sent_date column exists
            $check_date_column_query = "SHOW COLUMNS FROM appointments LIKE 'reminder_sent_date'";
            $check_date_column_result = $conn->query($check_date_column_query);
            $reminder_date_column_exists = $check_date_column_result->num_rows > 0;

            if ($reminder_date_column_exists) {
                $update_query = "UPDATE appointments SET reminder_sent = 1, reminder_sent_date = NOW() WHERE appointment_id = ?";
            } else {
                $update_query = "UPDATE appointments SET reminder_sent = 1 WHERE appointment_id = ?";
            }

            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("i", $row['appointment_id']);
            $update_stmt->execute();
            $update_stmt->close();
        }

        $success_count++;
        echo "Reminder sent for appointment #{$row['appointment_id']} to {$patient['first_name']} {$patient['last_name']} ({$patient['email']}).\n";
    } else {
        echo "Failed to send reminder for appointment #{$row['appointment_id']} to {$patient['first_name']} {$patient['last_name']} ({$patient['email']}).\n";
    }
}

// Log completion
echo "Appointment reminder process completed. Processed {$count} appointments, {$success_count} reminders sent successfully.\n";

// Close statement
$stmt->close();
