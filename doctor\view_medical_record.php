<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is a doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login
               FROM doctors d
               JOIN users u ON d.user_id = u.user_id
               WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: medical_records.php");
    exit();
}

$record_id = intval($_GET['id']);

// Get medical record details
$record = [];
$record_query = "SELECT mr.*,
                p.first_name as patient_first_name, p.last_name as patient_last_name,
                p.date_of_birth, p.gender, p.blood_type, p.allergies,
                d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                a.appointment_date, a.appointment_time, a.reason as appointment_reason
                FROM medical_records mr
                JOIN patients p ON mr.patient_id = p.patient_id
                JOIN doctors d ON mr.doctor_id = d.doctor_id
                LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
                WHERE mr.record_id = ? AND mr.doctor_id = ?";
$record_stmt = $conn->prepare($record_query);
$record_stmt->bind_param("ii", $record_id, $doctor_id);
$record_stmt->execute();
$record_result = $record_stmt->get_result();

if ($record_result->num_rows === 0) {
    header("Location: medical_records.php");
    exit();
}

$record = $record_result->fetch_assoc();

// Check if the record belongs to this doctor or if doctor has permission to view all records
$is_owner = ($record['doctor_id'] == $doctor_id);
$can_view = $is_owner; // Add additional permission checks here if needed

if (!$can_view) {
    header("Location: medical_records.php?error=permission");
    exit();
}

// Get related prescriptions
$prescriptions = [];
$prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
                       FROM prescriptions p
                       JOIN doctors d ON p.doctor_id = d.doctor_id
                       WHERE p.patient_id = ? AND p.doctor_id = ?";

// Add date range filter
$prescriptions_query .= " AND p.created_at >= ? AND p.created_at <= DATE_ADD(?, INTERVAL 1 DAY)";
$prescriptions_query .= " ORDER BY p.created_at DESC";

$created_at = $record['created_at'];
$prescriptions_stmt = $conn->prepare($prescriptions_query);
$prescriptions_stmt->bind_param("iiss", $record['patient_id'], $doctor_id, $created_at, $created_at);
$prescriptions_stmt->execute();
$prescriptions_result = $prescriptions_stmt->get_result();

if ($prescriptions_result->num_rows > 0) {
    while ($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}

// Check for success message
$success = "";
if (isset($_GET['success']) && $_GET['success'] == 1) {
    $success = "Medical record saved successfully!";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Medical Record | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li class="active">
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-file-medical"></i> View Medical Record</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['specialization']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Medical Record #<?php echo $record_id; ?></h3>
                        <div class="card-actions">
                            <a href="edit_medical_record.php?id=<?php echo $record_id; ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Edit</a>
                            <a href="print_medical_record.php?id=<?php echo $record_id; ?>" class="btn btn-info" target="_blank"><i class="fas fa-print"></i> Print</a>
                            <a href="add_prescription.php?record_id=<?php echo $record_id; ?>" class="btn btn-success"><i class="fas fa-prescription"></i> Add Prescription</a>
                            <a href="delete_medical_record.php?id=<?php echo $record_id; ?>" class="btn btn-danger" onclick="return confirm('Are you sure you want to delete this medical record? This action cannot be undone.');"><i class="fas fa-trash"></i> Delete</a>
                            <a href="medical_records.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Information Sections with Improved Formatting -->
                        <div class="info-sections">
                            <!-- Patient Information Section -->
                            <div class="info-section patient-info-section">
                                <div class="section-header">
                                    <h4><i class="fas fa-user-injured"></i> Patient Information</h4>
                                </div>
                                <div class="section-content">
                                    <table class="info-table">
                                        <tr>
                                            <td class="info-label">Name:</td>
                                            <td class="info-value"><?php echo $record['patient_first_name'] . ' ' . $record['patient_last_name']; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Date of Birth:</td>
                                            <td class="info-value"><?php echo date('M d, Y', strtotime($record['date_of_birth'])); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Gender:</td>
                                            <td class="info-value"><?php echo ucfirst($record['gender']); ?></td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Blood Type:</td>
                                            <td class="info-value"><?php echo $record['blood_type']; ?></td>
                                        </tr>
                                        <tr>
                                            <td class="info-label">Allergies:</td>
                                            <td class="info-value"><?php echo !empty($record['allergies']) ? $record['allergies'] : 'None'; ?></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>

                            <div class="info-columns">
                                <!-- Appointment Information Section -->
                                <?php if (!empty($record['appointment_date'])): ?>
                                <div class="info-section appointment-info-section">
                                    <div class="section-header">
                                        <h4><i class="fas fa-calendar-check"></i> Appointment Information</h4>
                                    </div>
                                    <div class="section-content">
                                        <table class="info-table">
                                            <tr>
                                                <td class="info-label">Date:</td>
                                                <td class="info-value"><?php echo date('M d, Y', strtotime($record['appointment_date'])); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="info-label">Time:</td>
                                                <td class="info-value"><?php echo date('h:i A', strtotime($record['appointment_time'])); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="info-label">Reason:</td>
                                                <td class="info-value"><?php echo $record['appointment_reason']; ?></td>
                                            </tr>
                                        </table>
                                    </div>
                                </div>
                                <?php endif; ?>

                                <!-- Record Information Section -->
                                <div class="info-section record-info-section">
                                    <div class="section-header">
                                        <h4><i class="fas fa-info-circle"></i> Record Information</h4>
                                    </div>
                                    <div class="section-content">
                                        <table class="info-table">
                                            <tr>
                                                <td class="info-label">Created:</td>
                                                <td class="info-value"><?php echo date('M d, Y h:i A', strtotime($record['created_at'])); ?></td>
                                            </tr>
                                            <tr>
                                                <td class="info-label">Doctor:</td>
                                                <td class="info-value">Dr. <?php echo $record['doctor_first_name'] . ' ' . $record['doctor_last_name']; ?></td>
                                            </tr>
                                            <?php if (!empty($record['follow_up_date'])): ?>
                                            <tr>
                                                <td class="info-label">Follow-up Date:</td>
                                                <td class="info-value"><?php echo date('M d, Y', strtotime($record['follow_up_date'])); ?></td>
                                            </tr>
                                            <?php endif; ?>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Medical Record Details Section -->
                        <div class="info-section medical-details-section mt-4">
                            <div class="section-header">
                                <h4><i class="fas fa-file-medical"></i> Medical Record Details</h4>
                            </div>
                            <div class="section-content">
                                <table class="info-table full-width">
                                    <tr>
                                        <td class="info-label">Diagnosis:</td>
                                        <td class="info-value"><?php echo $record['diagnosis']; ?></td>
                                    </tr>

                                    <?php if (!empty($record['symptoms'])): ?>
                                    <tr>
                                        <td class="info-label">Symptoms:</td>
                                        <td class="info-value"><?php echo $record['symptoms']; ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if (!empty($record['vital_signs'])): ?>
                                    <tr>
                                        <td class="info-label">Vital Signs:</td>
                                        <td class="info-value"><?php echo $record['vital_signs']; ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if (!empty($record['treatment'])): ?>
                                    <tr>
                                        <td class="info-label">Treatment:</td>
                                        <td class="info-value"><?php echo nl2br($record['treatment']); ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if (!empty($record['family_medical_history'])): ?>
                                    <tr>
                                        <td class="info-label">Family Medical History:</td>
                                        <td class="info-value"><?php echo nl2br($record['family_medical_history']); ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if (!empty($record['lab_results'])): ?>
                                    <tr>
                                        <td class="info-label">Lab Results:</td>
                                        <td class="info-value"><?php echo nl2br($record['lab_results']); ?></td>
                                    </tr>
                                    <?php endif; ?>

                                    <?php if (!empty($record['notes'])): ?>
                                    <tr>
                                        <td class="info-label">Notes:</td>
                                        <td class="info-value"><?php echo nl2br($record['notes']); ?></td>
                                    </tr>
                                    <?php endif; ?>
                                </table>
                            </div>
                        </div>

                        <?php if (count($prescriptions) > 0): ?>
                            <div class="prescriptions-section mt-4">
                                <h4>Related Prescriptions</h4>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Medication</th>
                                                <th>Dosage</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($prescriptions as $prescription): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></td>
                                                    <td><?php echo $prescription['medication_name']; ?></td>
                                                    <td><?php echo $prescription['dosage'] . ' - ' . $prescription['frequency']; ?></td>
                                                    <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                    <td>
                                                        <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
