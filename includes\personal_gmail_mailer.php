<?php
/**
 * Personal Gmail Mailer class for sending emails using a personal Gmail account
 * This class extends SimpleGmailMailer for backward compatibility
 */

// Include the SimpleGmailMailer class
require_once __DIR__ . '/simple_gmail_mailer.php';

/**
 * Personal Gmail Mailer class
 * This class is maintained for backward compatibility with existing code
 */
class PersonalGmailMailer extends SimpleGmailMailer {
    /**
     * Constructor
     * Calls the parent constructor
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Send an appointment confirmation email
     * This method is maintained for backward compatibility
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Call the parent method
        return parent::sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send an appointment reminder email
     * This method is maintained for backward compatibility
     *
     * @param array $appointment Appointment information
     * @param array $patient Patient information
     * @param array $doctor Doctor information
     * @param array $department Department information
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentReminder($appointment, $patient, $doctor, $department) {
        // Call the parent method
        return parent::sendAppointmentReminder($appointment, $patient, $doctor, $department);
    }

    /**
     * Send an appointment cancellation email
     * This method is maintained for backward compatibility
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Call the parent method
        return parent::sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }
}

/**
 * Mailer class
 * This class is maintained for backward compatibility with existing code
 */
class Mailer extends PersonalGmailMailer {
    /**
     * Constructor
     * Calls the parent constructor
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Send an appointment confirmation email with enhanced template
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Make sure we're using the correct status
        if (!isset($appointment_info['status']) || empty($appointment_info['status'])) {
            $appointment_info['status'] = 'confirmed';
        }

        // If status is 'scheduled', treat it as 'confirmed' for email purposes
        if ($appointment_info['status'] == 'scheduled') {
            $appointment_info['status'] = 'confirmed';
        }

        // Call the parent method with the correct status
        return parent::sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send an appointment cancellation email with enhanced template
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Make sure we're using the correct status
        $appointment_info['status'] = 'cancelled';

        // Call the parent method with the correct status
        return parent::sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }
}

/**
 * SimpleMailer class
 * This class is maintained for backward compatibility with existing code
 */
class SimpleMailer extends PersonalGmailMailer {
    /**
     * Constructor
     * Calls the parent constructor
     */
    public function __construct() {
        parent::__construct();
    }
}
