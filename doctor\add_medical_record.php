<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get appointment ID from URL if provided
$appointment_id = isset($_GET['appointment_id']) ? intval($_GET['appointment_id']) : 0;
$appointment_info = [];
$patient_info = [];

if ($appointment_id > 0) {
    $appointment_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                         p.patient_id, p.date_of_birth, p.gender, p.blood_type, p.allergies
                         FROM appointments a
                         JOIN patients p ON a.patient_id = p.patient_id
                         WHERE a.appointment_id = ? AND a.doctor_id = ?";
    $appointment_stmt = $conn->prepare($appointment_query);
    $appointment_stmt->bind_param("ii", $appointment_id, $doctor_id);
    $appointment_stmt->execute();
    $appointment_result = $appointment_stmt->get_result();

    if ($appointment_result->num_rows > 0) {
        $appointment_info = $appointment_result->fetch_assoc();
        $patient_info = [
            'patient_id' => $appointment_info['patient_id'],
            'first_name' => $appointment_info['patient_first_name'],
            'last_name' => $appointment_info['patient_last_name'],
            'date_of_birth' => $appointment_info['date_of_birth'],
            'gender' => $appointment_info['gender'],
            'blood_type' => $appointment_info['blood_type'],
            'allergies' => $appointment_info['allergies']
        ];
    }
}

// Get all appointments for dropdown (including the current one even if it has records)
$appointments = [];

// First, get the specific appointment from URL if provided
if ($appointment_id > 0) {
    $specific_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name
                      FROM appointments a
                      JOIN patients p ON a.patient_id = p.patient_id
                      WHERE a.appointment_id = ? AND a.doctor_id = ?";
    $specific_stmt = $conn->prepare($specific_query);
    $specific_stmt->bind_param("ii", $appointment_id, $doctor_id);
    $specific_stmt->execute();
    $specific_result = $specific_stmt->get_result();

    if ($specific_result->num_rows > 0) {
        $appointments[] = $specific_result->fetch_assoc();
    }
}

// Then get other eligible appointments
$appointments_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name
                      FROM appointments a
                      JOIN patients p ON a.patient_id = p.patient_id
                      LEFT JOIN medical_records mr ON a.appointment_id = mr.appointment_id
                      WHERE a.doctor_id = ? AND a.status IN ('completed', 'confirmed', 'pending')
                      AND mr.record_id IS NULL
                      AND (? = 0 OR a.appointment_id != ?)
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
$appointments_stmt = $conn->prepare($appointments_query);
$appointments_stmt->bind_param("iii", $doctor_id, $appointment_id, $appointment_id);
$appointments_stmt->execute();
$appointments_result = $appointments_stmt->get_result();

if ($appointments_result->num_rows > 0) {
    while ($row = $appointments_result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Process form submission
$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $appointment_id = isset($_POST['appointment_id']) ? intval($_POST['appointment_id']) : 0;
    $symptoms = isset($_POST['symptoms']) ? trim($_POST['symptoms']) : '';
    $diagnosis = isset($_POST['diagnosis']) ? trim($_POST['diagnosis']) : '';
    $treatment = isset($_POST['treatment']) ? trim($_POST['treatment']) : '';
    $prescription = isset($_POST['prescription']) ? trim($_POST['prescription']) : '';
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    $follow_up_date = isset($_POST['follow_up_date']) ? $_POST['follow_up_date'] : null;
    $follow_up_notes = isset($_POST['follow_up_notes']) ? trim($_POST['follow_up_notes']) : '';

    // Validate required fields
    if ($appointment_id === 0) {
        $errors[] = "Please select an appointment";
    }

    if (empty($symptoms)) {
        $errors[] = "Symptoms are required";
    }

    if (empty($diagnosis)) {
        $errors[] = "Diagnosis is required";
    }

    if (empty($treatment)) {
        $errors[] = "Treatment is required";
    }

    if (empty($prescription)) {
        $errors[] = "Prescription is required";
    }

    // If no errors, insert into database
    if (empty($errors)) {
        // Generate record ID (format: MR-YYYYMMDD-XXXX)
        $date_part = date('Ymd');
        $random_part = mt_rand(1000, 9999);
        $record_id = "MR-{$date_part}-{$random_part}";

        // Get patient_id from the appointment
        $patient_id = 0;
        $get_patient_query = "SELECT patient_id FROM appointments WHERE appointment_id = ?";
        $get_patient_stmt = $conn->prepare($get_patient_query);
        $get_patient_stmt->bind_param("i", $appointment_id);
        $get_patient_stmt->execute();
        $get_patient_result = $get_patient_stmt->get_result();
        if ($get_patient_result->num_rows > 0) {
            $patient_id = $get_patient_result->fetch_assoc()['patient_id'];
        }

        // Insert medical record
        $insert_query = "INSERT INTO medical_records (record_id, patient_id, doctor_id, appointment_id,
                        symptoms, diagnosis, treatment, prescription, notes, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())";
        $insert_stmt = $conn->prepare($insert_query);
        $insert_stmt->bind_param("siiisssss", $record_id, $patient_id, $doctor_id, $appointment_id,
                               $symptoms, $diagnosis, $treatment, $prescription, $notes);

        if ($insert_stmt->execute()) {
            $success_message = "Medical record added successfully!";

            // Clear form data
            $appointment_id = 0;
            $symptoms = '';
            $diagnosis = '';
            $treatment = '';
            $prescription = '';
            $notes = '';
            $follow_up_date = '';
            $follow_up_notes = '';
        } else {
            $errors[] = "Error adding medical record: " . $conn->error;
        }
    }
}

// Get appointment details if selected
if ($appointment_id > 0 && empty($appointment_info)) {
    $appointment_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                         p.patient_id, p.date_of_birth, p.gender, p.blood_type, p.allergies
                         FROM appointments a
                         JOIN patients p ON a.patient_id = p.patient_id
                         WHERE a.appointment_id = ? AND a.doctor_id = ?";
    $appointment_stmt = $conn->prepare($appointment_query);
    $appointment_stmt->bind_param("ii", $appointment_id, $doctor_id);
    $appointment_stmt->execute();
    $appointment_result = $appointment_stmt->get_result();

    if ($appointment_result->num_rows > 0) {
        $appointment_info = $appointment_result->fetch_assoc();
        $patient_info = [
            'patient_id' => $appointment_info['patient_id'],
            'first_name' => $appointment_info['patient_first_name'],
            'last_name' => $appointment_info['patient_last_name'],
            'date_of_birth' => $appointment_info['date_of_birth'],
            'gender' => $appointment_info['gender'],
            'blood_type' => $appointment_info['blood_type'],
            'allergies' => $appointment_info['allergies']
        ];
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Medical Record | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li class="active">
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-file-medical"></i> Add Medical Record</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a> /
                    <a href="medical_records.php">Medical Records</a> /
                    <span>Add Medical Record</span>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert error">
                        <i class="fas fa-exclamation-circle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success_message)): ?>
                    <div class="alert success">
                        <i class="fas fa-check-circle"></i>
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Medical Record Details</h3>
                    </div>
                    <div class="card-body">
                        <form action="add_medical_record.php" method="POST">
                            <div class="form-group">
                                <label for="appointment_id">Select Appointment <span class="required">*</span></label>
                                <select name="appointment_id" id="appointment_id" required>
                                    <option value="">Select Appointment</option>
                                    <?php foreach ($appointments as $appointment): ?>
                                        <option value="<?php echo $appointment['appointment_id']; ?>" <?php echo ($appointment_id == $appointment['appointment_id']) ? 'selected' : ''; ?>>
                                            <?php echo date('M d, Y', strtotime($appointment['appointment_date'])) . ' - ' .
                                                  date('h:i A', strtotime($appointment['appointment_time'])) . ' - ' .
                                                  $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <?php if (!empty($patient_info)): ?>
                                <div class="patient-info-box">
                                    <h4>Patient Information</h4>
                                    <div class="patient-info-grid">
                                        <div class="info-item">
                                            <span class="info-label">Name:</span>
                                            <span class="info-value"><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Date of Birth:</span>
                                            <span class="info-value"><?php echo date('M d, Y', strtotime($patient_info['date_of_birth'])); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Gender:</span>
                                            <span class="info-value"><?php echo ucfirst($patient_info['gender']); ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Blood Type:</span>
                                            <span class="info-value"><?php echo $patient_info['blood_type']; ?></span>
                                        </div>
                                        <div class="info-item">
                                            <span class="info-label">Allergies:</span>
                                            <span class="info-value"><?php echo !empty($patient_info['allergies']) ? $patient_info['allergies'] : 'None'; ?></span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="form-group">
                                <label for="symptoms">Symptoms <span class="required">*</span></label>
                                <textarea name="symptoms" id="symptoms" rows="3" required><?php echo isset($symptoms) ? $symptoms : ''; ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="diagnosis">Diagnosis <span class="required">*</span></label>
                                <textarea name="diagnosis" id="diagnosis" rows="3" required><?php echo isset($diagnosis) ? $diagnosis : ''; ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="treatment">Treatment <span class="required">*</span></label>
                                <textarea name="treatment" id="treatment" rows="3" required><?php echo isset($treatment) ? $treatment : ''; ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="prescription">Prescription <span class="required">*</span></label>
                                <textarea name="prescription" id="prescription" rows="3" required><?php echo isset($prescription) ? $prescription : ''; ?></textarea>
                            </div>

                            <div class="form-group">
                                <label for="notes">Additional Notes</label>
                                <textarea name="notes" id="notes" rows="3"><?php echo isset($notes) ? $notes : ''; ?></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="follow_up_date">Follow-up Date</label>
                                    <input type="date" name="follow_up_date" id="follow_up_date" value="<?php echo isset($follow_up_date) ? $follow_up_date : ''; ?>">
                                </div>
                                <div class="form-group">
                                    <label for="follow_up_notes">Follow-up Notes</label>
                                    <textarea name="follow_up_notes" id="follow_up_notes" rows="2"><?php echo isset($follow_up_notes) ? $follow_up_notes : ''; ?></textarea>
                                </div>
                            </div>

                            <div class="form-actions">
                                <a href="medical_records.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Save Medical Record</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Fetch appointment details when appointment is selected
        document.addEventListener('DOMContentLoaded', function() {
            const appointmentSelect = document.getElementById('appointment_id');

            // Pre-select the appointment from URL if available
            const urlParams = new URLSearchParams(window.location.search);
            const appointmentId = urlParams.get('appointment_id');

            if (appointmentId) {
                // Find the option with this value
                const options = appointmentSelect.options;
                for (let i = 0; i < options.length; i++) {
                    if (options[i].value === appointmentId) {
                        appointmentSelect.selectedIndex = i;
                        break;
                    }
                }
            }

            appointmentSelect.addEventListener('change', function() {
                if (this.value) {
                    window.location.href = 'add_medical_record.php?appointment_id=' + this.value;
                }
            });
        });
    </script>
</body>
</html>
