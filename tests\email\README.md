# Email Testing Scripts

This directory contains test scripts for the email functionality of the CSUCC Hospital Management System.

## Test Files

1. **test_db_names.php**
   - Tests that doctor and patient names are correctly retrieved from the database and displayed in emails
   - Uses real data from the database if available, or falls back to sample data

2. **test_fixed_email.php**
   - Tests the fixed email functionality after resolving syntax errors
   - Retrieves a real appointment from the database and sends a confirmation email

3. **test_simple_mailer.php**
   - Tests the SimpleGmailMailer class directly
   - Sends a test email and an appointment confirmation email with sample data

4. **test_status_changed_by.php**
   - Tests the functionality that shows which doctor changed the appointment status
   - Sends confirmation, cancellation, and completion emails with the status_changed_by parameter

5. **final_email_test_fixed.php**
   - A comprehensive test of the email functionality
   - Retrieves doctor and patient information from the database
   - Creates a test appointment and sends an email
   - Also tests the SimpleGmailMailer directly

## How to Use

To run any of these tests, simply access them through your web browser:

```
http://localhost:8000/tests/email/test_file_name.php
```

Replace `test_file_name.php` with the name of the test file you want to run.

## Expected Results

All test scripts should display information about the test being performed and whether the email was sent successfully. Check your <NAME_EMAIL> to see if you received the test messages.

## Troubleshooting

If you encounter any issues with the email functionality, these test scripts can help identify the problem:

1. If no emails are being sent, check the Gmail app password and SMTP settings
2. If doctor or patient names are not displaying correctly, check the database connection and queries
3. If the status_changed_by information is not showing, check the email templates

For more detailed troubleshooting, check the PHP error logs.
