<?php
session_start();
include "../db_connect.php";
include "../includes/mailer.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Get all patients
$patients = [];
$patients_query = "SELECT * FROM patients ORDER BY last_name, first_name";
$patients_result = $conn->query($patients_query);
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Get all departments
$departments = [];
$departments_query = "SELECT * FROM departments ORDER BY name";
$departments_result = $conn->query($departments_query);
if ($departments_result->num_rows > 0) {
    while ($row = $departments_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $patient_id = $_POST['patient_id'];
    $department_id = $_POST['department_id'];
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $reason = $_POST['reason'];
    $notes = $_POST['notes'];
    $status = $_POST['status'];

    // Validate inputs
    if (empty($patient_id) || empty($department_id) || empty($appointment_date) || empty($appointment_time)) {
        $error_message = "Please fill in all required fields.";
    } else {
        // Check if the appointment time is available
        $check_query = "SELECT * FROM appointments
                        WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
                        AND status != 'cancelled'";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("iss", $doctor_id, $appointment_date, $appointment_time);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = "This appointment time is already booked. Please select another time.";
        } else {
            // Insert new appointment
            $insert_query = "INSERT INTO appointments (patient_id, doctor_id, department_id, appointment_date, appointment_time, reason, notes, status)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $insert_stmt = $conn->prepare($insert_query);
            $insert_stmt->bind_param("iiisssss", $patient_id, $doctor_id, $department_id, $appointment_date, $appointment_time, $reason, $notes, $status);

            if ($insert_stmt->execute()) {
                $appointment_id = $conn->insert_id;
                $success_message = "Appointment created successfully!";

                // Get patient information for the email
                $patient_query = "SELECT p.*, u.email
                                 FROM patients p
                                 JOIN users u ON p.user_id = u.user_id
                                 WHERE p.patient_id = ?";
                $patient_stmt = $conn->prepare($patient_query);
                $patient_stmt->bind_param("i", $patient_id);
                $patient_stmt->execute();
                $patient_result = $patient_stmt->get_result();
                $patient_info = $patient_result->fetch_assoc();

                // Get appointment details
                $appointment_query = "SELECT * FROM appointments WHERE appointment_id = ?";
                $appointment_stmt = $conn->prepare($appointment_query);
                $appointment_stmt->bind_param("i", $appointment_id);
                $appointment_stmt->execute();
                $appointment_result = $appointment_stmt->get_result();
                $appointment_info = $appointment_result->fetch_assoc();

                // Send email notification to patient
                $mailer = new Mailer();
                $email_result = $mailer->sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info);

                // Update the appointment record to mark confirmation as sent
                if ($email_result) {
                    $update_query = "UPDATE appointments SET confirmation_sent = 1, confirmation_sent_date = NOW() WHERE appointment_id = ?";
                    $update_stmt = $conn->prepare($update_query);
                    $update_stmt->bind_param("i", $appointment_id);
                    $update_stmt->execute();
                }

                // Redirect to appointments page after successful creation
                header("Location: appointments.php");
                exit();
            } else {
                $error_message = "Error creating appointment: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-plus"></i> Add New Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Create New Appointment</h3>
                    <div class="actions">
                        <a href="appointments.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form action="add_appointment.php" method="post">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="patient_id">Patient <span class="required">*</span></label>
                                    <select name="patient_id" id="patient_id" class="form-control" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>">
                                                <?php echo $patient['last_name'] . ', ' . $patient['first_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="department_id">Department <span class="required">*</span></label>
                                    <select name="department_id" id="department_id" class="form-control" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo ($department['department_id'] == $doctor_info['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo $department['name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="appointment_date">Date <span class="required">*</span></label>
                                    <input type="date" name="appointment_date" id="appointment_date" class="form-control" min="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                                <div class="form-group col-md-6">
                                    <label for="appointment_time">Time <span class="required">*</span></label>
                                    <input type="time" name="appointment_time" id="appointment_time" class="form-control" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reason">Reason for Visit <span class="required">*</span></label>
                                <input type="text" name="reason" id="reason" class="form-control" required>
                            </div>

                            <div class="form-group">
                                <label for="notes">Additional Notes</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="status">Status <span class="required">*</span></label>
                                <select name="status" id="status" class="form-control" required>
                                    <option value="pending">Pending</option>
                                    <option value="confirmed">Confirmed</option>
                                </select>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Create Appointment</button>
                                <a href="appointments.php" class="btn btn-outline">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Add any JavaScript for form validation or dynamic behavior here
        document.addEventListener('DOMContentLoaded', function() {
            // Example: Check for available time slots based on selected date
            const dateInput = document.getElementById('appointment_date');
            const timeInput = document.getElementById('appointment_time');

            dateInput.addEventListener('change', function() {
                // You could make an AJAX call here to check available times
                console.log('Date selected:', this.value);
            });
        });
    </script>
</body>
</html>
