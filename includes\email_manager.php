<?php
/**
 * Email Manager class
 * This class serves as a central point for all email functionality in the system
 * It uses the SimpleGmailMailer class by default, but can be configured to use other mailers
 */

// Include the SimpleGmailMailer class and database utilities
require_once __DIR__ . '/simple_gmail_mailer.php';
require_once __DIR__ . '/db_utils.php';
require_once __DIR__ . '/../db_connect.php';

class EmailManager {
    /**
     * @var DBUtils Database utilities
     */
    private $db_utils;
    /**
     * @var SimpleGmailMailer The mailer instance
     */
    private $mailer;

    /**
     * Constructor
     *
     * @param string $mailer_class The mailer class to use (defaults to SimpleGmailMailer)
     */
    public function __construct($mailer_class = 'SimpleGmailMailer') {
        global $conn;

        // Create a new mailer instance
        $this->mailer = new $mailer_class();

        // Create a new database utilities instance
        $this->db_utils = new DBUtils($conn);
    }

    /**
     * Send an email
     *
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param array $data Additional data for the email
     * @return bool Whether the email was sent successfully
     */
    public function sendEmail($subject, $message, $data = []) {
        return $this->mailer->sendEmail($subject, $message, $data);
    }

    /**
     * Send an appointment status email using database information
     *
     * @param int $appointment_id Appointment ID
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentStatusEmailById($appointment_id, $status_changed_by = null) {
        // Get appointment information from database
        $appointment_info = $this->db_utils->getAppointmentById($appointment_id);

        if (!$appointment_info) {
            error_log("Appointment not found: $appointment_id");
            return false;
        }

        // Get patient information from database
        $patient_info = $this->db_utils->getPatientById($appointment_info['patient_id']);

        if (!$patient_info) {
            error_log("Patient not found: " . $appointment_info['patient_id']);
            return false;
        }

        // Get doctor information from database
        $doctor_info = $this->db_utils->getDoctorById($appointment_info['doctor_id']);

        if (!$doctor_info) {
            error_log("Doctor not found: " . $appointment_info['doctor_id']);
            return false;
        }

        // If status_changed_by is not provided, try to get the doctor who last updated the appointment
        if (!$status_changed_by && isset($appointment_info['last_updated_by'])) {
            // Try to get the doctor who updated the status
            $status_changed_by = $this->db_utils->getDoctorById($appointment_info['last_updated_by']);
        }

        // Format the data for the email
        $formatted_appointment = [
            'appointment_id' => $appointment_info['appointment_id'],
            'appointment_date' => $appointment_info['appointment_date'],
            'appointment_time' => $appointment_info['appointment_time'],
            'status' => $appointment_info['status'],
            'notes' => isset($appointment_info['notes']) ? $appointment_info['notes'] : '',
            'department_id' => $appointment_info['department_id'],
            'department_name' => $appointment_info['department_name'],
            // Add additional fields from the appointment query
            'doctor_first_name' => isset($appointment_info['doctor_first_name']) ? $appointment_info['doctor_first_name'] : $doctor_info['first_name'],
            'doctor_last_name' => isset($appointment_info['doctor_last_name']) ? $appointment_info['doctor_last_name'] : $doctor_info['last_name'],
            'patient_first_name' => isset($appointment_info['patient_first_name']) ? $appointment_info['patient_first_name'] : $patient_info['first_name'],
            'patient_last_name' => isset($appointment_info['patient_last_name']) ? $appointment_info['patient_last_name'] : $patient_info['last_name'],
            'doctor_name' => isset($appointment_info['doctor_name']) ? $appointment_info['doctor_name'] : "Dr. " . $doctor_info['first_name'] . " " . $doctor_info['last_name'],
            'patient_name' => isset($appointment_info['patient_name']) ? $appointment_info['patient_name'] : $patient_info['first_name'] . " " . $patient_info['last_name']
        ];

        $formatted_patient = [
            'first_name' => $patient_info['first_name'],
            'last_name' => $patient_info['last_name'],
            'email' => $patient_info['email'],
            'phone' => isset($patient_info['phone']) ? $patient_info['phone'] : '',
            'address' => isset($patient_info['address']) ? $patient_info['address'] : '',
            'date_of_birth' => isset($patient_info['date_of_birth']) ? $patient_info['date_of_birth'] : '',
            'gender' => isset($patient_info['gender']) ? $patient_info['gender'] : '',
            'full_name' => isset($patient_info['full_name']) ? $patient_info['full_name'] : $patient_info['first_name'] . " " . $patient_info['last_name']
        ];

        $formatted_doctor = [
            'first_name' => $doctor_info['first_name'],
            'last_name' => $doctor_info['last_name'],
            'email' => isset($doctor_info['email']) ? $doctor_info['email'] : '',
            'phone' => isset($doctor_info['phone']) ? $doctor_info['phone'] : '',
            'specialization' => isset($doctor_info['specialization']) ? $doctor_info['specialization'] : '',
            'department_name' => isset($doctor_info['department_name']) ? $doctor_info['department_name'] : '',
            'full_name' => isset($doctor_info['full_name']) ? $doctor_info['full_name'] : "Dr. " . $doctor_info['first_name'] . " " . $doctor_info['last_name']
        ];

        // Format the status_changed_by data if available
        $formatted_status_changed_by = null;
        if ($status_changed_by) {
            $formatted_status_changed_by = [
                'first_name' => $status_changed_by['first_name'],
                'last_name' => $status_changed_by['last_name'],
                'email' => isset($status_changed_by['email']) ? $status_changed_by['email'] : '',
                'specialization' => isset($status_changed_by['specialization']) ? $status_changed_by['specialization'] : '',
                'department_name' => isset($status_changed_by['department_name']) ? $status_changed_by['department_name'] : '',
                'full_name' => isset($status_changed_by['full_name']) ? $status_changed_by['full_name'] : "Dr. " . $status_changed_by['first_name'] . " " . $status_changed_by['last_name']
            ];
        }

        // Send the email
        $result = $this->mailer->sendAppointmentConfirmation($formatted_appointment, $formatted_patient, $formatted_doctor, $formatted_status_changed_by);

        // If email was sent successfully, mark it as sent in the database
        if ($result) {
            $this->db_utils->markConfirmationSent($appointment_id);
        }

        return $result;
    }

    /**
     * Send an appointment status email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentStatusEmail($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        return $this->mailer->sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send an appointment confirmation email by ID
     *
     * @param int $appointment_id Appointment ID
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentConfirmationById($appointment_id, $status_changed_by = null) {
        // Get appointment information from database
        $appointment_info = $this->db_utils->getAppointmentById($appointment_id);

        if (!$appointment_info) {
            error_log("Appointment not found: $appointment_id");
            return false;
        }

        // Update status to confirmed
        $this->db_utils->updateAppointmentStatus($appointment_id, 'Confirmed');

        // Send the email
        return $this->sendAppointmentStatusEmailById($appointment_id, $status_changed_by);
    }

    /**
     * Send an appointment confirmation email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Set status to confirmed
        $appointment_info['status'] = 'Confirmed';
        return $this->sendAppointmentStatusEmail($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send an appointment cancellation email by ID
     *
     * @param int $appointment_id Appointment ID
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCancellationById($appointment_id, $status_changed_by = null) {
        // Get appointment information from database
        $appointment_info = $this->db_utils->getAppointmentById($appointment_id);

        if (!$appointment_info) {
            error_log("Appointment not found: $appointment_id");
            return false;
        }

        // Update status to canceled
        $this->db_utils->updateAppointmentStatus($appointment_id, 'Canceled');

        // Send the email
        return $this->sendAppointmentStatusEmailById($appointment_id, $status_changed_by);
    }

    /**
     * Send an appointment cancellation email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Set status to canceled
        $appointment_info['status'] = 'Canceled';
        return $this->sendAppointmentStatusEmail($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send an appointment completion email by ID
     *
     * @param int $appointment_id Appointment ID
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCompletionById($appointment_id, $status_changed_by = null) {
        // Get appointment information from database
        $appointment_info = $this->db_utils->getAppointmentById($appointment_id);

        if (!$appointment_info) {
            error_log("Appointment not found: $appointment_id");
            return false;
        }

        // Update status to completed
        $this->db_utils->updateAppointmentStatus($appointment_id, 'Completed');

        // Send the email
        return $this->sendAppointmentStatusEmailById($appointment_id, $status_changed_by);
    }

    /**
     * Send an appointment completion email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCompletion($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Set status to completed
        $appointment_info['status'] = 'Completed';
        return $this->sendAppointmentStatusEmail($appointment_info, $patient_info, $doctor_info, $status_changed_by);
    }

    /**
     * Send a test email
     *
     * @return bool Whether the email was sent successfully
     */
    public function sendTestEmail() {
        return $this->mailer->sendTestEmail();
    }

    /**
     * Get the mailer instance
     *
     * @return SimpleGmailMailer The mailer instance
     */
    public function getMailer() {
        return $this->mailer;
    }

    /**
     * Set the mailer instance
     *
     * @param SimpleGmailMailer $mailer The mailer instance
     */
    public function setMailer($mailer) {
        $this->mailer = $mailer;
    }
}

// Create a global instance of the EmailManager for easy access
$GLOBALS['email_manager'] = new EmailManager();

/**
 * Get the global EmailManager instance
 *
 * @return EmailManager The global EmailManager instance
 */
function get_email_manager() {
    return $GLOBALS['email_manager'];
}

/**
 * Send an email using the global EmailManager instance
 *
 * @param string $subject Email subject
 * @param string $message Email message (HTML)
 * @param array $data Additional data for the email
 * @return bool Whether the email was sent successfully
 */
function send_email($subject, $message, $data = []) {
    return get_email_manager()->sendEmail($subject, $message, $data);
}

/**
 * Send an appointment confirmation email using the global EmailManager instance
 *
 * @param int|array $appointment_id_or_info Appointment ID or information array
 * @param array|null $patient_info Patient information (optional if appointment ID is provided)
 * @param array|null $doctor_info Doctor information (optional if appointment ID is provided)
 * @param array|null $status_changed_by Doctor who changed the status (optional)
 * @return bool Whether the email was sent successfully
 */
function send_appointment_confirmation($appointment_id_or_info, $patient_info = null, $doctor_info = null, $status_changed_by = null) {
    if (is_array($appointment_id_or_info) && $patient_info !== null && $doctor_info !== null) {
        // If all three arrays are provided, use them directly
        return get_email_manager()->sendAppointmentConfirmation($appointment_id_or_info, $patient_info, $doctor_info, $status_changed_by);
    } else if (is_numeric($appointment_id_or_info)) {
        // If only appointment ID is provided, fetch data from database
        return get_email_manager()->sendAppointmentConfirmationById($appointment_id_or_info, $status_changed_by);
    } else {
        error_log("Invalid parameters for send_appointment_confirmation");
        return false;
    }
}

/**
 * Send an appointment cancellation email using the global EmailManager instance
 *
 * @param int|array $appointment_id_or_info Appointment ID or information array
 * @param array|null $patient_info Patient information (optional if appointment ID is provided)
 * @param array|null $doctor_info Doctor information (optional if appointment ID is provided)
 * @param array|null $status_changed_by Doctor who changed the status (optional)
 * @return bool Whether the email was sent successfully
 */
function send_appointment_cancellation($appointment_id_or_info, $patient_info = null, $doctor_info = null, $status_changed_by = null) {
    if (is_array($appointment_id_or_info) && $patient_info !== null && $doctor_info !== null) {
        // If all three arrays are provided, use them directly
        return get_email_manager()->sendAppointmentCancellation($appointment_id_or_info, $patient_info, $doctor_info, $status_changed_by);
    } else if (is_numeric($appointment_id_or_info)) {
        // If only appointment ID is provided, fetch data from database
        return get_email_manager()->sendAppointmentCancellationById($appointment_id_or_info, $status_changed_by);
    } else {
        error_log("Invalid parameters for send_appointment_cancellation");
        return false;
    }
}

/**
 * Send an appointment completion email using the global EmailManager instance
 *
 * @param int|array $appointment_id_or_info Appointment ID or information array
 * @param array|null $patient_info Patient information (optional if appointment ID is provided)
 * @param array|null $doctor_info Doctor information (optional if appointment ID is provided)
 * @param array|null $status_changed_by Doctor who changed the status (optional)
 * @return bool Whether the email was sent successfully
 */
function send_appointment_completion($appointment_id_or_info, $patient_info = null, $doctor_info = null, $status_changed_by = null) {
    if (is_array($appointment_id_or_info) && $patient_info !== null && $doctor_info !== null) {
        // If all three arrays are provided, use them directly
        return get_email_manager()->sendAppointmentCompletion($appointment_id_or_info, $patient_info, $doctor_info, $status_changed_by);
    } else if (is_numeric($appointment_id_or_info)) {
        // If only appointment ID is provided, fetch data from database
        return get_email_manager()->sendAppointmentCompletionById($appointment_id_or_info, $status_changed_by);
    } else {
        error_log("Invalid parameters for send_appointment_completion");
        return false;
    }
}

/**
 * Send a test email using the global EmailManager instance
 *
 * @return bool Whether the email was sent successfully
 */
function send_test_email() {
    return get_email_manager()->sendTestEmail();
}

