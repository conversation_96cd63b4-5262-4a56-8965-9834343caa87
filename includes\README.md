# Includes Directory

This directory contains core functionality files for the CSUCC Hospital Management System.

## Email-Related Files

1. **email_manager.php**
   - Main class for managing email functionality
   - Provides methods for sending various types of appointment emails
   - Interacts with the database to retrieve appointment, doctor, and patient information
   - Includes global helper functions for easy access to email functionality

2. **simple_gmail_mailer.php**
   - Simplified mailer class for sending emails using Gmail SMTP
   - Handles the actual email sending process
   - Includes methods for formatting and sending different types of emails
   - Used by the EmailManager class

3. **personal_gmail_mailer.php**
   - Legacy mailer class maintained for backward compatibility
   - Extends SimpleGmailMailer to ensure existing code continues to work

4. **email_templates/**
   - Directory containing HTML email templates
   - See the README in that directory for more information

## Database-Related Files

1. **db_utils.php**
   - Utility class for database operations
   - Provides methods for retrieving and updating appointment, doctor, and patient information
   - Used by the EmailManager class to get data for emails

2. **config.php**
   - Configuration settings for the application
   - Includes database connection parameters and other settings

## How to Use

### Sending Emails

To send emails, you can use the global helper functions:

```php
// Send an appointment confirmation email
send_appointment_confirmation($appointment_id);

// Send an appointment cancellation email
send_appointment_cancellation($appointment_id);

// Send an appointment completion email
send_appointment_completion($appointment_id);

// Send a test email
send_test_email();
```

Or you can use the EmailManager class directly:

```php
$email_manager = new EmailManager();

// Send an appointment confirmation email
$email_manager->sendAppointmentConfirmationById($appointment_id);

// Send an appointment cancellation email
$email_manager->sendAppointmentCancellationById($appointment_id);

// Send an appointment completion email
$email_manager->sendAppointmentCompletionById($appointment_id);
```

### Including the Status Changed By Information

To include information about which doctor changed the appointment status:

```php
// Get the doctor who changed the status
$status_changed_by = $db_utils->getDoctorById($doctor_id);

// Send an appointment confirmation email with the doctor who changed the status
send_appointment_confirmation($appointment_id, null, null, $status_changed_by);
```
