<?php
// <PERSON><PERSON><PERSON> to update the patients table structure
include "db_connect.php";

echo "<h2>Updating Patients Table</h2>";

// Read the SQL file
$sql_file = file_get_contents('sql/update_patients_table.sql');

// Split into individual statements
$statements = explode(';', $sql_file);

// Execute each statement
foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        if ($conn->query($statement)) {
            echo "Executed: " . substr($statement, 0, 50) . "...<br>";
        } else {
            echo "Error executing: " . $statement . "<br>";
            echo "Error details: " . $conn->error . "<br>";
        }
    }
}

echo "<h3>Update Complete</h3>";
echo "<p>The patients table has been updated to include the blood_type column.</p>";
echo "<p><a href='doctor/view_appointment.php?id=2'>Return to Appointment View</a></p>";
?>
