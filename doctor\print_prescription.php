<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if prescription ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: prescriptions.php");
    exit();
}

$prescription_id = $_GET['id'];

// Get prescription details
$prescription = null;
$query = "SELECT p.*, pt.first_name as patient_first_name, pt.last_name as patient_last_name,
          pt.date_of_birth, pt.gender, pt.phone, pt.address, pt.profile_image as patient_profile_image,
          pt.allergies, pt.medical_history, pt.blood_type,
          u.email as patient_email
          FROM prescriptions p
          JOIN patients pt ON p.patient_id = pt.patient_id
          JOIN users u ON pt.user_id = u.user_id
          WHERE p.prescription_id = ? AND p.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("si", $prescription_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $prescription = $result->fetch_assoc();
} else {
    header("Location: prescriptions.php");
    exit();
}

// Get prescription medications
$medications = [];
$medications_query = "SELECT * FROM prescription_medications WHERE prescription_id = ? ORDER BY id";
$medications_stmt = $conn->prepare($medications_query);
$medications_stmt->bind_param("s", $prescription_id);
$medications_stmt->execute();
$medications_result = $medications_stmt->get_result();
if ($medications_result->num_rows > 0) {
    while ($row = $medications_result->fetch_assoc()) {
        $medications[] = $row;
    }
}

// Get the most recent diagnosis from medical records
$diagnosis = '';
$diagnosis_query = "SELECT diagnosis FROM medical_records
                  WHERE patient_id = ?
                  ORDER BY created_at DESC LIMIT 1";
$diagnosis_stmt = $conn->prepare($diagnosis_query);
$diagnosis_stmt->bind_param("i", $prescription['patient_id']);
$diagnosis_stmt->execute();
$diagnosis_result = $diagnosis_stmt->get_result();
if ($diagnosis_result->num_rows > 0) {
    $diagnosis = $diagnosis_result->fetch_assoc()['diagnosis'];
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Print Prescription | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/print.css">
    <style>
        @media print {
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
            }
            .print-header {
                text-align: center;
                margin-bottom: 20px;
                border-bottom: 2px solid #333;
                padding-bottom: 10px;
            }
            .hospital-name {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .hospital-address {
                font-size: 14px;
                margin-bottom: 5px;
            }
            .hospital-contact {
                font-size: 14px;
            }
            .prescription-title {
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                margin: 20px 0;
                text-decoration: underline;
            }
            .prescription-info {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
            }
            .patient-info, .doctor-info {
                width: 48%;
            }
            .info-title {
                font-weight: bold;
                margin-bottom: 10px;
                border-bottom: 1px solid #ccc;
                padding-bottom: 5px;
            }
            .info-row {
                margin-bottom: 5px;
            }
            .info-label {
                font-weight: bold;
                display: inline-block;
                width: 100px;
            }
            .medications-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            .medications-table th, .medications-table td {
                border: 1px solid #ccc;
                padding: 8px;
                text-align: left;
            }
            .medications-table th {
                background-color: #f2f2f2;
            }
            .notes-section {
                margin: 20px 0;
            }
            .notes-title {
                font-weight: bold;
                margin-bottom: 10px;
            }
            .medical-info-section {
                margin: 20px 0;
                border: 1px solid #ccc;
                padding: 15px;
                border-radius: 5px;
            }
            .medical-info-title {
                font-weight: bold;
                margin-bottom: 15px;
                font-size: 16px;
                border-bottom: 1px solid #ccc;
                padding-bottom: 5px;
            }
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            .signature-box {
                width: 200px;
                text-align: center;
            }
            .signature-line {
                border-top: 1px solid #333;
                margin-bottom: 5px;
            }
            .no-print {
                display: none;
            }
        }

        /* Styles for screen view */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }
        .print-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            border: 1px solid #ccc;
        }
        .print-header {
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 2px solid #333;
            padding-bottom: 10px;
        }
        .hospital-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .hospital-address {
            font-size: 14px;
            margin-bottom: 5px;
        }
        .hospital-contact {
            font-size: 14px;
        }
        .prescription-title {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0;
            text-decoration: underline;
        }
        .prescription-info {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }
        .patient-info, .doctor-info {
            width: 48%;
        }
        .info-title {
            font-weight: bold;
            margin-bottom: 10px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .info-row {
            margin-bottom: 5px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        .medications-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .medications-table th, .medications-table td {
            border: 1px solid #ccc;
            padding: 8px;
            text-align: left;
        }
        .medications-table th {
            background-color: #f2f2f2;
        }
        .notes-section {
            margin: 20px 0;
        }
        .notes-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .medical-info-section {
            margin: 20px 0;
            border: 1px solid #ccc;
            padding: 15px;
            border-radius: 5px;
        }
        .medical-info-title {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 16px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 200px;
            text-align: center;
        }
        .signature-line {
            border-top: 1px solid #333;
            margin-bottom: 5px;
        }
        .print-actions {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 0 5px;
        }
        .btn-secondary {
            background-color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-actions no-print">
            <button onclick="window.print()" class="btn">Print Prescription</button>
            <a href="view_prescription.php?id=<?php echo $prescription_id; ?>" class="btn btn-secondary">Back to Prescription</a>
        </div>

        <div class="print-header">
            <div class="hospital-name">CSUCC HOSPITAL</div>
            <div class="hospital-address">123 University Avenue, Cebu City, Philippines 6000</div>
            <div class="hospital-contact">Phone: (************* | Email: <EMAIL></div>
        </div>

        <div class="prescription-title">PRESCRIPTION</div>

        <div class="prescription-info">
            <div class="patient-info">
                <div class="info-title">Patient Information</div>
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span><?php echo $prescription['patient_first_name'] . ' ' . $prescription['patient_last_name']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Gender:</span>
                    <span><?php echo ucfirst($prescription['gender']); ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Age:</span>
                    <span><?php echo calculateAge($prescription['date_of_birth']); ?> years</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Address:</span>
                    <span><?php echo $prescription['address']; ?></span>
                </div>
                <?php if (!empty($prescription['blood_type'])): ?>
                <div class="info-row">
                    <span class="info-label">Blood Type:</span>
                    <span><?php echo $prescription['blood_type']; ?></span>
                </div>
                <?php endif; ?>
                <div class="info-row">
                    <span class="info-label">Allergies:</span>
                    <span><?php echo !empty($prescription['allergies']) ? $prescription['allergies'] : 'None reported'; ?></span>
                </div>
            </div>

            <div class="doctor-info">
                <div class="info-title">Doctor Information</div>
                <div class="info-row">
                    <span class="info-label">Name:</span>
                    <span>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Specialization:</span>
                    <span><?php
                        // Add space between words if needed
                        $specialization = trim($doctor_info['specialization']);
                        // Check if it contains "GeneralCardiology" without space
                        if (preg_match('/GeneralCardiology/i', $specialization)) {
                            echo "General Cardiology";
                        } else {
                            echo $specialization;
                        }
                    ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">License No:</span>
                    <span><?php echo $doctor_info['license_number'] ?? 'N/A'; ?></span>
                </div>
                <div class="info-row">
                    <span class="info-label">Date:</span>
                    <span><?php echo date('F d, Y', strtotime($prescription['prescription_date'])); ?></span>
                </div>
            </div>
        </div>

        <div class="medical-info-section">
            <div class="medical-info-title">Medical Information</div>
            <div class="info-row">
                <span class="info-label">Diagnosis:</span>
                <span><?php echo !empty($diagnosis) ? $diagnosis : 'No diagnosis recorded'; ?></span>
            </div>
            <div class="info-row">
                <span class="info-label">Medical History:</span>
                <span><?php echo !empty($prescription['medical_history']) ? $prescription['medical_history'] : 'None reported'; ?></span>
            </div>
        </div>

        <div class="medications-section">
            <table class="medications-table">
                <thead>
                    <tr>
                        <th>Medication</th>
                        <th>Dosage</th>
                        <th>Frequency</th>
                        <th>Duration</th>
                        <th>Instructions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($medications as $medication): ?>
                        <tr>
                            <td><?php echo $medication['medication_name']; ?></td>
                            <td><?php echo $medication['dosage']; ?></td>
                            <td><?php echo $medication['frequency']; ?></td>
                            <td><?php echo $medication['duration']; ?></td>
                            <td><?php echo $medication['instructions']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (!empty($prescription['notes'])): ?>
        <div class="notes-section">
            <div class="notes-title">Additional Notes:</div>
            <div><?php echo nl2br($prescription['notes']); ?></div>
        </div>
        <?php endif; ?>

        <div class="signature-section">
            <div class="signature-box">
                <div class="signature-line"></div>
                <div>Patient's Signature</div>
            </div>

            <div class="signature-box">
                <div class="signature-line"></div>
                <div>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></div>
                <div><?php
                    // Use the same formatting as above
                    $specialization = trim($doctor_info['specialization']);
                    // Check if it contains "GeneralCardiology" without space
                    if (preg_match('/GeneralCardiology/i', $specialization)) {
                        echo "General Cardiology";
                    } else {
                        echo $specialization;
                    }
                ?></div>
            </div>
        </div>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            // Uncomment the line below to automatically print when page loads
            // window.print();
        };
    </script>

    <?php
    // Helper function to calculate age
    function calculateAge($dob) {
        $birthDate = new DateTime($dob);
        $today = new DateTime('today');
        $age = $birthDate->diff($today)->y;
        return $age;
    }
    ?>
</body>
</html>
