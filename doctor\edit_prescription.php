<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login 
                FROM doctors d 
                JOIN users u ON d.user_id = u.user_id 
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if prescription ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: prescriptions.php");
    exit();
}

$prescription_id = $_GET['id'];

// Get prescription details
$prescription = null;
$query = "SELECT p.*, pt.first_name as patient_first_name, pt.last_name as patient_last_name
          FROM prescriptions p
          JOIN patients pt ON p.patient_id = pt.patient_id
          WHERE p.prescription_id = ? AND p.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("si", $prescription_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $prescription = $result->fetch_assoc();
} else {
    header("Location: prescriptions.php");
    exit();
}

// Get prescription medications
$medications = [];
$medications_query = "SELECT * FROM prescription_medications WHERE prescription_id = ? ORDER BY id";
$medications_stmt = $conn->prepare($medications_query);
$medications_stmt->bind_param("s", $prescription_id);
$medications_stmt->execute();
$medications_result = $medications_stmt->get_result();
if ($medications_result->num_rows > 0) {
    while ($row = $medications_result->fetch_assoc()) {
        $medications[] = $row;
    }
}

// Get first medication for form defaults
$medication_name = '';
$dosage = '';
$frequency = '';
$duration = '';
$instructions = '';

if (count($medications) > 0) {
    $medication_name = $medications[0]['medication_name'];
    $dosage = $medications[0]['dosage'];
    $frequency = $medications[0]['frequency'];
    $duration = $medications[0]['duration'];
    $instructions = $medications[0]['instructions'];
}

// Get all patients for dropdown
$patients = [];
$patients_query = "SELECT p.* FROM patients p
                  JOIN appointments a ON p.patient_id = a.patient_id
                  WHERE a.doctor_id = ?
                  GROUP BY p.patient_id
                  ORDER BY p.last_name, p.first_name";
$patients_stmt = $conn->prepare($patients_query);
$patients_stmt->bind_param("i", $doctor_id);
$patients_stmt->execute();
$patients_result = $patients_stmt->get_result();
if ($patients_result->num_rows > 0) {
    while ($row = $patients_result->fetch_assoc()) {
        $patients[] = $row;
    }
}

// Process form submission
$errors = [];
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate inputs
    $patient_id = isset($_POST['patient_id']) ? intval($_POST['patient_id']) : 0;
    $prescription_date = isset($_POST['prescription_date']) ? $_POST['prescription_date'] : '';
    $medications_input = isset($_POST['medications']) ? trim($_POST['medications']) : '';
    $dosage = isset($_POST['dosage']) ? trim($_POST['dosage']) : '';
    $frequency = isset($_POST['frequency']) ? trim($_POST['frequency']) : '';
    $duration = isset($_POST['duration']) ? trim($_POST['duration']) : '';
    $instructions = isset($_POST['instructions']) ? trim($_POST['instructions']) : '';
    $notes = isset($_POST['notes']) ? trim($_POST['notes']) : '';
    $status = isset($_POST['status']) ? trim($_POST['status']) : 'active';

    // Validate required fields
    if ($patient_id === 0) {
        $errors[] = "Please select a patient";
    }
    if (empty($prescription_date)) {
        $errors[] = "Please enter a prescription date";
    }
    if (empty($medications_input)) {
        $errors[] = "Please enter at least one medication";
    }
    if (empty($dosage)) {
        $errors[] = "Please enter dosage";
    }
    if (empty($frequency)) {
        $errors[] = "Please enter frequency";
    }
    if (empty($duration)) {
        $errors[] = "Please enter duration";
    }

    // If no errors, update the prescription
    if (empty($errors)) {
        // Update prescription
        $update_query = "UPDATE prescriptions SET 
                        patient_id = ?, 
                        prescription_date = ?,
                        notes = ?,
                        status = ?,
                        updated_at = NOW()
                        WHERE prescription_id = ? AND doctor_id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("issssi", $patient_id, $prescription_date, $notes, $status, $prescription_id, $doctor_id);
        
        if ($update_stmt->execute()) {
            // Delete existing medications
            $delete_query = "DELETE FROM prescription_medications WHERE prescription_id = ?";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bind_param("s", $prescription_id);
            $delete_stmt->execute();
            
            // Process medications (split by new line)
            $medication_list = explode("\n", $medications_input);
            $success = true;
            
            foreach ($medication_list as $medication) {
                $medication = trim($medication);
                if (!empty($medication)) {
                    // Insert into prescription_medications table
                    $med_query = "INSERT INTO prescription_medications (prescription_id, medication_name, dosage, frequency, duration, instructions)
                                VALUES (?, ?, ?, ?, ?, ?)";
                    $med_stmt = $conn->prepare($med_query);
                    $med_stmt->bind_param("ssssss", $prescription_id, $medication, $dosage, $frequency, $duration, $instructions);
                    
                    if (!$med_stmt->execute()) {
                        $success = false;
                        $errors[] = "Error adding medication: " . $conn->error;
                        break;
                    }
                }
            }
            
            if ($success) {
                $success_message = "Prescription updated successfully!";
                
                // Log activity
                $activity_query = "INSERT INTO activity_logs (user_id, activity_type, description, created_at) 
                                VALUES (?, 'prescription', 'Updated prescription: $prescription_id', NOW())";
                $activity_stmt = $conn->prepare($activity_query);
                $activity_stmt->bind_param("i", $_SESSION['user_id']);
                $activity_stmt->execute();
                
                // Refresh prescription data
                $stmt->execute();
                $result = $stmt->get_result();
                $prescription = $result->fetch_assoc();
                
                // Refresh medications
                $medications = [];
                $medications_stmt->execute();
                $medications_result = $medications_stmt->get_result();
                if ($medications_result->num_rows > 0) {
                    while ($row = $medications_result->fetch_assoc()) {
                        $medications[] = $row;
                    }
                }
                
                // Update form defaults
                if (count($medications) > 0) {
                    $medication_name = $medications[0]['medication_name'];
                    $dosage = $medications[0]['dosage'];
                    $frequency = $medications[0]['frequency'];
                    $duration = $medications[0]['duration'];
                    $instructions = $medications[0]['instructions'];
                }
            }
        } else {
            $errors[] = "Error updating prescription: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Prescription | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li class="active">
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> Schedule</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-prescription"></i> Edit Prescription</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['specialization']; ?></p>
                        </div>
                    </div>
                </div>
            </header>
            
            <div class="dashboard-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Edit Prescription #<?php echo $prescription_id; ?></h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($errors)): ?>
                            <div class="alert error">
                                <i class="fas fa-exclamation-circle"></i>
                                <ul>
                                    <?php foreach ($errors as $error): ?>
                                        <li><?php echo $error; ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($success_message)): ?>
                            <div class="alert success">
                                <i class="fas fa-check-circle"></i>
                                <?php echo $success_message; ?>
                            </div>
                        <?php endif; ?>
                        
                        <form action="edit_prescription.php?id=<?php echo $prescription_id; ?>" method="POST">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="patient_id">Patient <span class="required">*</span></label>
                                    <select name="patient_id" id="patient_id" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>" <?php echo ($prescription['patient_id'] == $patient['patient_id']) ? 'selected' : ''; ?>>
                                                <?php echo $patient['last_name'] . ', ' . $patient['first_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="prescription_date">Date <span class="required">*</span></label>
                                    <input type="date" name="prescription_date" id="prescription_date" value="<?php echo $prescription['prescription_date']; ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="medications">Medications <span class="required">*</span></label>
                                <textarea name="medications" id="medications" rows="3" required><?php echo $medication_name; ?></textarea>
                                <div class="form-help">Enter each medication on a new line</div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="dosage">Dosage <span class="required">*</span></label>
                                    <input type="text" name="dosage" id="dosage" value="<?php echo $dosage; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="frequency">Frequency <span class="required">*</span></label>
                                    <input type="text" name="frequency" id="frequency" value="<?php echo $frequency; ?>" required>
                                </div>
                            </div>
                            
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="duration">Duration <span class="required">*</span></label>
                                    <input type="text" name="duration" id="duration" value="<?php echo $duration; ?>" required>
                                </div>
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <select name="status" id="status">
                                        <option value="active" <?php echo ($prescription['status'] == 'active') ? 'selected' : ''; ?>>Active</option>
                                        <option value="completed" <?php echo ($prescription['status'] == 'completed') ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo ($prescription['status'] == 'cancelled') ? 'selected' : ''; ?>>Cancelled</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="instructions">Instructions</label>
                                <textarea name="instructions" id="instructions" rows="2"><?php echo $instructions; ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label for="notes">Notes</label>
                                <textarea name="notes" id="notes" rows="3"><?php echo $prescription['notes']; ?></textarea>
                            </div>
                            
                            <div class="form-actions">
                                <a href="view_prescription.php?id=<?php echo $prescription_id; ?>" class="btn btn-secondary">Cancel</a>
                                <button type="submit" class="btn btn-primary">Update Prescription</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
