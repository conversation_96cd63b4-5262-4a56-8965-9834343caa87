# Email Troubleshooting Guide

## Current Issues

The email functionality in the Hospital Management System is not working correctly. We've identified the following issues:

1. **Authentication Failure**: The Gmail account credentials are not being accepted by the Gmail SMTP server.
2. **PHP Mail Function**: The PHP mail function is not properly configured on the server.

## Recommended Solutions

### Option 1: Fix Gmail App Password

1. **Generate a New App Password**:
   - Go to your Google Account settings (https://myaccount.google.com/)
   - Navigate to Security > 2-Step Verification > App passwords
   - Create a new app password specifically for this application
   - Use the new app password without spaces in the configuration

2. **Update Configuration**:
   - Update the password in `includes/simple_gmail_mailer.php`
   - Update the password in `includes/config.php`
   - Make sure to use the password without spaces

### Option 2: Use a Different Email Service

1. **Consider using a different email service** like:
   - SendGrid
   - Mailgun
   - Amazon SES
   - Mailtrap (for testing)

2. **Update the mailer class** to use the new service

### Option 3: Configure Local SMTP Server

1. **Install and configure a local SMTP server** like:
   - hMailServer
   - Postfix (for Linux)
   - Mercury Mail (for Windows)

2. **Update the mailer configuration** to use the local SMTP server

## Immediate Workaround

For immediate testing, you can use a service like Mailtrap.io which provides a free testing inbox:

1. Sign up for a free Mailtrap account
2. Get the SMTP credentials
3. Update the mailer configuration to use Mailtrap

## Gmail-Specific Issues

When using Gmail, ensure:

1. **2FA is enabled** on the Gmail account
2. **App password is generated** (not your regular password)
3. **Less secure app access** is not relied upon (it's deprecated)
4. **SMTP settings are correct**:
   - Host: smtp.gmail.com
   - Port: 587
   - Security: TLS
   - Authentication: Yes

## Testing Steps

1. Use the provided test scripts to verify email functionality
2. Check error logs for specific error messages
3. Try sending emails with different configurations
4. Verify that the recipient email address is correct

## Long-term Solution

Consider implementing a queue-based email system that:
1. Stores emails in a database queue
2. Processes them via a background job
3. Handles retries for failed emails
4. Provides logging and monitoring

This approach is more robust and can handle temporary email service outages.
