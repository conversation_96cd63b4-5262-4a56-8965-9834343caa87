<?php
session_start();
include "../db_connect.php";
// Check if simple_mailer.php exists, if not, create a basic version
if (!file_exists("../includes/simple_mailer.php")) {
    include "../includes/mailer.php"; // Try to include the regular mailer instead
} else {
    include "../includes/simple_mailer.php";
}

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get admin information
$admin_id = 0;
$admin_info = [];

$admin_query = "SELECT a.*, u.email, u.last_login
               FROM admins a
               JOIN users u ON a.user_id = u.user_id
               WHERE a.user_id = ?";
$admin_stmt = $conn->prepare($admin_query);
$admin_stmt->bind_param("i", $_SESSION['user_id']);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows > 0) {
    $admin_info = $admin_result->fetch_assoc();
    $admin_id = $admin_info['admin_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = intval($_GET['id']);

// Get appointment details
$appointment = [];
$appointment_query = "SELECT a.*,
                     p.first_name as patient_first_name, p.last_name as patient_last_name,
                     p.phone as patient_phone, p.profile_image as patient_profile_image,
                     d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                     d.specialization, d.profile_image as doctor_profile_image,
                     d.phone as doctor_phone, dep.department_name
                     FROM appointments a
                     JOIN patients p ON a.patient_id = p.patient_id
                     JOIN doctors d ON a.doctor_id = d.doctor_id
                     JOIN departments dep ON a.department_id = dep.department_id
                     WHERE a.appointment_id = ?";
$appointment_stmt = $conn->prepare($appointment_query);
$appointment_stmt->bind_param("i", $appointment_id);
$appointment_stmt->execute();
$appointment_result = $appointment_stmt->get_result();

if ($appointment_result->num_rows === 0) {
    header("Location: appointments.php");
    exit();
}

$appointment = $appointment_result->fetch_assoc();

// Get medical records related to this appointment
$medical_records = [];
// Check if medical_records table exists
$table_check = $conn->query("SHOW TABLES LIKE 'medical_records'");
if ($table_check->num_rows > 0) {
    $records_query = "SELECT mr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
                     FROM medical_records mr
                     JOIN doctors d ON mr.doctor_id = d.doctor_id
                     WHERE mr.appointment_id = ?
                     ORDER BY mr.created_at DESC";
    $records_stmt = $conn->prepare($records_query);
    $records_stmt->bind_param("i", $appointment_id);
    $records_stmt->execute();
    $records_result = $records_stmt->get_result();
    if ($records_result->num_rows > 0) {
        while ($row = $records_result->fetch_assoc()) {
            $medical_records[] = $row;
        }
    }
}

// Get prescriptions related to this appointment
$prescriptions = [];
// Check if prescriptions table exists
$table_check = $conn->query("SHOW TABLES LIKE 'prescriptions'");
if ($table_check->num_rows > 0) {
    // Check if appointment_id column exists in prescriptions table
    $column_check = $conn->query("SHOW COLUMNS FROM prescriptions LIKE 'appointment_id'");

    if ($column_check->num_rows > 0) {
        // If appointment_id column exists, use it in the query
        $prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                              d.specialization, d.profile_image as doctor_profile_image
                              FROM prescriptions p
                              JOIN doctors d ON p.doctor_id = d.doctor_id
                              WHERE p.patient_id = ? AND (p.appointment_id = ? OR p.appointment_id IS NULL)
                              ORDER BY p.prescription_date DESC";
        $prescriptions_stmt = $conn->prepare($prescriptions_query);
        $prescriptions_stmt->bind_param("ii", $appointment['patient_id'], $appointment_id);
    } else {
        // If appointment_id column doesn't exist, just filter by patient_id
        $prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                              d.specialization, d.profile_image as doctor_profile_image
                              FROM prescriptions p
                              JOIN doctors d ON p.doctor_id = d.doctor_id
                              WHERE p.patient_id = ?
                              ORDER BY p.prescription_date DESC";
        $prescriptions_stmt = $conn->prepare($prescriptions_query);
        $prescriptions_stmt->bind_param("i", $appointment['patient_id']);
    }

    $prescriptions_stmt->execute();
    $prescriptions_result = $prescriptions_stmt->get_result();
    if ($prescriptions_result->num_rows > 0) {
        while ($row = $prescriptions_result->fetch_assoc()) {
            $prescriptions[] = $row;
        }
    }
}

// Process status update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];
    $update_query = "UPDATE appointments SET status = ? WHERE appointment_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("si", $new_status, $appointment_id);

    if ($update_stmt->execute()) {
        // Send notification email if status changed to confirmed or cancelled
        if ($new_status == 'confirmed' || $new_status == 'cancelled') {
            try {
                // Get patient email
                $patient_query = "SELECT p.*, u.email FROM patients p JOIN users u ON p.user_id = u.user_id WHERE p.patient_id = ?";
                $patient_stmt = $conn->prepare($patient_query);
                $patient_stmt->bind_param("i", $appointment['patient_id']);
                $patient_stmt->execute();
                $patient_result = $patient_stmt->get_result();
                $patient_info = $patient_result->fetch_assoc();

                // Get doctor info
                $doctor_query = "SELECT d.* FROM doctors d WHERE d.doctor_id = ?";
                $doctor_stmt = $conn->prepare($doctor_query);
                $doctor_stmt->bind_param("i", $appointment['doctor_id']);
                $doctor_stmt->execute();
                $doctor_result = $doctor_stmt->get_result();
                $doctor_info = $doctor_result->fetch_assoc();

                // Send email notification
                $mailer = new SimpleMailer();
                if ($new_status == 'confirmed') {
                    $mailer->sendAppointmentConfirmation($appointment, $patient_info, $doctor_info);
                } else if ($new_status == 'cancelled') {
                    // If you have a cancellation method, use it
                    if (method_exists($mailer, 'sendAppointmentCancellation')) {
                        $mailer->sendAppointmentCancellation($appointment, $patient_info, $doctor_info);
                    } else {
                        // Otherwise, just send a generic confirmation with cancelled status
                        $appointment['status'] = 'cancelled'; // Set status for email
                        $mailer->sendAppointmentConfirmation($appointment, $patient_info, $doctor_info);
                    }
                }
            } catch (Exception $e) {
                // Log error but continue
                error_log("Failed to send notification email: " . $e->getMessage());
            }
        }

        // Refresh the page to show updated status
        header("Location: view_appointment.php?id=" . $appointment_id . "&status_updated=1");
        exit();
    }
}

// Check for success message
$success_message = "";
if (isset($_GET['status_updated']) && $_GET['status_updated'] == 1) {
    $success_message = "Appointment status updated successfully.";
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-check"></i> View Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $admin_info['profile_image']; ?>" alt="Admin" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $admin_info['first_name'] . ' ' . $admin_info['last_name']; ?></h4>
                            <p>Administrator</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if (!empty($success_message)): ?>
                    <div class="alert alert-success">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Appointment #<?php echo $appointment_id; ?></h3>
                        <div class="card-actions">
                            <a href="edit_appointment.php?id=<?php echo $appointment_id; ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Edit</a>
                            <a href="appointments.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="appointment-status">
                            <span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span>

                            <form action="" method="post" class="status-form">
                                <select name="status" id="status">
                                    <option value="pending" <?php echo $appointment['status'] == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                    <option value="confirmed" <?php echo $appointment['status'] == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                    <option value="completed" <?php echo $appointment['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                    <option value="cancelled" <?php echo $appointment['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                    <option value="no_show" <?php echo $appointment['status'] == 'no_show' ? 'selected' : ''; ?>>No Show</option>
                                </select>
                                <button type="submit" name="update_status" class="btn btn-sm">Update Status</button>
                            </form>
                        </div>

                        <div class="row">
                            <div class="col-md-8">
                                <div class="appointment-details">
                                    <h4>Appointment Details</h4>
                                    <div class="detail-row">
                                        <div class="detail-label">Date:</div>
                                        <div class="detail-value"><?php echo date('l, F j, Y', strtotime($appointment['appointment_date'])); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Time:</div>
                                        <div class="detail-value"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Department:</div>
                                        <div class="detail-value"><?php echo $appointment['department_name']; ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Doctor:</div>
                                        <div class="detail-value">Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Patient:</div>
                                        <div class="detail-value"><?php echo $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Reason:</div>
                                        <div class="detail-value"><?php echo $appointment['reason']; ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Created:</div>
                                        <div class="detail-value"><?php echo date('M d, Y h:i A', strtotime($appointment['created_at'])); ?></div>
                                    </div>
                                </div>

                                <?php if (count($medical_records) > 0): ?>
                                    <div class="card mt-4">
                                        <div class="card-header">
                                            <h3>Medical Records</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <thead>
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Diagnosis</th>
                                                            <th>Doctor</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($medical_records as $record): ?>
                                                            <tr>
                                                                <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                                <td><?php echo substr($record['diagnosis'], 0, 50) . (strlen($record['diagnosis']) > 50 ? '...' : ''); ?></td>
                                                                <td>Dr. <?php echo $record['doctor_first_name'] . ' ' . $record['doctor_last_name']; ?></td>
                                                                <td>
                                                                    <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                                        <i class="fas fa-eye"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <?php if (count($prescriptions) > 0): ?>
                                    <div class="card mt-4">
                                        <div class="card-header">
                                            <h3>Prescriptions</h3>
                                        </div>
                                        <div class="card-body">
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <thead>
                                                        <tr>
                                                            <th>Date</th>
                                                            <th>Doctor</th>
                                                            <th>Status</th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <?php foreach ($prescriptions as $prescription): ?>
                                                            <tr>
                                                                <td><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></td>
                                                                <td>Dr. <?php echo $prescription['doctor_first_name'] . ' ' . $prescription['doctor_last_name']; ?></td>
                                                                <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                                <td>
                                                                    <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                                        <i class="fas fa-eye"></i>
                                                                    </a>
                                                                </td>
                                                            </tr>
                                                        <?php endforeach; ?>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h3>Patient Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="patient-profile">
                                            <div class="patient-image">
                                                <img src="../assets/images/<?php echo $appointment['patient_profile_image']; ?>" alt="Patient">
                                            </div>
                                            <div class="patient-details">
                                                <h4><?php echo $appointment['patient_first_name'] . ' ' . $appointment['patient_last_name']; ?></h4>
                                                <p><i class="fas fa-phone"></i> <?php echo $appointment['patient_phone']; ?></p>
                                                <a href="view_patient.php?id=<?php echo $appointment['patient_id']; ?>" class="btn btn-sm btn-primary mt-2">View Patient Profile</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h3>Doctor Information</h3>
                                    </div>
                                    <div class="card-body">
                                        <div class="doctor-profile">
                                            <div class="doctor-image">
                                                <img src="../assets/images/<?php echo $appointment['doctor_profile_image']; ?>" alt="Doctor">
                                            </div>
                                            <div class="doctor-details">
                                                <h4>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></h4>
                                                <p><i class="fas fa-stethoscope"></i> <?php echo $appointment['specialization']; ?></p>
                                                <p><i class="fas fa-hospital"></i> <?php echo $appointment['department_name']; ?></p>
                                                <p><i class="fas fa-phone"></i> <?php echo $appointment['doctor_phone']; ?></p>
                                                <a href="view_doctor.php?id=<?php echo $appointment['doctor_id']; ?>" class="btn btn-sm btn-primary mt-2">View Doctor Profile</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>

