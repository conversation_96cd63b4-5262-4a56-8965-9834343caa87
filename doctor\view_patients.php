<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if patient ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: patients.php");
    exit();
}

$patient_id = $_GET['id'];

// Get patient details
$patient = null;
$query = "SELECT * FROM patients WHERE patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $patient = $result->fetch_assoc();
} else {
    // Patient not found
    header("Location: patients.php");
    exit();
}

// Check if this doctor has seen this patient
$has_seen_patient = false;
$check_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND patient_id = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("ii", $doctor_id, $patient_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
if ($check_result->fetch_assoc()['count'] > 0) {
    $has_seen_patient = true;
}

// If doctor has not seen this patient, redirect
if (!$has_seen_patient) {
    header("Location: patients.php");
    exit();
}

// Get patient's appointment history with this doctor
$appointments = [];
$appointments_query = "SELECT a.*, dep.department_name
                      FROM appointments a
                      JOIN departments dep ON a.department_id = dep.department_id
                      WHERE a.doctor_id = ? AND a.patient_id = ?
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
$appointments_stmt = $conn->prepare($appointments_query);
$appointments_stmt->bind_param("ii", $doctor_id, $patient_id);
$appointments_stmt->execute();
$appointments_result = $appointments_stmt->get_result();
if ($appointments_result->num_rows > 0) {
    while ($row = $appointments_result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get patient's medical records created by this doctor
$medical_records = [];
$records_query = "SELECT mr.*, a.appointment_date
                 FROM medical_records mr
                 JOIN appointments a ON mr.appointment_id = a.appointment_id
                 WHERE a.doctor_id = ? AND a.patient_id = ?
                 ORDER BY mr.created_at DESC";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param("ii", $doctor_id, $patient_id);
$records_stmt->execute();
$records_result = $records_stmt->get_result();
if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}

// Get patient's prescriptions created by this doctor
$prescriptions = [];
$prescriptions_query = "SELECT p.*, a.appointment_date
                       FROM prescriptions p
                       JOIN appointments a ON p.appointment_id = a.appointment_id
                       WHERE a.doctor_id = ? AND a.patient_id = ?
                       ORDER BY p.created_at DESC";
$prescriptions_stmt = $conn->prepare($prescriptions_query);
$prescriptions_stmt->bind_param("ii", $doctor_id, $patient_id);
$prescriptions_stmt->execute();
$prescriptions_result = $prescriptions_stmt->get_result();
if ($prescriptions_result->num_rows > 0) {
    while ($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Profile | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> Patient Profile</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Patient Information</h3>
                    <div class="actions">
                        <a href="patients.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Patients</a>
                        <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-primary"><i class="fas fa-calendar-plus"></i> Schedule Appointment</a>
                    </div>
                </div>

                <div class="patient-profile">
                    <!-- Patient Information Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Personal Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="patient-info">
                                <div class="patient-image">
                                    <img src="../assets/images/<?php echo $patient['profile_image']; ?>" alt="Patient">
                                </div>
                                <div class="patient-details">
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">Patient ID:</span>
                                            <span class="detail-value"><?php echo $patient['patient_id']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Full Name:</span>
                                            <span class="detail-value"><?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Gender:</span>
                                            <span class="detail-value"><?php echo ucfirst($patient['gender']); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Date of Birth:</span>
                                            <span class="detail-value"><?php echo date('F d, Y', strtotime($patient['date_of_birth'])); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Age:</span>
                                            <span class="detail-value"><?php echo date_diff(date_create($patient['date_of_birth']), date_create('today'))->y; ?> years</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Blood Type:</span>
                                            <span class="detail-value"><?php echo $patient['blood_type']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Phone:</span>
                                            <span class="detail-value"><?php echo $patient['phone']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Email:</span>
                                            <span class="detail-value"><?php echo $patient['email']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Address:</span>
                                            <span class="detail-value"><?php echo $patient['address']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Emergency Contact:</span>
                                            <span class="detail-value"><?php echo $patient['emergency_contact_name']; ?> (<?php echo $patient['emergency_contact_phone']; ?>)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Information Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Medical Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="details-grid">
                                <div class="detail-item full-width">
                                    <span class="detail-label">Allergies:</span>
                                    <span class="detail-value"><?php echo $patient['allergies'] ? $patient['allergies'] : 'None'; ?></span>
                                </div>
                                <div class="detail-item full-width">
                                    <span class="detail-label">Chronic Conditions:</span>
                                    <span class="detail-value"><?php echo $patient['chronic_conditions'] ? $patient['chronic_conditions'] : 'None'; ?></span>
                                </div>
                                <div class="detail-item full-width">
                                    <span class="detail-label">Current Medications:</span>
                                    <span class="detail-value"><?php echo $patient['current_medications'] ? $patient['current_medications'] : 'None'; ?></span>
                                </div>
                                <div class="detail-item full-width">
                                    <span class="detail-label">Past Surgeries:</span>
                                    <span class="detail-value"><?php echo $patient['past_surgeries'] ? $patient['past_surgeries'] : 'None'; ?></span>
                                </div>
                                <div class="detail-item full-width">
                                    <span class="detail-label">Family Medical History:</span>
                                    <span class="detail-value"><?php echo $patient['family_medical_history'] ? $patient['family_medical_history'] : 'None'; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Appointment History Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Appointment History</h3>
                            <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-sm btn-primary">Schedule New</a>
                        </div>
                        <div class="card-body">
                            <?php if (count($appointments) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Time</th>
                                                <th>Department</th>
                                                <th>Reason</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($appointments as $appointment): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                    <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                    <td><?php echo $appointment['department_name']; ?></td>
                                                    <td><?php echo $appointment['reason']; ?></td>
                                                    <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                    <td>
                                                        <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-calendar-times empty-icon"></i>
                                    <p>No appointment history found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Medical Records Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Medical Records</h3>
                            <a href="medical_records.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-sm btn-outline">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (count($medical_records) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Diagnosis</th>
                                                <th>Treatment</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($medical_records as $record): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                    <td><?php echo $record['diagnosis']; ?></td>
                                                    <td><?php echo $record['treatment']; ?></td>
                                                    <td>
                                                        <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-file-medical empty-icon"></i>
                                    <p>No medical records found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Prescriptions Card -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Prescriptions</h3>
                            <a href="prescriptions.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-sm btn-outline">View All</a>
                        </div>
                        <div class="card-body">
                            <?php if (count($prescriptions) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Medication</th>
                                                <th>Dosage</th>
                                                <th>Duration</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($prescriptions as $prescription):
                                                // Get first medication for this prescription
                                                $med_query = "SELECT * FROM prescription_medications WHERE prescription_id = ? LIMIT 1";
                                                $med_stmt = $conn->prepare($med_query);
                                                $med_stmt->bind_param("s", $prescription['prescription_id']);
                                                $med_stmt->execute();
                                                $med_result = $med_stmt->get_result();
                                                $medication = $med_result->fetch_assoc();
                                            ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($prescription['created_at'])); ?></td>
                                                    <td><?php echo $medication ? $medication['medication_name'] : 'N/A'; ?></td>
                                                    <td><?php echo $medication ? $medication['dosage'] : 'N/A'; ?></td>
                                                    <td><?php echo $medication ? $medication['duration'] : 'N/A'; ?></td>
                                                    <td>
                                                        <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="print_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Print">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-prescription empty-icon"></i>
                                    <p>No prescriptions found.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
