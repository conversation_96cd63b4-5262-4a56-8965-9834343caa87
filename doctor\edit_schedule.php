<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

// Check if patient_capacity column exists in doctor_schedules table
$check_column_query = "SHOW COLUMNS FROM doctor_schedules LIKE 'patient_capacity'";
$check_column_result = $conn->query($check_column_query);
$capacity_column_exists = $check_column_result->num_rows > 0;

// If the column doesn't exist, add it
if (!$capacity_column_exists) {
    $add_column_query = "ALTER TABLE doctor_schedules ADD COLUMN patient_capacity INT DEFAULT 10 COMMENT 'Maximum number of patients for this time slot'";
    $conn->query($add_column_query);
}

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if schedule ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: schedule.php");
    exit();
}

$schedule_id = $_GET['id'];

// Get schedule details
$schedule = null;
$query = "SELECT * FROM doctor_schedules WHERE schedule_id = ? AND doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $schedule_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $schedule = $result->fetch_assoc();
} else {
    // Schedule not found or doesn't belong to this doctor
    header("Location: schedule.php");
    exit();
}

// Get days of week for display
$days_of_week = [
    1 => 'Monday',
    2 => 'Tuesday',
    3 => 'Wednesday',
    4 => 'Thursday',
    5 => 'Friday',
    6 => 'Saturday',
    7 => 'Sunday'
];

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $day_of_week = $_POST['day_of_week'];
    $start_time = $_POST['start_time'];
    $end_time = $_POST['end_time'];
    $patient_capacity = $_POST['patient_capacity'];
    $is_active = isset($_POST['is_active']) ? 1 : 0;

    // Validate inputs
    if (empty($day_of_week) || empty($start_time) || empty($end_time) || empty($patient_capacity)) {
        $error_message = "Please fill in all required fields.";
    } elseif (strtotime($start_time) >= strtotime($end_time)) {
        $error_message = "End time must be after start time.";
    } elseif (!is_numeric($patient_capacity) || $patient_capacity < 1 || $patient_capacity > 100) {
        $error_message = "Patient capacity must be a number between 1 and 100.";
    } else {
        // Check for overlapping schedules (excluding this schedule)
        $check_query = "SELECT * FROM doctor_schedules
                        WHERE doctor_id = ? AND day_of_week = ? AND schedule_id != ? AND
                        ((start_time <= ? AND end_time > ?) OR
                         (start_time < ? AND end_time >= ?) OR
                         (start_time >= ? AND end_time <= ?))";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("iiissssss", $doctor_id, $day_of_week, $schedule_id, $end_time, $start_time, $end_time, $start_time, $start_time, $end_time);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error_message = "This time slot overlaps with an existing schedule. Please choose a different time.";
        } else {
            // Update schedule
            $update_query = "UPDATE doctor_schedules SET
                            day_of_week = ?,
                            start_time = ?,
                            end_time = ?,
                            patient_capacity = ?,
                            is_active = ?,
                            updated_at = NOW()
                            WHERE schedule_id = ? AND doctor_id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("issiiiii", $day_of_week, $start_time, $end_time, $patient_capacity, $is_active, $schedule_id, $doctor_id);

            if ($update_stmt->execute()) {
                $success_message = "Schedule updated successfully!";
                // Refresh schedule data
                $stmt->execute();
                $result = $stmt->get_result();
                $schedule = $result->fetch_assoc();
            } else {
                $error_message = "Error updating schedule: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Schedule | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li class="active">
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-edit"></i> Edit Schedule</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Edit Schedule Slot</h3>
                    <div class="actions">
                        <a href="schedule.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Schedule</a>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-body">
                        <form action="edit_schedule.php?id=<?php echo $schedule_id; ?>" method="post">
                            <div class="form-group">
                                <label for="day_of_week">Day of Week <span class="required">*</span></label>
                                <select name="day_of_week" id="day_of_week" class="form-control" required>
                                    <?php foreach ($days_of_week as $day_num => $day_name): ?>
                                        <option value="<?php echo $day_num; ?>" <?php echo ($day_num == $schedule['day_of_week']) ? 'selected' : ''; ?>>
                                            <?php echo $day_name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="start_time">Start Time <span class="required">*</span></label>
                                    <input type="time" name="start_time" id="start_time" class="form-control" value="<?php echo $schedule['start_time']; ?>" required>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="end_time">End Time <span class="required">*</span></label>
                                    <input type="time" name="end_time" id="end_time" class="form-control" value="<?php echo $schedule['end_time']; ?>" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="patient_capacity">Patient Capacity <span class="required">*</span></label>
                                <input type="number" name="patient_capacity" id="patient_capacity" class="form-control"
                                       value="<?php echo isset($schedule['patient_capacity']) ? $schedule['patient_capacity'] : 10; ?>"
                                       min="1" max="100" required>
                                <small class="form-text text-muted">Maximum number of patients for this time slot (1-100).</small>
                            </div>

                            <div class="form-group">
                                <div class="checkbox">
                                    <input type="checkbox" name="is_active" id="is_active" <?php echo $schedule['is_active'] ? 'checked' : ''; ?>>
                                    <label for="is_active">Active</label>
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Update Schedule</button>
                                <a href="schedule.php" class="btn btn-outline">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
