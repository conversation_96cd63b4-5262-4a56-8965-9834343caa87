-- SQL script to add medical fields to the patients table
-- Add columns for allergies, current_medications, and medical_history

-- Check if the allergies column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'patients' AND COLUMN_NAME = 'allergies';

-- Add allergies column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE patients ADD COLUMN allergies TEXT DEFAULT NULL COMMENT "Patient allergies"',
    'SELECT "Column allergies already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the current_medications column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'patients' AND COLUMN_NAME = 'current_medications';

-- Add current_medications column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE patients ADD COLUMN current_medications TEXT DEFAULT NULL COMMENT "Patient current medications"',
    'SELECT "Column current_medications already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the medical_history column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'patients' AND COLUMN_NAME = 'medical_history';

-- Add medical_history column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE patients ADD COLUMN medical_history TEXT DEFAULT NULL COMMENT "Patient medical history"',
    'SELECT "Column medical_history already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
