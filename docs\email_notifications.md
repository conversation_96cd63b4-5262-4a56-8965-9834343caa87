# Email Notifications System

This document provides information about the email notification system implemented in the Hospital Management System.

## Overview

The system sends email notifications to patients for:
1. Appointment confirmations - when a new appointment is created
2. Appointment reminders - sent before the scheduled appointment date

## Configuration

### Email Settings

Email settings are configured in `includes/config.php`. The following settings can be customized:

```php
// Email Configuration
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_SECURE', 'tls');
define('SMTP_AUTH', true);
define('SMTP_USERNAME', '<EMAIL>'); // Replace with your actual email
define('SMTP_PASSWORD', 'your-app-password'); // Replace with your app password
define('SMTP_FROM_EMAIL', '<EMAIL>'); // Replace with your actual email
define('SMTP_FROM_NAME', 'CSUCC Hospital');

// Appointment Configuration
define('APPOINTMENT_CONFIRMATION_ENABLED', true);
define('APPOINTMENT_REMINDER_ENABLED', true);
define('APPOINTMENT_REMINDER_HOURS', 24); // Send reminder 24 hours before appointment
```

### Email Templates

Email templates are stored in the `includes/email_templates` directory:
- `appointment_confirmation.php` - Template for appointment confirmation emails
- `appointment_reminder.php` - Template for appointment reminder emails

These templates use variables passed from the Mailer class to populate the email content.

## Database Structure

The appointments table has been updated with the following columns to track email notifications:

- `confirmation_sent` (TINYINT) - Flag indicating if confirmation email was sent (0 = not sent, 1 = sent)
- `confirmation_sent_date` (DATETIME) - Date and time when confirmation email was sent
- `reminder_sent` (TINYINT) - Flag indicating if reminder email was sent (0 = not sent, 1 = sent)
- `reminder_sent_date` (DATETIME) - Date and time when reminder email was sent

To update your database with these new columns, run the SQL script:
```
sql/update_appointments_table.sql
```

## Sending Emails

### Appointment Confirmations

Appointment confirmation emails are automatically sent when:
1. A patient books an appointment through the patient portal
2. A doctor creates an appointment for a patient

The system uses the `sendAppointmentConfirmation()` method in the `Mailer` class.

### Appointment Reminders

Appointment reminder emails are sent using a scheduled task (cron job). The script `cron/send_appointment_reminders.php` handles this process.

To set up the cron job to run daily (e.g., at 8 AM):

**Linux/Unix:**
```
0 8 * * * php /path/to/hospital_management_system/cron/send_appointment_reminders.php
```

**Windows (Task Scheduler):**
1. Create a new task in Task Scheduler
2. Set the trigger to run daily at 8 AM
3. Set the action to start a program: `php.exe`
4. Add arguments: `C:\path\to\hospital_management_system\cron\send_appointment_reminders.php`

## Testing Email Functionality

To test the email functionality:

1. Update the email configuration in `includes/config.php` with valid SMTP settings
2. Book a new appointment as a patient or create one as a doctor
3. Check if the confirmation email is received
4. Run the reminder script manually to test reminder emails:
   ```
   php cron/send_appointment_reminders.php
   ```

## Troubleshooting

If emails are not being sent:

1. Check the email configuration in `includes/config.php`
2. Verify that PHPMailer is installed (or the system will fall back to PHP's mail function)
3. Check server logs for any error messages
4. Ensure the SMTP credentials are correct
5. If using Gmail, make sure you're using an app password if 2FA is enabled

## Customizing Email Templates

To customize the email templates:

1. Edit the files in `includes/email_templates/`
2. You can modify the HTML structure, CSS styles, and text content
3. Make sure to keep the PHP variables (e.g., `$patient_name`, `$appointment_date`) intact

## Disabling Email Notifications

To disable email notifications:

1. Set `APPOINTMENT_CONFIRMATION_ENABLED` to `false` in `includes/config.php` to disable confirmation emails
2. Set `APPOINTMENT_REMINDER_ENABLED` to `false` in `includes/config.php` to disable reminder emails
3. Stop the cron job if you've set it up
