<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is a patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: medical_records.php");
    exit();
}

$record_id = $_GET['id'];

// Get medical record details
$record = null;
$query = "SELECT mr.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          d.specialization, d.profile_image as doctor_profile_image,
          a.appointment_date, a.appointment_time, a.appointment_id,
          dep.department_name
          FROM medical_records mr
          JOIN doctors d ON mr.doctor_id = d.doctor_id
          JOIN departments dep ON d.department_id = dep.department_id
          LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
          WHERE mr.record_id = ? AND mr.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $record_id, $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $record = $result->fetch_assoc();
} else {
    header("Location: medical_records.php");
    exit();
}

// Get prescriptions related to this medical record
$prescriptions = [];
$prescriptions_query = "SELECT * FROM prescriptions WHERE record_id = ?";
$prescriptions_stmt = $conn->prepare($prescriptions_query);
$prescriptions_stmt->bind_param("i", $record_id);
$prescriptions_stmt->execute();
$prescriptions_result = $prescriptions_stmt->get_result();
if ($prescriptions_result->num_rows > 0) {
    while ($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}

// Get hospital information
$hospital_name = "CSUCC Hospital";
$hospital_address = "123 University Avenue, Caraga, Philippines";
$hospital_phone = "+63 (85) 123-4567";
$hospital_email = "<EMAIL>";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Record #<?php echo $record_id; ?> | CSUCC Hospital</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <button class="print-button" onclick="window.print()">
        <i class="fas fa-print"></i> Print Record
    </button>

    <div class="print-container">
        <div class="print-header">
            <div class="hospital-info">
                <h1><?php echo $hospital_name; ?></h1>
                <p><?php echo $hospital_address; ?></p>
                <p>Phone: <?php echo $hospital_phone; ?> | Email: <?php echo $hospital_email; ?></p>
            </div>
            <div class="record-info">
                <h2>Medical Record #<?php echo $record_id; ?></h2>
                <p>Date: <?php echo date('F d, Y', strtotime($record['created_at'])); ?></p>
                <p>Time: <?php echo date('h:i A', strtotime($record['created_at'])); ?></p>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Patient Information</div>
            <div class="section-content">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Name:</span>
                        <span class="info-value"><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Date of Birth:</span>
                        <span class="info-value"><?php echo date('M d, Y', strtotime($patient_info['date_of_birth'])); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Gender:</span>
                        <span class="info-value"><?php echo ucfirst($patient_info['gender']); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Blood Type:</span>
                        <span class="info-value"><?php echo $patient_info['blood_type']; ?></span>
                    </div>
                    <div class="info-item full-width">
                        <span class="info-label">Allergies:</span>
                        <span class="info-value"><?php echo !empty($patient_info['allergies']) ? $patient_info['allergies'] : 'None'; ?></span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <div class="section-title">Doctor Information</div>
            <div class="section-content">
                <div class="info-grid">
                    <div class="info-item">
                        <span class="info-label">Doctor:</span>
                        <span class="info-value">Dr. <?php echo $record['doctor_first_name'] . ' ' . $record['doctor_last_name']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Specialization:</span>
                        <span class="info-value"><?php echo $record['specialization']; ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Department:</span>
                        <span class="info-value"><?php echo $record['department_name']; ?></span>
                    </div>
                </div>
            </div>
        </div>

        <?php if (!empty($record['appointment_id'])): ?>
        <div class="section">
            <div class="section-title">Appointment Information</div>
            <div class="section-content">
                <div class="info-grid">

                    <div class="info-item">
                        <span class="info-label">Date:</span>
                        <span class="info-value"><?php echo date('M d, Y', strtotime($record['appointment_date'])); ?></span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">Time:</span>
                        <span class="info-value"><?php echo date('h:i A', strtotime($record['appointment_time'])); ?></span>
                    </div>
                </div>
            </div>
        </div>
        <?php endif; ?>

        <div class="section">
            <div class="section-title">Medical Details</div>
            <div class="section-content">
                <div class="info-item full-width">
                    <span class="info-label">Diagnosis:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['diagnosis'])); ?></span>
                </div>

                <div class="info-item full-width">
                    <span class="info-label">Symptoms:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['symptoms'])); ?></span>
                </div>

                <div class="info-item full-width">
                    <span class="info-label">Treatment:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['treatment'])); ?></span>
                </div>

                <?php if (!empty($record['vital_signs'])): ?>
                <div class="info-item full-width">
                    <span class="info-label">Vital Signs:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['vital_signs'])); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($record['lab_results'])): ?>
                <div class="info-item full-width">
                    <span class="info-label">Lab Results:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['lab_results'])); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($record['notes'])): ?>
                <div class="info-item full-width">
                    <span class="info-label">Additional Notes:</span>
                    <span class="info-value"><?php echo nl2br(htmlspecialchars($record['notes'])); ?></span>
                </div>
                <?php endif; ?>

                <?php if (!empty($record['follow_up_date'])): ?>
                <div class="info-item">
                    <span class="info-label">Follow-up Date:</span>
                    <span class="info-value"><?php echo date('M d, Y', strtotime($record['follow_up_date'])); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <?php if (count($prescriptions) > 0): ?>
        <div class="section">
            <div class="section-title">Prescriptions</div>
            <div class="section-content">
                <?php foreach ($prescriptions as $prescription): ?>
                <div class="prescription-item">
                    <div class="prescription-header">
                        <span class="prescription-title">Prescription #<?php echo $prescription['prescription_id']; ?></span>
                        <span class="prescription-date"><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></span>
                    </div>
                    <div class="prescription-content">
                        <?php echo nl2br(htmlspecialchars($prescription['medication'])); ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <div class="footer">
            <p>This is an official medical record from <?php echo $hospital_name; ?>.</p>
            <p>Printed on: <?php echo date('F d, Y h:i A'); ?></p>
        </div>
    </div>
</body>
</html>
