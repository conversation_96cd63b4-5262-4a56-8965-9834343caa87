<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor ID
$doctor_id = 0;
$doctor_query = "SELECT doctor_id FROM doctors WHERE user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_id = $doctor_result->fetch_assoc()['doctor_id'];
}

// Check if filtering by patient
$patient_id = isset($_GET['patient_id']) ? intval($_GET['patient_id']) : 0;
$patient_name = '';

if ($patient_id > 0) {
    $patient_query = "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM patients WHERE patient_id = ?";
    $patient_stmt = $conn->prepare($patient_query);
    $patient_stmt->bind_param("i", $patient_id);
    $patient_stmt->execute();
    $patient_result = $patient_stmt->get_result();
    if ($patient_result->num_rows > 0) {
        $patient_name = $patient_result->fetch_assoc()['full_name'];
    }
}

// Get all prescriptions for this doctor
$prescriptions = [];
$query = "SELECT p.*, 
        pt.first_name as patient_first_name, pt.last_name as patient_last_name
        FROM prescriptions p
        JOIN patients pt ON p.patient_id = pt.patient_id
        WHERE p.doctor_id = ?";

// Add patient filter if specified
if ($patient_id > 0) {
    $query .= " AND p.patient_id = ?";
}

$query .= " ORDER BY p.created_at DESC";

$stmt = $conn->prepare($query);

if ($patient_id > 0) {
    $stmt->bind_param("ii", $doctor_id, $patient_id);
} else {
    $stmt->bind_param("i", $doctor_id);
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prescriptions | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li class="active">
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-prescription"></i> Prescriptions</h2>
                    <?php if ($patient_name): ?>
                        <span class="subtitle">Patient: <?php echo $patient_name; ?></span>
                    <?php endif; ?>
                </div>
                <div class="header-right">
                    <a href="add_prescription.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Prescription
                    </a>
                </div>
            </header>
            
            <div class="dashboard-content">
                <div class="card">
                    <div class="card-header">
                        <h3>All Prescriptions</h3>
                        <div class="card-tools">
                            <input type="text" id="searchInput" placeholder="Search..." class="search-input">
                        </div>
                    </div>
                    <div class="card-body">
                        <?php if (count($prescriptions) > 0): ?>
                            <div class="table-responsive">
                                <table class="table" id="prescriptionsTable">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Patient</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($prescriptions as $prescription): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></td>
                                                <td><?php echo $prescription['patient_first_name'] . ' ' . $prescription['patient_last_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                <td>
                                                    <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <a href="delete_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this prescription?');">
                                                        <i class="fas fa-trash"></i>
                                                    </a>
                                                    <a href="print_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon btn-success" title="Print">
                                                        <i class="fas fa-print"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-prescription empty-icon"></i>
                                <p>No prescriptions found.</p>
                                <a href="add_prescription.php" class="btn btn-primary">Add Prescription</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script>
        // Search functionality
        document.getElementById('searchInput').addEventListener('keyup', function() {
            const searchValue = this.value.toLowerCase();
            const table = document.getElementById('prescriptionsTable');
            const rows = table.getElementsByTagName('tr');
            
            for (let i = 1; i < rows.length; i++) {
                let found = false;
                const cells = rows[i].getElementsByTagName('td');
                
                for (let j = 0; j < cells.length; j++) {
                    const cellText = cells[j].innerText.toLowerCase();
                    
                    if (cellText.indexOf(searchValue) > -1) {
                        found = true;
                        break;
                    }
                }
                
                if (found) {
                    rows[i].style.display = '';
                } else {
                    rows[i].style.display = 'none';
                }
            }
        });
    </script>
</body>
</html>