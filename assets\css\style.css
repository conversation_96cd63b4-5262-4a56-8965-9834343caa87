/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f7fb;
  color: #333;
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: #1e88e5;
}

ul {
  list-style: none;
}

/* Container Styles */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Login and Register Styles */
.login-container,
.register-container {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
  width: 100%;
  max-width: 500px;
  margin: 0 auto;
}

.login-header,
.register-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-header h2,
.register-header h2 {
  color: #1e88e5;
  margin-bottom: 10px;
}

.login-form,
.register-form {
  margin-top: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  border-color: #1e88e5;
  outline: none;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.form-footer {
  margin-top: 20px;
  text-align: center;
}

.form-footer a {
  color: #1e88e5;
}

.form-footer a:hover {
  text-decoration: underline;
}

/* Button Styles */
.btn {
  display: inline-block;
  padding: 10px 20px;
  background-color: #1e88e5;
  color: #fff;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s, transform 0.2s;
}

.btn:hover {
  background-color: #1565c0;
  transform: translateY(-2px);
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-primary {
  background-color: #1e88e5;
  color: #fff;
}

.btn-secondary {
  background-color: #757575;
  color: #fff;
}

.btn-success {
  background-color: #4caf50;
  color: #fff;
}

.btn-danger {
  background-color: #f44336;
  color: #fff;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid #1e88e5;
  color: #1e88e5;
}

.btn-outline:hover {
  background-color: #1e88e5;
  color: #fff;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 14px;
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #f5f5f5;
  color: #333;
  transition: background-color 0.3s;
}

.btn-icon:hover {
  background-color: #e0e0e0;
}

.btn-icon.btn-danger {
  color: #f44336;
}

.btn-icon.btn-danger:hover {
  background-color: #ffebee;
}

/* Alert Styles */
.alert {
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.alert i {
  margin-right: 10px;
  font-size: 18px;
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.alert-danger {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

.alert-warning {
  background-color: #fff8e1;
  color: #f57f17;
  border: 1px solid #ffecb3;
}

.alert-info {
  background-color: #e3f2fd;
  color: #0d47a1;
  border: 1px solid #bbdefb;
}

/* Dashboard Styles */
.dashboard-container {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background-color: #1e3a56;
  color: #fff;
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  transition: all 0.3s;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h3 {
  color: #fff;
  display: flex;
  align-items: center;
  gap: 10px;
}

.sidebar-nav ul {
  padding: 20px 0;
}

.sidebar-nav li {
  margin-bottom: 5px;
}

.sidebar-nav a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.7);
  transition: all 0.3s;
}

.sidebar-nav a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.sidebar-nav a:hover,
.sidebar-nav li.active a {
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  border-left: 4px solid #1e88e5;
}

.main-content {
  flex: 1;
  margin-left: 250px;
  padding: 20px;
  transition: all 0.3s;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #eee;
}

.header h2 {
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header h2 i {
  color: #1e88e5;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.user-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.user-image-sm {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  object-fit: cover;
}

.user-details h4 {
  font-size: 16px;
  margin-bottom: 2px;
}

.user-details p {
  font-size: 14px;
  color: #666;
}

.dashboard-content {
  padding: 20px 0;
}

/* Welcome Banner */
.welcome-banner {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 30px;
  margin-bottom: 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h2 {
  margin-bottom: 10px;
  color: #333;
}

.welcome-content p {
  color: #666;
}

/* Stats Container */
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card-icon {
  width: 60px;
  height: 60px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.doctor-icon {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.patient-icon {
  background-color: #e8f5e9;
  color: #4caf50;
}

.appointment-icon {
  background-color: #fff8e1;
  color: #ffa000;
}

.department-icon {
  background-color: #f3e5f5;
  color: #9c27b0;
}

.completed-icon {
  background-color: #e8f5e9;
  color: #4caf50;
}

.pending-icon {
  background-color: #fff8e1;
  color: #ffa000;
}

.upcoming-icon {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.cancelled-icon {
  background-color: #ffebee;
  color: #f44336;
}

.stat-card-info h3 {
  font-size: 24px;
  margin-bottom: 5px;
}

.stat-card-info p {
  color: #666;
  font-size: 14px;
}

/* Card Styles */
.card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  margin-bottom: 30px;
  overflow: hidden;
}

.card-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.card-header h3 {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
}

.card-header h3 i {
  color: #1e88e5;
}

.card-body {
  padding: 20px;
}

/* Table Styles */
.table-responsive {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.table th {
  background-color: #f9f9f9;
  font-weight: 600;
}

.table tbody tr:hover {
  background-color: #f5f5f5;
}

.status-badge {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.status-pending {
  background-color: #fff8e1;
  color: #ffa000;
}

.status-confirmed {
  background-color: #e3f2fd;
  color: #1e88e5;
}

.status-completed {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-cancelled {
  background-color: #ffebee;
  color: #f44336;
}

.status-no-show {
  background-color: #f5f5f5;
  color: #757575;
}

.status-active {
  background-color: #e8f5e9;
  color: #4caf50;
}

.status-inactive {
  background-color: #f5f5f5;
  color: #757575;
}

.status-on-leave {
  background-color: #fff8e1;
  color: #ffa000;
}

.date-badge {
  background-color: #e3f2fd;
  color: #1e88e5;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.empty-icon {
  font-size: 48px;
  color: #bdbdbd;
  margin-bottom: 20px;
}

.empty-state p {
  color: #757575;
  margin-bottom: 20px;
}

/* Charts Container */
.charts-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.chart-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.chart-header {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.chart-header h3 {
  font-size: 18px;
}

.chart-body {
  padding: 20px;
  height: 300px;
  position: relative;
}

/* Section Styles */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.section-header h3 i {
  color: #1e88e5;
}

/* Quick Actions */
.quick-actions-section {
  margin-bottom: 30px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
}

.quick-action-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s;
  color: #333;
}

.quick-action-card:hover {
  transform: translateY(-5px);
  background-color: #f5f7fb;
}

.quick-action-icon {
  width: 50px;
  height: 50px;
  border-radius: 10px;
  background-color: #e3f2fd;
  color: #1e88e5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.quick-action-text h4 {
  margin-bottom: 5px;
}

.quick-action-text p {
  color: #666;
  font-size: 14px;
}

/* Footer */
.footer {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 14px;
  margin-top: auto;
}

/* Responsive Styles */
@media (max-width: 992px) {
  .sidebar {
    width: 70px;
  }

  .sidebar-header h3 span {
    display: none;
  }

  .sidebar-nav a span {
    display: none;
  }

  .sidebar-nav a {
    justify-content: center;
    padding: 15px;
  }

  .sidebar-nav a i {
    margin-right: 0;
    font-size: 18px;
  }

  .main-content {
    margin-left: 70px;
  }

  .form-row {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .welcome-banner {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .stats-container {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .charts-container {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .user-info {
    width: 100%;
    justify-content: flex-end;
  }
}

@media (max-width: 576px) {
  .main-content {
    margin-left: 0;
    padding: 10px;
  }

  .sidebar {
    width: 0;
    overflow: hidden;
  }

  .stats-container {
    grid-template-columns: 1fr;
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .table th,
  .table td {
    padding: 8px;
  }
}

/* Reports Page Specific Styles */
.content-wrapper {
  padding: 0 0 20px 0;
}

/* Filter Form Styles */
.filter-form {
  margin-bottom: 20px;
}

.filter-row {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.filter-form .form-group {
  flex: 1;
  min-width: 200px;
}

.filter-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-container .filter-form {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-container .form-group {
  margin-bottom: 0;
  min-width: 180px;
}

.filter-form label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.filter-form select,
.filter-form input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: #fff;
}

.filter-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.filter-actions .btn {
  padding: 6px 12px;
}

.form-actions {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

/* Stats Grid for Reports */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  align-items: center;
}

.stat-card-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  color: white;
}

.stat-card-icon.primary {
  background-color: #1e3a56;
}
.stat-card-icon.success {
  background-color: #4caf50;
}
.stat-card-icon.warning {
  background-color: #ffc107;
}
.stat-card-icon.danger {
  background-color: #f44336;
}
.stat-card-icon.info {
  background-color: #2196f3;
}
.stat-card-icon.secondary {
  background-color: #9e9e9e;
}

.stat-card-info h4 {
  font-size: 24px;
  margin: 0;
  font-weight: 600;
}

.stat-card-info p {
  margin: 5px 0 0;
  color: #666;
}

/* Charts Grid for Reports */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-container {
  position: relative;
  height: 300px;
  width: 100%;
}

/* Summary Stats for Reports */
.summary-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.summary-item {
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 15px;
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 180px;
}

.summary-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  font-size: 20px;
  background-color: #1e3a56;
  color: white;
}

.summary-details {
  flex: 1;
}

.summary-value {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 5px;
}

.summary-label {
  color: #666;
  font-size: 14px;
}

/* Export Options */
.export-options {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  display: none;
  min-width: 160px;
  padding: 8px 0;
  margin: 2px 0 0;
  background-color: #fff;
  border-radius: 5px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 15px;
  color: #333;
  transition: background-color 0.3s;
}

.dropdown-item:hover {
  background-color: #f5f7fb;
}

/* Print Styles for Reports */
@media print {
  .sidebar,
  .header-right,
  .filter-form,
  .form-actions,
  .dropdown {
    display: none !important;
  }

  .main-content {
    margin-left: 0 !important;
    padding: 0 !important;
  }

  .card {
    box-shadow: none !important;
    border: 1px solid #ddd !important;
    margin-bottom: 20px !important;
    break-inside: avoid;
  }

  .charts-grid {
    grid-template-columns: 1fr !important;
  }

  .chart-container {
    page-break-inside: avoid;
    height: 250px !important;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Breadcrumb Styles */
.breadcrumb {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  margin-bottom: 20px;
}

.breadcrumb a {
  color: #1e88e5;
}

.breadcrumb span {
  margin: 0 8px;
}

/* Page Header Styles */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.page-header h1 {
  font-size: 24px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.page-header h1 i {
  color: #1e88e5;
}

/* Chart Title Styles */
.chart-title {
  font-size: 18px;
  margin-bottom: 15px;
  color: #333;
  font-weight: 600;
}

/* Report Info Styles */
.report-info {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.report-summary {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
  color: #666;
  font-size: 14px;
}

/* Header Left/Right Styles */
.header-left,
.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Patient Profile Styles */
.patient-profile {
  padding: 10px;
}

.patient-profile-header {
  display: flex;
  margin-bottom: 30px;
}

.patient-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30px;
  border: 5px solid #f5f7fb;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.patient-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.patient-info {
  flex: 1;
}

.patient-info h2 {
  margin-bottom: 5px;
  color: #1e3a56;
}

.patient-id {
  color: #666;
  margin-bottom: 15px;
  font-size: 14px;
}

.info-label {
  display: inline-block;
  width: 80px;
  color: #666;
}

.info-label i {
  width: 20px;
  color: #1e88e5;
}

.patient-details {
  margin-top: 20px;
}

.details-section {
  margin-bottom: 30px;
}

.details-section h3 {
  font-size: 18px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #1e3a56;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.detail-item {
  margin-bottom: 15px;
}

.detail-label {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
  color: #666;
}

.detail-value {
  display: block;
  color: #333;
}

.text-muted {
  color: #666;
  font-size: 0.9em;
}

/* Form Section Styles */
.form-section {
  margin-bottom: 30px;
}

.form-section h4 {
  font-size: 18px;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #1e3a56;
}

/* Profile Image Upload Styles */
.profile-image-upload {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.current-image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  border: 3px solid #f5f7fb;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.current-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-controls {
  flex: 1;
}

.help-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.required {
  color: #f44336;
}

/* Medical Record Information Styling */
.info-sections {
  margin-bottom: 30px;
}

.info-section {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  margin-bottom: 20px;
  overflow: hidden;
  transition: transform 0.2s ease;
}

.info-section:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.section-header {
  background-color: #f8f9fa;
  padding: 12px 15px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
  display: flex;
  align-items: center;
}

.section-header h4 i {
  margin-right: 10px;
  color: #1e88e5;
}

.section-content {
  padding: 15px;
}

/* Table Styling for Information Display */
.info-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 8px;
}

.info-table.full-width {
  border-spacing: 0 12px;
}

.info-table .info-label {
  font-weight: 600;
  color: #495057;
  width: 140px;
  vertical-align: top;
  padding: 8px 10px 8px 0;
}

.info-table .info-value {
  color: #212529;
  background-color: #f8f9fa;
  padding: 8px 12px;
  border-radius: 4px;
  line-height: 1.5;
}

/* Layout for side-by-side sections */
.info-columns {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-top: 20px;
}

.info-columns .info-section {
  flex: 1;
  min-width: 300px;
}

/* Patient Information Section */
.patient-info-section .section-header {
  background-color: #e3f2fd;
}

.patient-info-section .section-header h4 i {
  color: #1976d2;
}

/* Appointment Information Section */
.appointment-info-section .section-header {
  background-color: #fff8e1;
}

.appointment-info-section .section-header h4 i {
  color: #ffa000;
}

/* Record Information Section */
.record-info-section .section-header {
  background-color: #e8f5e9;
}

.record-info-section .section-header h4 i {
  color: #4caf50;
}

/* Medical Details Section */
.medical-details-section .section-header {
  background-color: #f3e5f5;
}

.medical-details-section .section-header h4 i {
  color: #9c27b0;
}

/* Prescription styling */
.prescription-card {
  margin-bottom: 15px;
  border-left: 4px solid #3498db;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .patient-profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .patient-image {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .info-label {
    width: auto;
    margin-right: 10px;
  }

  .details-grid {
    grid-template-columns: 1fr;
  }

  .profile-image-upload {
    flex-direction: column;
    align-items: flex-start;
  }

  .current-image {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .info-columns {
    flex-direction: column;
  }

  .info-table .info-label {
    width: 120px;
  }
}

/* Medical Info Section Styles */
.medical-info-section {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin: 10px 0;
}

.medical-info-section .detail-item {
  margin-bottom: 15px;
  border-bottom: 1px solid #eee;
  padding-bottom: 15px;
}

.medical-info-section .detail-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.medical-info-section .detail-label {
  font-weight: bold;
  color: #4a7c59;
  display: block;
  margin-bottom: 5px;
}

.medical-info-section .detail-value {
  display: block;
  white-space: pre-line;
}

/* Print Styles */
.print-container {
  max-width: 800px;
  margin: 20px auto;
  background-color: #fff;
  box-shadow: 0 0 10px rgba(0,0,0,0.1);
  padding: 30px;
}

.print-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2px solid #1e3a56;
  padding-bottom: 20px;
  margin-bottom: 20px;
}

.hospital-info {
  flex: 1;
}

.hospital-info h1 {
  color: #1e3a56;
  margin: 0 0 5px 0;
  font-size: 24px;
}

.hospital-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.record-info {
  text-align: right;
}

.record-info h2 {
  color: #1e3a56;
  margin: 0 0 5px 0;
  font-size: 18px;
}

.record-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.section {
  margin-bottom: 25px;
}

.section-title {
  background-color: #f5f7fb;
  padding: 10px 15px;
  border-left: 4px solid #1e3a56;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.section-content {
  padding: 0 15px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.info-item {
  margin-bottom: 10px;
}

.info-item .info-label {
  font-weight: 600;
  color: #555;
  display: block;
  margin-bottom: 3px;
  font-size: 14px;
}

.info-item .info-value {
  color: #333;
}

.full-width {
  grid-column: 1 / -1;
}

.prescription-item {
  border-left: 3px solid #1e88e5;
  padding: 10px 15px;
  margin-bottom: 15px;
  background-color: #f8f9fa;
}

.prescription-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.prescription-title {
  font-weight: 600;
  color: #1e3a56;
}

.prescription-date {
  color: #666;
  font-size: 14px;
}

.prescription-content {
  margin-top: 10px;
}

.footer {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #eee;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.print-button {
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #1e88e5;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.print-button:hover {
  background-color: #1565c0;
}

@media print {
  body {
    background-color: #fff;
  }

  .print-container {
    box-shadow: none;
    margin: 0;
    padding: 15px;
    max-width: 100%;
  }

  .print-button {
    display: none;
  }

  .section-title {
    background-color: #f9f9f9;
    border-left-color: #666;
  }
}

/* Additional Utility Classes */
.d-inline {
  display: inline;
}

.text-center {
  text-align: center;
}

.mt-3 {
  margin-top: 15px;
}

.inline-form {
  display: inline;
}
