<?php
// Initialize the session
session_start();

// Check if the user is logged in, if not then redirect to login page
if (!isset($_SESSION["user_id"]) || $_SESSION["role"] !== "doctor") {
    header("location: ../index.php");
    exit;
}

// Include database connection
require_once "../db_connect.php";

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, dp.department_name
                FROM doctors d
                JOIN departments dp ON d.department_id = dp.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();
$doctor_info = $doctor_result->fetch_assoc();
if ($doctor_result->num_rows > 0) {
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if record ID is provided
if (!isset($_GET["id"]) || empty($_GET["id"])) {
    header("location: medical_records.php");
    exit;
}

$record_id = $_GET["id"];
$errors = [];
$success_message = '';

// Check if the record exists and belongs to this doctor
$check_query = "SELECT mr.*, p.first_name as patient_first_name, p.last_name as patient_last_name
                FROM medical_records mr
                JOIN patients p ON mr.patient_id = p.patient_id
                WHERE mr.record_id = ? AND mr.doctor_id = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("si", $record_id, $doctor_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows === 0) {
    // Record doesn't exist or doesn't belong to this doctor
    header("location: medical_records.php");
    exit;
}

$record_info = $check_result->fetch_assoc();

// Process deletion
if ($_SERVER["REQUEST_METHOD"] === "POST" && isset($_POST["confirm_delete"])) {
    // Delete the record
    $delete_query = "DELETE FROM medical_records WHERE record_id = ? AND doctor_id = ?";
    $delete_stmt = $conn->prepare($delete_query);
    $delete_stmt->bind_param("si", $record_id, $doctor_id);

    if ($delete_stmt->execute()) {
        // Redirect to medical records page with success message
        $_SESSION["success_message"] = "Medical record deleted successfully.";
        header("location: medical_records.php");
        exit;
    } else {
        $errors[] = "Error deleting record: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Medical Record | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li class="active">
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-file-medical"></i> Delete Medical Record</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a> /
                    <a href="medical_records.php">Medical Records</a> /
                    <span>Delete Medical Record</span>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert error">
                        <i class="fas fa-exclamation-circle"></i>
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Confirm Deletion</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <p>Are you sure you want to delete this medical record? This action cannot be undone.</p>
                        </div>

                        <div class="record-details">
                            <h4>Record Information</h4>
                            <div class="detail-item">
                                <span class="detail-label">Record ID:</span>
                                <span class="detail-value"><?php echo $record_info['record_id']; ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Patient:</span>
                                <span class="detail-value"><?php echo $record_info['patient_first_name'] . ' ' . $record_info['patient_last_name']; ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Date Created:</span>
                                <span class="detail-value"><?php echo date('M d, Y', strtotime($record_info['created_at'])); ?></span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">Diagnosis:</span>
                                <span class="detail-value"><?php echo $record_info['diagnosis']; ?></span>
                            </div>
                        </div>

                        <form action="delete_medical_record.php?id=<?php echo $record_id; ?>" method="POST" class="delete-form">
                            <div class="form-actions">
                                <a href="medical_records.php" class="btn btn-secondary">Cancel</a>
                                <button type="submit" name="confirm_delete" class="btn btn-danger">Delete Record</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
