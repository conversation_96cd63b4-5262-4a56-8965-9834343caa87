<?php
// <PERSON><PERSON><PERSON> to update the patients table structure with medical fields
include "db_connect.php";

echo "<h2>Updating Patients Table with Medical Fields</h2>";

// Read the SQL file
$sql_file = file_get_contents(__DIR__ . '/sql/add_medical_fields.sql');

// Split into individual statements
$statements = explode(';', $sql_file);

// Execute each statement
foreach ($statements as $statement) {
    $statement = trim($statement);
    if (!empty($statement)) {
        if ($conn->query($statement)) {
            echo "Executed: " . substr($statement, 0, 50) . "...<br>";
        } else {
            echo "Error executing: " . $statement . "<br>";
            echo "Error details: " . $conn->error . "<br>";
        }
    }
}

echo "<h3>Update Complete!</h3>";
echo "<p>The patients table has been updated with the following fields:</p>";
echo "<ul>";
echo "<li>allergies - For storing patient allergies</li>";
echo "<li>current_medications - For storing patient's current medications</li>";
echo "<li>medical_history - For storing patient's medical history</li>";
echo "</ul>";

echo "<p><a href='admin/patients.php'>Return to Patients List</a></p>";
?>
