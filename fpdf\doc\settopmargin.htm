<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>SetTopMargin</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>SetTopMargin</h1>
<code>SetTopMargin(<b>float</b> margin)</code>
<h2>Description</h2>
Defines the top margin. The method can be called before creating the first page.
<h2>Parameters</h2>
<dl class="param">
<dt><code>margin</code></dt>
<dd>
The margin.
</dd>
</dl>
<h2>See also</h2>
<a href="setleftmargin.htm">SetLeftMargin</a>,
<a href="setrightmargin.htm">SetRightMargin</a>,
<a href="setautopagebreak.htm">SetAutoPageBreak</a>,
<a href="setmargins.htm">SetMargins</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
