<?php
/**
 * Database Class
 *
 * This class provides a connection to the database and utility methods.
 */

class Database {
    // Database configuration
    private $host = "127.0.0.1";
    private $username = "root";
    private $password = "";
    private $database = "hospital_management_system";

    // Connection object
    private $conn = null;

    /**
     * Constructor
     * Establishes a database connection
     */
    public function __construct() {
        // Try multiple connection methods
        try {
            // Method 1: Try with IP address
            $this->conn = new mysqli($this->host, $this->username, $this->password, $this->database);

            // Check connection
            if ($this->conn->connect_error) {
                throw new Exception("Connection failed with IP: " . $this->conn->connect_error);
            }

            // Set character set
            $this->conn->set_charset("utf8mb4");

        } catch (Exception $e) {
            try {
                // Method 2: Try with 'localhost'
                $this->conn = new mysqli('localhost', $this->username, $this->password, $this->database);

                if ($this->conn->connect_error) {
                    throw new Exception("Connection failed with localhost: " . $this->conn->connect_error);
                }

                // Set character set
                $this->conn->set_charset("utf8mb4");

            } catch (Exception $e2) {
                // All connection methods failed
                die("Database connection error: Please check your MySQL server is running and the database exists.<br>
                     Technical details: " . $e->getMessage() . "<br>" . $e2->getMessage());
            }
        }
    }

    /**
     * Get the database connection
     *
     * @return mysqli The database connection
     */
    public function getConnection() {
        return $this->conn;
    }

    /**
     * Close the database connection
     */
    public function closeConnection() {
        if ($this->conn && !($this->conn instanceof mysqli_result) && $this->conn->connect_errno === 0) {
            $this->conn->close();
            $this->conn = null;
        }
    }

    /**
     * Destructor
     * Closes the database connection when the object is destroyed
     */
    public function __destruct() {
        $this->closeConnection();
    }
}
?>
