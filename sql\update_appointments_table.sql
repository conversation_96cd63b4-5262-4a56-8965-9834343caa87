-- SQL script to update the appointments table for email notifications
-- Add columns for tracking email notifications

-- Check if the columns already exist before adding them
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND COLUMN_NAME = 'reminder_sent';

-- Add reminder_sent column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE appointments ADD COLUMN reminder_sent TINYINT(1) DEFAULT 0 COMMENT "Flag indicating if reminder email was sent"',
    'SELECT "Column reminder_sent already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the reminder_sent_date column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND COLUMN_NAME = 'reminder_sent_date';

-- Add reminder_sent_date column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE appointments ADD COLUMN reminder_sent_date DATETIME NULL COMMENT "Date and time when reminder email was sent"',
    'SELECT "Column reminder_sent_date already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the confirmation_sent column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND COLUMN_NAME = 'confirmation_sent';

-- Add confirmation_sent column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE appointments ADD COLUMN confirmation_sent TINYINT(1) DEFAULT 0 COMMENT "Flag indicating if confirmation email was sent"',
    'SELECT "Column confirmation_sent already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the confirmation_sent_date column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND COLUMN_NAME = 'confirmation_sent_date';

-- Add confirmation_sent_date column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE appointments ADD COLUMN confirmation_sent_date DATETIME NULL COMMENT "Date and time when confirmation email was sent"',
    'SELECT "Column confirmation_sent_date already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create an index for faster queries when sending reminders
SET @indexExists = 0;
SELECT COUNT(*) INTO @indexExists FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND INDEX_NAME = 'idx_appointment_reminder';

-- Add index if it doesn't exist
SET @query = IF(@indexExists = 0, 
    'CREATE INDEX idx_appointment_reminder ON appointments(appointment_date, status, reminder_sent)',
    'SELECT "Index idx_appointment_reminder already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create an index for faster queries when checking confirmation status
SET @indexExists = 0;
SELECT COUNT(*) INTO @indexExists FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'appointments' AND INDEX_NAME = 'idx_appointment_confirmation';

-- Add index if it doesn't exist
SET @query = IF(@indexExists = 0, 
    'CREATE INDEX idx_appointment_confirmation ON appointments(appointment_date, status, confirmation_sent)',
    'SELECT "Index idx_appointment_confirmation already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
