<?php
// Database configuration
$host = "127.0.0.1"; // Using IP instead of hostname
$username = "root";
$password = "";
$database = "hospital_management_system";

// Error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Try multiple connection methods
try {
    // Method 1: Try with IP address
    $conn = new mysqli($host, $username, $password, $database);

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed with IP: " . $conn->connect_error);
    }

    // Set character set
    $conn->set_charset("utf8mb4");

} catch (Exception $e) {
    try {
        // Method 2: Try with 'localhost'
        $conn = new mysqli('localhost', $username, $password, $database);

        if ($conn->connect_error) {
            throw new Exception("Connection failed with localhost: " . $conn->connect_error);
        }

        // Set character set
        $conn->set_charset("utf8mb4");

    } catch (Exception $e2) {
        // Method 3: Try with PDO
        try {
            $dsn = "mysql:host=$host;dbname=$database;charset=utf8mb4";
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ];

            $pdo = new PDO($dsn, $username, $password, $options);

            // Create a mysqli-compatible wrapper for PDO
            class PDOtoMySQLi {
                private $pdo;

                public function __construct($pdo) {
                    $this->pdo = $pdo;
                }

                public function prepare($query) {
                    $stmt = $this->pdo->prepare($query);
                    return new PDOtoMySQLiStatement($stmt, $this->pdo);
                }

                public function query($query) {
                    $stmt = $this->pdo->query($query);
                    return new PDOtoMySQLiResult($stmt);
                }

                public function real_escape_string($string) {
                    return substr($this->pdo->quote($string), 1, -1);
                }

                public function set_charset($charset) {
                    $this->pdo->exec("SET NAMES $charset");
                }

                public function __get($name) {
                    if ($name === 'insert_id') {
                        return $this->pdo->lastInsertId();
                    }
                    if ($name === 'error') {
                        $errorInfo = $this->pdo->errorInfo();
                        return $errorInfo[2];
                    }
                    return null;
                }
            }

            class PDOtoMySQLiStatement {
                private $stmt;
                private $pdo;
                private $params = [];
                private $types = '';

                public function __construct($stmt, $pdo) {
                    $this->stmt = $stmt;
                    $this->pdo = $pdo;
                }

                public function bind_param($types, ...$params) {
                    $this->types = $types;
                    $this->params = $params;
                    return true;
                }

                public function execute() {
                    if (empty($this->params)) {
                        return $this->stmt->execute();
                    }

                    // Bind parameters
                    $paramCount = 0;
                    foreach ($this->params as $i => $param) {
                        $paramCount++;
                        $type = isset($this->types[$i]) ? $this->types[$i] : 's';

                        switch ($type) {
                            case 'i':
                                $this->stmt->bindValue($paramCount, $param, PDO::PARAM_INT);
                                break;
                            case 'd':
                                $this->stmt->bindValue($paramCount, $param, PDO::PARAM_STR);
                                break;
                            case 'b':
                                $this->stmt->bindValue($paramCount, $param, PDO::PARAM_LOB);
                                break;
                            default:
                                $this->stmt->bindValue($paramCount, $param, PDO::PARAM_STR);
                        }
                    }

                    return $this->stmt->execute();
                }

                public function get_result() {
                    return new PDOtoMySQLiResult($this->stmt);
                }
            }

            class PDOtoMySQLiResult {
                private $stmt;
                private $result = [];
                private $position = 0;

                public function __construct($stmt) {
                    $this->stmt = $stmt;
                    $this->result = $stmt->fetchAll(PDO::FETCH_ASSOC);
                }

                public function fetch_assoc() {
                    if (isset($this->result[$this->position])) {
                        return $this->result[$this->position++];
                    }
                    return false;
                }

                public function fetch_all() {
                    return $this->result;
                }

                public function __get($name) {
                    if ($name === 'num_rows') {
                        return count($this->result);
                    }
                    return null;
                }
            }

            $conn = new PDOtoMySQLi($pdo);

        } catch (Exception $e3) {
            // All connection methods failed
            die("Database connection error: Please check your MySQL server is running and the database exists.<br>
                 Technical details: " . $e->getMessage() . "<br>" . $e2->getMessage() . "<br>" . $e3->getMessage());
        }
    }
}
?>