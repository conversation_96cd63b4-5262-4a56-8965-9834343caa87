<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Get upcoming appointments (only pending, scheduled, or confirmed)
$upcoming_appointments = [];
$upcoming_query = "SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                  dep.department_name as department_name
                  FROM appointments a
                  JOIN doctors d ON a.doctor_id = d.doctor_id
                  JOIN departments dep ON a.department_id = dep.department_id
                  WHERE a.patient_id = ? AND a.appointment_date >= CURDATE()
                  AND a.status IN ('scheduled', 'confirmed', 'pending')
                  ORDER BY a.appointment_date, a.appointment_time
                  LIMIT 5";
$upcoming_stmt = $conn->prepare($upcoming_query);
$upcoming_stmt->bind_param("i", $patient_id);
$upcoming_stmt->execute();
$upcoming_result = $upcoming_stmt->get_result();
if ($upcoming_result->num_rows > 0) {
    while ($row = $upcoming_result->fetch_assoc()) {
        $upcoming_appointments[] = $row;
    }
}





// Get prescriptions
$prescriptions = [];
$prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name
                       FROM prescriptions p
                       JOIN doctors d ON p.doctor_id = d.doctor_id
                       WHERE p.patient_id = ?
                       ORDER BY p.created_at DESC
                       LIMIT 5";
$prescriptions_stmt = $conn->prepare($prescriptions_query);
$prescriptions_stmt->bind_param("i", $patient_id);
$prescriptions_stmt->execute();
$prescriptions_result = $prescriptions_stmt->get_result();
if ($prescriptions_result->num_rows > 0) {
    while ($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}

// Get appointment statistics
$total_appointments = 0;
$completed_appointments = 0;
$upcoming_count = 0;
$cancelled_appointments = 0;

// Instead of counting from database, count the actual appointments we display
// Get all appointments for this patient
$all_appointments_query = "SELECT * FROM appointments WHERE patient_id = ?";
$all_appointments_stmt = $conn->prepare($all_appointments_query);
$all_appointments_stmt->bind_param("i", $patient_id);
$all_appointments_stmt->execute();
$all_appointments_result = $all_appointments_stmt->get_result();
$all_appointments = [];
if ($all_appointments_result->num_rows > 0) {
    while ($row = $all_appointments_result->fetch_assoc()) {
        if (in_array($row['status'], ['completed', 'cancelled', 'pending', 'confirmed', 'scheduled'])) {
            $all_appointments[] = $row;
        }
    }
}
// Set total appointments to the count of valid appointments
$total_appointments = count($all_appointments);

// Count appointments by status from our filtered array
$completed_appointments = 0;
$upcoming_count = 0;
$cancelled_appointments = 0;
$today = date('Y-m-d');

foreach ($all_appointments as $appointment) {
    // Count completed appointments
    if ($appointment['status'] == 'completed') {
        $completed_appointments++;
    }

    // Count upcoming appointments (pending, scheduled, or confirmed with future date)
    if (in_array($appointment['status'], ['scheduled', 'confirmed', 'pending']) &&
        $appointment['appointment_date'] >= $today) {
        $upcoming_count++;
    }

    // Count cancelled appointments
    if ($appointment['status'] == 'cancelled') {
        $cancelled_appointments++;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Dashboard | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li class="active">
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-tachometer-alt"></i> Patient Dashboard</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <!-- Welcome Banner -->
                <div class="welcome-banner">
                    <div class="welcome-content">
                        <h2>Welcome, <?php echo $patient_info['first_name']; ?>!</h2>
                        <p>Here's your health information and upcoming appointments.</p>
                    </div>
                    <div class="welcome-actions">
                        <a href="book_appointment.php" class="btn btn-primary"><i class="fas fa-calendar-plus"></i> Book Appointment</a>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="stats-container">
                    <div class="stat-card">
                        <div class="stat-card-icon appointment-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $total_appointments; ?></h3>
                            <p>Total Appointments</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon completed-icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $completed_appointments; ?></h3>
                            <p>Completed</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon upcoming-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $upcoming_count; ?></h3>
                            <p>Upcoming</p>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-card-icon cancelled-icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div class="stat-card-info">
                            <h3><?php echo $cancelled_appointments; ?></h3>
                            <p>Cancelled</p>
                        </div>
                    </div>
                </div>

                <!-- Upcoming Appointments -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-calendar-alt"></i> Upcoming Appointments</h3>
                        <a href="appointments.php?date=upcoming" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (count($upcoming_appointments) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Time</th>
                                            <th>Doctor</th>
                                            <th>Department</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($upcoming_appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                <td>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></td>
                                                <td><?php echo $appointment['department_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($appointment['status'] != 'completed'): ?>
                                                        <a href="cancel_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon btn-danger" title="Cancel" onclick="return confirm('Are you sure you want to cancel this appointment?')">
                                                            <i class="fas fa-times"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-calendar-alt empty-icon"></i>
                                <p>No upcoming appointments scheduled.</p>
                                <a href="book_appointment.php" class="btn btn-primary">Book an Appointment</a>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>





                <!-- Prescriptions -->
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-prescription"></i> Recent Prescriptions</h3>
                        <a href="prescriptions.php" class="btn btn-sm btn-outline">View All</a>
                    </div>
                    <div class="card-body">
                        <?php if (count($prescriptions) > 0): ?>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Doctor</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($prescriptions as $prescription): ?>
                                            <tr>
                                                <td><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></td>
                                                <td>Dr. <?php echo $prescription['doctor_first_name'] . ' ' . $prescription['doctor_last_name']; ?></td>
                                                <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                <td>
                                                    <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="download_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Download">
                                                        <i class="fas fa-download"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="empty-state">
                                <i class="fas fa-prescription empty-icon"></i>
                                <p>No prescriptions found.</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>