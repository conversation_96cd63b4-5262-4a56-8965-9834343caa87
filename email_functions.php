<?php
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\PHPMailer\SMTP;
use <PERSON><PERSON><PERSON>ailer\PHPMailer\Exception;

// Load Composer's autoloader
require 'vendor/autoload.php';

/**
 * Send an email using <PERSON><PERSON><PERSON><PERSON>er
 * 
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message Email message (HTML)
 * @param array $attachments Optional array of attachments
 * @return bool True if email was sent successfully, false otherwise
 */
function sendEmail($to, $subject, $message, $attachments = []) {
    // Create a new PHPMailer instance
    $mail = new PHPMailer(true);
    
    try {
        // Server settings
        $mail->isSMTP();
        $mail->Host = 'smtp.gmail.com';
        $mail->SMTPAuth = true;
        $mail->Username = '<EMAIL>'; // Hospital system email
        $mail->Password = 'joqs huft yxtp httx'; // App password
        $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
        $mail->Port = 587;
        
        // Recipients
        $mail->setFrom('<EMAIL>', 'CSUCC Hospital');
        $mail->addAddress($to);
        $mail->addReplyTo('<EMAIL>', 'CSUCC Hospital');
        
        // Content
        $mail->isHTML(true);
        $mail->Subject = $subject;
        $mail->Body = $message;
        $mail->AltBody = strip_tags(str_replace('<br>', "\n", $message));
        
        // Add attachments if any
        if (!empty($attachments)) {
            foreach ($attachments as $attachment) {
                if (isset($attachment['path']) && file_exists($attachment['path'])) {
                    $mail->addAttachment(
                        $attachment['path'],
                        isset($attachment['name']) ? $attachment['name'] : basename($attachment['path'])
                    );
                }
            }
        }
        
        // Send the email
        $mail->send();
        return true;
    } catch (Exception $e) {
        // Log the error
        error_log("Email sending failed: {$mail->ErrorInfo}");
        return false;
    }
}

/**
 * Send appointment notification email
 * 
 * @param array $appointment Appointment data
 * @param array $patient Patient data
 * @param array $doctor Doctor data
 * @param string $status Appointment status
 * @return bool True if email was sent successfully, false otherwise
 */
function sendAppointmentNotification($appointment, $patient, $doctor, $status) {
    $patient_name = $patient['first_name'] . ' ' . $patient['last_name'];
    $doctor_name = 'Dr. ' . $doctor['first_name'] . ' ' . $doctor['last_name'];
    $appointment_date = date('l, F d, Y', strtotime($appointment['appointment_date']));
    $appointment_time = date('h:i A', strtotime($appointment['appointment_time']));
    $status_text = ucfirst($status);
    
    $subject = "CSUCC Hospital - Appointment $status_text";
    
    // Customize message based on status
    switch ($status) {
        case 'confirmed':
            $status_message = "Your appointment has been confirmed.";
            $next_steps = "Please arrive 15 minutes before your scheduled time. Don't forget to bring any relevant medical records or test results.";
            break;
        case 'cancelled':
            $status_message = "Your appointment has been cancelled.";
            $next_steps = "If you would like to reschedule, please log in to your patient portal or contact us at (*************.";
            break;
        case 'completed':
            $status_message = "Your appointment has been marked as completed.";
            $next_steps = "If you have any follow-up questions or concerns, please contact your doctor directly.";
            break;
        case 'rescheduled':
            $status_message = "Your appointment has been rescheduled.";
            $next_steps = "Please note the new date and time for your appointment. If this time doesn't work for you, please contact us as soon as possible.";
            break;
        default:
            $status_message = "Your appointment status has been updated to: $status_text.";
            $next_steps = "If you have any questions, please log in to your patient portal or contact us at (*************.";
    }
    
    $message = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 10px; text-align: center; }
            .content { padding: 20px; border: 1px solid #ddd; }
            .appointment-details { background-color: #f9f9f9; padding: 15px; margin: 15px 0; border-left: 4px solid #4CAF50; }
            .footer { font-size: 12px; text-align: center; margin-top: 20px; color: #777; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h2>CSUCC Hospital - Appointment $status_text</h2>
            </div>
            <div class='content'>
                <p>Dear $patient_name,</p>
                
                <p>$status_message</p>
                
                <div class='appointment-details'>
                    <p><strong>Appointment ID:</strong> {$appointment['appointment_id']}</p>
                    <p><strong>Doctor:</strong> $doctor_name</p>
                    <p><strong>Department:</strong> {$appointment['department_name']}</p>
                    <p><strong>Date:</strong> $appointment_date</p>
                    <p><strong>Time:</strong> $appointment_time</p>
                    <p><strong>Reason for Visit:</strong> {$appointment['reason']}</p>
                </div>
                
                <p>$next_steps</p>
                
                <p>Thank you for choosing CSUCC Hospital for your healthcare needs.</p>
                
                <p>Best regards,<br>
                CSUCC Hospital Team</p>
            </div>
            <div class='footer'>
                <p>This is an automated message. Please do not reply directly to this email.</p>
                <p>CSUCC Hospital | 123 University Avenue, Cebu City, Philippines 6000 | (*************</p>
            </div>
        </div>
    </body>
    </html>
    ";
    
    return sendEmail($patient['email'], $subject, $message);
}
?>
