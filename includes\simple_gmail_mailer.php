<?php
// Include PHPMailer classes
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\PHPMailer;
use <PERSON><PERSON><PERSON>ail<PERSON>\PHPMailer\SMTP;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

// Load Composer's autoloader
require_once __DIR__ . '/../vendor/autoload.php';

/**
 * Simple Gmail Mailer class for sending emails using a Gmail account
 * This is a simplified version that should work reliably
 */
class SimpleGmailMailer {
    /**
     * Send an email using Gmail
     *
     * @param string $subject Email subject
     * @param string $message Email message (HTML)
     * @param array $data Additional data for the email
     * @return bool Whether the email was sent successfully
     */
    public function sendEmail($subject, $message, $data = array()) {
        try {
            // Create a new PHPMailer instance
            $mail = new PHPMailer(true);

            // Server settings
            $mail->SMTPDebug = 0; // Disable debug output for production
            $mail->isSMTP();
            $mail->Host = 'smtp.gmail.com';
            $mail->SMTPAuth = true;
            $mail->Username = '<EMAIL>'; // Sender email
            $mail->Password = 'dieivjpbildcybvi'; // Gmail app password
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Port = 587;

            // Disable SSL verification if needed
            $mail->SMTPOptions = array(
                'ssl' => array(
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true
                )
            );

            // Set timeout values
            $mail->Timeout = 60; // SMTP connection timeout

            // Recipients
            $mail->setFrom('<EMAIL>', 'CSUCC Hospital');
            $mail->addAddress('<EMAIL>'); // Send to the requested recipient email

            // Content
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $message;
            $mail->AltBody = strip_tags($message);

            // Send the email
            $result = $mail->send();
            error_log("Email sent <NAME_EMAIL>");
            return $result;

        } catch (Exception $e) {
            error_log("Email sending failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get the appointment email template
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return string The email template with placeholders replaced
     */
    public function getAppointmentTemplate($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Format appointment date and time
        $appointment_date = date('l, F j, Y', strtotime($appointment_info['appointment_date']));
        $appointment_time = date('h:i A', strtotime($appointment_info['appointment_time']));

        // Get patient and doctor names - ensure we use the exact names from the database
        $patient_name = $patient_info['first_name'] . ' ' . $patient_info['last_name'];
        $doctor_name = 'Dr. ' . $doctor_info['first_name'] . ' ' . $doctor_info['last_name'];

        // Get department name
        $department_name = isset($doctor_info['department_name']) ? $doctor_info['department_name'] : '';

        // Determine email subject and header based on status
        $status = strtolower($appointment_info['status']);
        $email_header = '';
        $header_color = '#4a7c59'; // Default green color

        if ($status == 'confirmed') {
            $email_header = 'Appointment Confirmation';
        } else if ($status == 'cancelled' || $status == 'canceled') {
            $email_header = 'Appointment Cancellation';
            $status = 'cancelled'; // Normalize spelling
            $header_color = '#d9534f'; // Red for cancellation
        } else if ($status == 'completed') {
            $email_header = 'Appointment Completed';
            $header_color = '#5bc0de'; // Blue for completion
        } else {
            $email_header = 'Appointment Reminder';
        }

        // Get reason for visit if available
        $reason = isset($appointment_info['reason']) ? $appointment_info['reason'] : 'Not specified';

        // Get information about who changed the status
        $status_changed_by_text = '';
        if ($status_changed_by !== null) {
            $status_changed_by_name = 'Dr. ' . $status_changed_by['first_name'] . ' ' . $status_changed_by['last_name'];
            $status_changed_by_text = " by $status_changed_by_name";
        }

        // Create the email template with the enhanced design
        $template = "
        <html>
        <head>
            <meta charset='UTF-8'>
            <meta name='viewport' content='width=device-width, initial-scale=1.0'>
            <title>$email_header - CSUCC Hospital</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 3px 10px rgba(0,0,0,0.1); }
                .header { background-color: $header_color; color: white; padding: 30px; text-align: center; }
                .header h1 { margin: 0; font-size: 24px; }
                .content { padding: 30px; background-color: #ffffff; }
                .appointment-details { margin: 25px 0; background-color: #f9f9f9; padding: 20px; border-left: 4px solid $header_color; }
                .appointment-details h3 { margin-top: 0; color: #444; }
                .appointment-details p { margin: 10px 0; }
                .instructions { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 4px; }
                .instructions h3 { margin-top: 0; color: #444; }
                .footer { font-size: 12px; color: #777; text-align: center; margin-top: 30px; padding: 20px; background-color: #f9f9f9; }
                .button { display: inline-block; background-color: $header_color; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; margin-top: 15px; }
                .contact-info { margin-top: 25px; padding-top: 20px; border-top: 1px solid #eee; }

                /* Print styles */
                @media print {
                    body { background-color: #fff; }
                    .container { box-shadow: none; max-width: 100%; }
                    .header, .footer, .instructions, .contact-info, .button { display: none; }
                    .printable-section { border: 1px solid #000; padding: 20px; margin: 0; }
                    .content { padding: 0; }
                }
            </style>
        </head>
        <body>
            <div class='container'>
                <div class='header'>
                    <h1>$email_header</h1>
                </div>
                <div class='content'>
                    <p>Dear $patient_name,</p>";

        if ($status == 'confirmed') {
            $template .= "
                    <p>Your appointment at CSUCC Hospital has been <strong>confirmed</strong>" . $status_changed_by_text . ". We look forward to seeing you on the scheduled date and time.</p>";
        } else if ($status == 'cancelled') {
            $template .= "
                    <p>We regret to inform you that your appointment at CSUCC Hospital has been <strong>cancelled</strong>" . $status_changed_by_text . ".</p>";

            // Add cancellation reason if available
            if (isset($appointment_info['cancellation_reason']) && !empty($appointment_info['cancellation_reason'])) {
                $template .= "
                    <p><strong>Reason for cancellation:</strong> " . $appointment_info['cancellation_reason'] . "</p>";
            }
        } else if ($status == 'completed') {
            $template .= "
                    <p>Thank you for visiting CSUCC Hospital. Your appointment has been marked as <strong>completed</strong>" . $status_changed_by_text . ".</p>
                    <p>We hope your visit was satisfactory and all your healthcare needs were addressed.</p>";
        } else {
            $template .= "
                    <p>This is a friendly reminder about your upcoming appointment at CSUCC Hospital.</p>";
        }

        $template .= "
                    <div class='appointment-details'>
                        <h3>Appointment Details:</h3>
                        <p><strong>Date:</strong> $appointment_date</p>
                        <p><strong>Time:</strong> $appointment_time</p>
                        <p><strong>Doctor:</strong> $doctor_name</p>
                        <p><strong>Department:</strong> $department_name</p>
                        <p><strong>Reason for Visit:</strong> $reason</p>
                    </div>";

        if ($status == 'confirmed' || $status == '') {
            $template .= "
                    <div class='instructions'>
                        <h3>Important Instructions:</h3>
                        <ul>
                            <li>Please arrive 15 minutes before your scheduled appointment time.</li>
                            <li>Bring your ID and insurance card (if applicable).</li>
                            <li>If you're taking any medications, please bring a list with you.</li>
                            <li>If you need to reschedule or cancel, please do so at least 24 hours in advance.</li>
                        </ul>
                    </div>

                    <p>You can print this confirmation to present at the hospital reception desk on the day of your appointment.</p>

                    <center>
                        <div class='printable-section' style='border: 1px dashed #ccc; padding: 20px; margin: 20px 0; background-color: #f9f9f9;'>
                            <div style='text-align: center; margin-bottom: 15px;'>
                                <h2 style='color: #4a7c59; margin: 0;'>CSUCC Hospital</h2>
                                <p style='margin: 5px 0;'>123 Healthcare Avenue, Medical District, City</p>
                                <p style='margin: 5px 0;'>Phone: (*************</p>
                            </div>
                            <div style='text-align: center; margin-bottom: 15px;'>
                                <h3 style='margin: 0;'>Appointment Confirmation</h3>
                                <p style='margin: 5px 0;'><strong>Confirmation #:</strong> " . $appointment_info['appointment_id'] . "</p>
                            </div>
                            <div style='margin-bottom: 15px;'>
                                <p><strong>Patient:</strong> $patient_name</p>
                                <p><strong>Date:</strong> $appointment_date</p>
                                <p><strong>Time:</strong> $appointment_time</p>
                                <p><strong>Doctor:</strong> $doctor_name</p>
                                <p><strong>Department:</strong> $department_name</p>
                                <p><strong>Reason for Visit:</strong> $reason</p>
                            </div>
                            <div style='margin-top: 20px; font-size: 12px; color: #666;'>
                                <p>Please present this confirmation at the hospital reception desk.</p>
                                <p>Arrive 15 minutes before your scheduled appointment time.</p>
                            </div>
                        </div>
                    </center>

                    <center><a href='javascript:void(0);' onclick='window.print();' class='button'>Print Appointment Details</a></center>";
        } else if ($status == 'cancelled') {
            $template .= "
                    <div class='instructions'>
                        <h3>What's Next?</h3>
                        <p>If you would like to reschedule your appointment, you can:</p>
                        <ul>
                            <li>Log in to your patient portal and book a new appointment</li>
                            <li>Call our appointment desk at (*************</li>
                            <li>Visit our hospital during working hours</li>
                        </ul>
                    </div>";
        } else if ($status == 'completed') {
            $template .= "
                    <div class='instructions'>
                        <h3>Follow-up Information:</h3>
                        <ul>
                            <li>Any prescribed medications should be taken as directed by your doctor.</li>
                            <li>Your medical records have been updated with details from this visit.</li>
                            <li>If a follow-up appointment is needed, please schedule it at your earliest convenience.</li>
                            <li>You can access your medical records through your patient portal.</li>
                        </ul>
                    </div>";
        }

        $template .= "
                    <div class='contact-info'>
                        <p>If you have any questions or need assistance, please contact us:</p>
                        <p><strong>Phone:</strong> (*************</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Address:</strong> 123 Healthcare Avenue, Medical District, City</p>
                    </div>

                    <p>Thank you for choosing CSUCC Hospital for your healthcare needs.</p>

                    <p>Best regards,<br>
                    CSUCC Hospital Team</p>
                </div>
                <div class='footer'>
                    <p>This is an automated message. Please do not reply to this email.</p>
                    <p>© " . date('Y') . " CSUCC Hospital. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>";

        return $template;
    }

    /**
     * Send an appointment reminder email
     *
     * @param array $appointment Appointment information
     * @param array $patient Patient information
     * @param array $doctor Doctor information
     * @param array $department Department information
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentReminder($appointment, $patient, $doctor, $department) {
        // Create subject for reminder email
        $subject = 'Appointment Reminder - CSUCC Hospital';

        // Format appointment date and time for display
        $appointment_date = date('l, F j, Y', strtotime($appointment['appointment_date']));
        $appointment_time = date('h:i A', strtotime($appointment['appointment_time']));

        // Get patient and doctor names
        $patient_name = $patient['first_name'] . ' ' . $patient['last_name'];
        $doctor_name = 'Dr. ' . $doctor['first_name'] . ' ' . $doctor['last_name'];

        // Get department name
        $department_name = isset($department['department_name']) ? $department['department_name'] : '';

        // Get reason for visit if available
        $reason = isset($appointment['reason']) ? $appointment['reason'] : 'Not specified';

        // Build the email message with enhanced design
        $message = '
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Appointment Reminder - CSUCC Hospital</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 3px 10px rgba(0,0,0,0.1); }
                .header { background-color: #4a7c59; color: white; padding: 30px; text-align: center; }
                .header h1 { margin: 0; font-size: 24px; }
                .content { padding: 30px; background-color: #ffffff; }
                .appointment-details { margin: 25px 0; background-color: #f9f9f9; padding: 20px; border-left: 4px solid #4a7c59; }
                .appointment-details h3 { margin-top: 0; color: #444; }
                .appointment-details p { margin: 10px 0; }
                .instructions { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 4px; }
                .instructions h3 { margin-top: 0; color: #444; }
                .footer { font-size: 12px; color: #777; text-align: center; margin-top: 30px; padding: 20px; background-color: #f9f9f9; }
                .button { display: inline-block; background-color: #4a7c59; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; margin-top: 15px; }
                .contact-info { margin-top: 25px; padding-top: 20px; border-top: 1px solid #eee; }

                /* Print styles */
                @media print {
                    body { background-color: #fff; }
                    .container { box-shadow: none; max-width: 100%; }
                    .header, .footer, .instructions, .contact-info, .button { display: none; }
                    .printable-section { border: 1px solid #000; padding: 20px; margin: 0; }
                    .content { padding: 0; }
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Appointment Reminder</h1>
                </div>
                <div class="content">
                    <p>Dear ' . $patient_name . ',</p>

                    <p>This is a friendly reminder about your upcoming appointment at CSUCC Hospital.</p>

                    <div class="appointment-details">
                        <h3>Appointment Details:</h3>
                        <p><strong>Date:</strong> ' . $appointment_date . '</p>
                        <p><strong>Time:</strong> ' . $appointment_time . '</p>
                        <p><strong>Doctor:</strong> ' . $doctor_name . '</p>
                        <p><strong>Department:</strong> ' . $department_name . '</p>
                        <p><strong>Reason for Visit:</strong> ' . $reason . '</p>
                    </div>

                    <div class="instructions">
                        <h3>Important Instructions:</h3>
                        <ul>
                            <li>Please arrive 15 minutes before your scheduled appointment time.</li>
                            <li>Bring your ID and insurance card (if applicable).</li>
                            <li>If you\'re taking any medications, please bring a list with you.</li>
                            <li>If you need to reschedule or cancel, please do so at least 24 hours in advance.</li>
                        </ul>
                    </div>

                    <p>You can print this confirmation to present at the hospital reception desk on the day of your appointment.</p>

                    <center>
                        <div class="printable-section" style="border: 1px dashed #ccc; padding: 20px; margin: 20px 0; background-color: #f9f9f9;">
                            <div style="text-align: center; margin-bottom: 15px;">
                                <h2 style="color: #4a7c59; margin: 0;">CSUCC Hospital</h2>
                                <p style="margin: 5px 0;">123 Healthcare Avenue, Medical District, City</p>
                                <p style="margin: 5px 0;">Phone: (*************</p>
                            </div>
                            <div style="text-align: center; margin-bottom: 15px;">
                                <h3 style="margin: 0;">Appointment Reminder</h3>
                                <p style="margin: 5px 0;"><strong>Appointment #:</strong> ' . $appointment['appointment_id'] . '</p>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <p><strong>Patient:</strong> ' . $patient_name . '</p>
                                <p><strong>Date:</strong> ' . $appointment_date . '</p>
                                <p><strong>Time:</strong> ' . $appointment_time . '</p>
                                <p><strong>Doctor:</strong> ' . $doctor_name . '</p>
                                <p><strong>Department:</strong> ' . $department_name . '</p>
                                <p><strong>Reason for Visit:</strong> ' . $reason . '</p>
                            </div>
                            <div style="margin-top: 20px; font-size: 12px; color: #666;">
                                <p>Please present this confirmation at the hospital reception desk.</p>
                                <p>Arrive 15 minutes before your scheduled appointment time.</p>
                            </div>
                        </div>
                    </center>

                    <center><a href="javascript:void(0);" onclick="window.print();" class="button">Print Appointment Details</a></center>

                    <div class="contact-info">
                        <p>If you have any questions or need assistance, please contact us:</p>
                        <p><strong>Phone:</strong> (*************</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Address:</strong> 123 Healthcare Avenue, Medical District, City</p>
                    </div>

                    <p>Thank you for choosing CSUCC Hospital for your healthcare needs.</p>

                    <p>Best regards,<br>
                    CSUCC Hospital Team</p>
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                    <p>© ' . date('Y') . ' CSUCC Hospital. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>';

        // Send the email
        return $this->sendEmail($subject, $message);
    }

    /**
     * Send an appointment cancellation email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentCancellation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Set status to cancelled for the email
        $appointment_info['status'] = 'cancelled';

        // Create subject for cancellation email
        $subject = 'Appointment Cancellation - CSUCC Hospital';

        // Format appointment date and time for display
        $appointment_date = date('l, F j, Y', strtotime($appointment_info['appointment_date']));
        $appointment_time = date('h:i A', strtotime($appointment_info['appointment_time']));

        // Get patient and doctor names - ensure we use the exact names from the database
        $patient_name = $patient_info['first_name'] . ' ' . $patient_info['last_name'];
        $doctor_name = 'Dr. ' . $doctor_info['first_name'] . ' ' . $doctor_info['last_name'];

        // Get department name
        $department_name = isset($doctor_info['department_name']) ? $doctor_info['department_name'] : '';

        // Get reason for visit if available
        $reason = isset($appointment_info['reason']) ? $appointment_info['reason'] : 'Not specified';

        // Get information about who changed the status
        $status_changed_by_text = '';
        if ($status_changed_by !== null) {
            $status_changed_by_name = 'Dr. ' . $status_changed_by['first_name'] . ' ' . $status_changed_by['last_name'];
            $status_changed_by_text = " by $status_changed_by_name";
        }

        // Build the email message with enhanced design
        $message = '
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Appointment Cancellation - CSUCC Hospital</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f5f5f5; }
                .container { max-width: 600px; margin: 0 auto; background-color: #ffffff; box-shadow: 0 3px 10px rgba(0,0,0,0.1); }
                .header { background-color: #d9534f; color: white; padding: 30px; text-align: center; }
                .header h1 { margin: 0; font-size: 24px; }
                .content { padding: 30px; background-color: #ffffff; }
                .appointment-details { margin: 25px 0; background-color: #f9f9f9; padding: 20px; border-left: 4px solid #d9534f; }
                .appointment-details h3 { margin-top: 0; color: #444; }
                .appointment-details p { margin: 10px 0; }
                .cancellation-reason { margin: 25px 0; background-color: #fff5f5; padding: 20px; border-left: 4px solid #d9534f; }
                .cancellation-reason h3 { margin-top: 0; color: #d9534f; }
                .instructions { background-color: #f8f9fa; padding: 15px; margin: 20px 0; border-radius: 4px; }
                .instructions h3 { margin-top: 0; color: #444; }
                .footer { font-size: 12px; color: #777; text-align: center; margin-top: 30px; padding: 20px; background-color: #f9f9f9; }
                .button { display: inline-block; background-color: #d9534f; color: white; padding: 12px 25px; text-decoration: none; border-radius: 4px; margin-top: 15px; }
                .contact-info { margin-top: 25px; padding-top: 20px; border-top: 1px solid #eee; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>Appointment Cancellation</h1>
                </div>
                <div class="content">
                    <p>Dear ' . $patient_name . ',</p>

                    <p>We regret to inform you that your appointment at CSUCC Hospital has been <strong>cancelled</strong>' . $status_changed_by_text . '.</p>';

        // Add cancellation reason if available
        if (isset($appointment_info['cancellation_reason']) && !empty($appointment_info['cancellation_reason'])) {
            $message .= '
                    <div class="cancellation-reason">
                        <h3>Reason for Cancellation:</h3>
                        <p>' . $appointment_info['cancellation_reason'] . '</p>
                    </div>';
        }

        $message .= '
                    <div class="appointment-details">
                        <h3>Cancelled Appointment Details:</h3>
                        <p><strong>Date:</strong> ' . $appointment_date . '</p>
                        <p><strong>Time:</strong> ' . $appointment_time . '</p>
                        <p><strong>Doctor:</strong> ' . $doctor_name . '</p>
                        <p><strong>Department:</strong> ' . $department_name . '</p>
                        <p><strong>Reason for Visit:</strong> ' . $reason . '</p>
                    </div>

                    <div class="instructions">
                        <h3>What\'s Next?</h3>
                        <p>If you would like to reschedule your appointment, you can:</p>
                        <ul>
                            <li>Log in to your patient portal and book a new appointment</li>
                            <li>Call our appointment desk at (*************</li>
                            <li>Visit our hospital during working hours</li>
                        </ul>
                    </div>

                    <center><a href="#" class="button">Book a New Appointment</a></center>

                    <div class="contact-info">
                        <p>If you have any questions or need assistance, please contact us:</p>
                        <p><strong>Phone:</strong> (*************</p>
                        <p><strong>Email:</strong> <EMAIL></p>
                        <p><strong>Address:</strong> 123 Healthcare Avenue, Medical District, City</p>
                    </div>

                    <p>Thank you for your understanding.</p>

                    <p>Best regards,<br>
                    CSUCC Hospital Team</p>
                </div>
                <div class="footer">
                    <p>This is an automated message. Please do not reply to this email.</p>
                    <p>© ' . date('Y') . ' CSUCC Hospital. All rights reserved.</p>
                </div>
            </div>
        </body>
        </html>';

        // Send the email
        return $this->sendEmail($subject, $message);
    }

    /**
     * Send an appointment status email
     *
     * @param array $appointment_info Appointment information
     * @param array $patient_info Patient information
     * @param array $doctor_info Doctor information
     * @param array $status_changed_by Doctor who changed the status (optional)
     * @return bool Whether the email was sent successfully
     */
    public function sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by = null) {
        // Determine subject based on appointment status
        $status = strtolower($appointment_info['status']);
        $subject = 'Appointment ' . ucfirst($status) . ' - CSUCC Hospital';

        // Get the email template
        $message = $this->getAppointmentTemplate($appointment_info, $patient_info, $doctor_info, $status_changed_by);

        // Send the email
        return $this->sendEmail($subject, $message);
    }

    /**
     * Send a test email
     *
     * @return bool Whether the email was sent successfully
     */
    public function sendTestEmail() {
        $subject = 'Test Email from CSUCC Hospital';
        $message = "
        <html>
        <head>
            <title>Test Email</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                h1 { color: #3498db; }
            </style>
        </head>
        <body>
            <div class='container'>
                <h1>Test Email</h1>
                <p>This is a test email from the CSUCC Hospital Management System.</p>
                <p>If you received this email, the email functionality is working correctly.</p>
                <p>Time sent: " . date('Y-m-d H:i:s') . "</p>
            </div>
        </body>
        </html>
        ";

        return $this->sendEmail($subject, $message);
    }
}

// For backward compatibility
class Mailer extends SimpleGmailMailer {
    // This class extends SimpleGmailMailer for backward compatibility
}

class PersonalGmailMailer extends SimpleGmailMailer {
    // This class extends SimpleGmailMailer for backward compatibility
}

