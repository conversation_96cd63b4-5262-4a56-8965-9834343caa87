<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is a doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login
               FROM doctors d
               JOIN users u ON d.user_id = u.user_id
               WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if record ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: medical_records.php");
    exit();
}

$record_id = $_GET['id'];

// Get medical record details
$record = [];
$record_query = "SELECT mr.*,
                p.first_name as patient_first_name, p.last_name as patient_last_name,
                p.date_of_birth, p.gender, p.blood_type, p.allergies,
                d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                a.appointment_date, a.appointment_time, a.reason as appointment_reason
                FROM medical_records mr
                JOIN patients p ON mr.patient_id = p.patient_id
                JOIN doctors d ON mr.doctor_id = d.doctor_id
                LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
                WHERE mr.record_id = ? AND mr.doctor_id = ?";
$record_stmt = $conn->prepare($record_query);
$record_stmt->bind_param("ii", $record_id, $doctor_id);
$record_stmt->execute();
$record_result = $record_stmt->get_result();

if ($record_result->num_rows === 0) {
    header("Location: medical_records.php");
    exit();
}

$record = $record_result->fetch_assoc();

// Check if the record belongs to this doctor
$is_owner = ($record['doctor_id'] == $doctor_id);
if (!$is_owner) {
    header("Location: medical_records.php?error=permission");
    exit();
}

// Process form submission
$errors = [];
$success = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Validate and sanitize input
    $diagnosis = trim($_POST['diagnosis']);
    $symptoms = trim($_POST['symptoms']);
    $treatment = trim($_POST['treatment']);
    $notes = trim($_POST['notes']);
    $follow_up_date = !empty($_POST['follow_up_date']) ? $_POST['follow_up_date'] : null;

    // Validation
    if (empty($diagnosis)) {
        $errors[] = "Diagnosis is required";
    }

    if (empty($symptoms)) {
        $errors[] = "Symptoms are required";
    }

    if (empty($treatment)) {
        $errors[] = "Treatment is required";
    }

    // If no errors, update the record
    if (empty($errors)) {
        $update_query = "UPDATE medical_records SET
                        diagnosis = ?,
                        symptoms = ?,
                        treatment = ?,
                        notes = ?,
                        follow_up_date = ?,
                        updated_at = NOW()
                        WHERE record_id = ? AND doctor_id = ?";

        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("sssssii",
            $diagnosis,
            $symptoms,
            $treatment,
            $notes,
            $follow_up_date,
            $record_id,
            $doctor_id
        );

        if ($update_stmt->execute()) {
            // Redirect to view page with success message
            header("Location: view_medical_record.php?id=$record_id&success=1");
            exit();
        } else {
            $errors[] = "Error updating record: " . $conn->error;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Medical Record | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li class="active">
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-calendar-alt"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-file-medical"></i> Edit Medical Record</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['specialization']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="breadcrumb">
                    <a href="dashboard.php">Dashboard</a> /
                    <a href="medical_records.php">Medical Records</a> /
                    <span>Edit Medical Record</span>
                </div>

                <?php if (!empty($errors)): ?>
                    <div class="alert alert-danger">
                        <ul>
                            <?php foreach ($errors as $error): ?>
                                <li><?php echo $error; ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                <?php endif; ?>

                <div class="card">
                    <div class="card-header">
                        <h3>Edit Medical Record #<?php echo $record_id; ?></h3>
                        <div class="card-actions">
                            <a href="view_medical_record.php?id=<?php echo $record_id; ?>" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back</a>
                        </div>
                    </div>
                    <div class="card-body">
                        <form action="edit_medical_record.php?id=<?php echo $record_id; ?>" method="POST">
                            <div class="info-section patient-info-section">
                                <div class="section-header">
                                    <h4><i class="fas fa-user-injured"></i> Patient Information</h4>
                                </div>
                                <div class="section-content">
                                    <p><strong>Name:</strong> <?php echo $record['patient_first_name'] . ' ' . $record['patient_last_name']; ?></p>
                                    <p><strong>Date of Birth:</strong> <?php echo date('M d, Y', strtotime($record['date_of_birth'])); ?></p>
                                    <p><strong>Gender:</strong> <?php echo ucfirst($record['gender']); ?></p>
                                    <p><strong>Blood Type:</strong> <?php echo $record['blood_type']; ?></p>
                                    <p><strong>Allergies:</strong> <?php echo !empty($record['allergies']) ? $record['allergies'] : 'None'; ?></p>
                                </div>
                            </div>

                            <?php if (!empty($record['appointment_id'])): ?>
                                <div class="info-section appointment-info-section">
                                    <div class="section-header">
                                        <h4><i class="fas fa-calendar-check"></i> Appointment Information</h4>
                                    </div>
                                    <div class="section-content">
                                        <p><strong>Date:</strong> <?php echo date('M d, Y', strtotime($record['appointment_date'])); ?></p>
                                        <p><strong>Time:</strong> <?php echo date('h:i A', strtotime($record['appointment_time'])); ?></p>
                                        <p><strong>Reason:</strong> <?php echo $record['appointment_reason']; ?></p>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <div class="form-section">
                                <h4>Medical Details</h4>

                                <div class="form-group">
                                    <label for="diagnosis">Diagnosis <span class="required">*</span></label>
                                    <textarea id="diagnosis" name="diagnosis" rows="3" required><?php echo htmlspecialchars($record['diagnosis']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="symptoms">Symptoms <span class="required">*</span></label>
                                    <textarea id="symptoms" name="symptoms" rows="3" required><?php echo htmlspecialchars($record['symptoms']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="treatment">Treatment <span class="required">*</span></label>
                                    <textarea id="treatment" name="treatment" rows="3" required><?php echo htmlspecialchars($record['treatment']); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="notes">Additional Notes</label>
                                    <textarea id="notes" name="notes" rows="3"><?php echo htmlspecialchars($record['notes'] ?? ''); ?></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="follow_up_date">Follow-up Date</label>
                                    <input type="date" id="follow_up_date" name="follow_up_date" value="<?php echo $record['follow_up_date'] ?? ''; ?>">
                                </div>
                            </div>

                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary"><i class="fas fa-save"></i> Save Changes</button>
                                <a href="view_medical_record.php?id=<?php echo $record_id; ?>" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
