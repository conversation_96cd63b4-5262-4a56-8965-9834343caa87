<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Check if patient ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: patients.php");
    exit();
}

$patient_id = $_GET['id'];

// Check if medical fields exist in the patients table and add them if they don't
$fields_to_check = ['allergies', 'current_medications', 'medical_history'];
foreach ($fields_to_check as $field) {
    $check_field_query = "SELECT COUNT(*) as count FROM INFORMATION_SCHEMA.COLUMNS
                         WHERE TABLE_SCHEMA = DATABASE()
                         AND TABLE_NAME = 'patients'
                         AND COLUMN_NAME = ?";
    $check_field_stmt = $conn->prepare($check_field_query);
    $check_field_stmt->bind_param("s", $field);
    $check_field_stmt->execute();
    $check_field_result = $check_field_stmt->get_result();
    $field_exists = $check_field_result->fetch_assoc()['count'];

    if ($field_exists == 0) {
        // Add the missing field
        $add_field_query = "ALTER TABLE patients ADD COLUMN $field TEXT DEFAULT NULL";
        $conn->query($add_field_query);
    }
}

// Get patient details
$patient = null;
$query = "SELECT p.*, u.email, u.username, u.created_at as account_created
          FROM patients p
          JOIN users u ON p.user_id = u.user_id
          WHERE p.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $patient = $result->fetch_assoc();
} else {
    header("Location: patients.php");
    exit();
}

// Get patient's appointments
$appointments = [];
$appointment_query = "SELECT a.*,
                     d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                     dep.department_name
                     FROM appointments a
                     JOIN doctors d ON a.doctor_id = d.doctor_id
                     LEFT JOIN departments dep ON d.department_id = dep.department_id
                     WHERE a.patient_id = ?
                     ORDER BY a.appointment_date DESC, a.appointment_time DESC";
$appointment_stmt = $conn->prepare($appointment_query);
$appointment_stmt->bind_param("i", $patient_id);
$appointment_stmt->execute();
$appointment_result = $appointment_stmt->get_result();

if ($appointment_result->num_rows > 0) {
    while ($row = $appointment_result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get patient's medical records (if you have a medical_records table)
$medical_records = [];
// Uncomment and modify if you have a medical_records table
/*
$records_query = "SELECT * FROM medical_records WHERE patient_id = ? ORDER BY record_date DESC";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param("i", $patient_id);
$records_stmt->execute();
$records_result = $records_stmt->get_result();

if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}
*/
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Patient | Hospital Management System</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> HMS</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> Patient Details</h2>
                </div>
                <div class="header-right">
                    <a href="edit_patient.php?id=<?php echo $patient_id; ?>" class="btn btn-primary">
                        <i class="fas fa-edit"></i> Edit Patient
                    </a>
                    <a href="patients.php" class="btn btn-outline">
                        <i class="fas fa-arrow-left"></i> Back to Patients
                    </a>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="card">
                    <div class="card-header">
                        <h3>Patient Information</h3>
                    </div>
                    <div class="card-body">
                        <div class="patient-profile">
                            <div class="patient-profile-header">
                                <div class="patient-image">
                                    <img src="../assets/images/<?php echo $patient['profile_image'] ?: 'default-patient.jpg'; ?>" alt="Patient">
                                </div>
                                <div class="patient-info">
                                    <h2><?php echo htmlspecialchars($patient['first_name'] . ' ' . $patient['last_name']); ?></h2>
                                    <p class="patient-id">Patient ID: <?php echo $patient['patient_id']; ?></p>
                                    <p>
                                        <span class="info-label"><i class="fas fa-envelope"></i> Email:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($patient['email']); ?></span>
                                    </p>
                                    <p>
                                        <span class="info-label"><i class="fas fa-phone"></i> Phone:</span>
                                        <span class="info-value"><?php echo htmlspecialchars($patient['phone']); ?></span>
                                    </p>
                                </div>
                            </div>

                            <div class="patient-details">
                                <div class="details-section">
                                    <h3>Personal Information</h3>
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">Gender</span>
                                            <span class="detail-value"><?php echo ucfirst(htmlspecialchars($patient['gender'] ?? 'Not specified')); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Date of Birth</span>
                                            <span class="detail-value">
                                                <?php
                                                if ($patient['date_of_birth']) {
                                                    echo date('F d, Y', strtotime($patient['date_of_birth']));

                                                    // Calculate age
                                                    $dob = new DateTime($patient['date_of_birth']);
                                                    $now = new DateTime();
                                                    $age = $now->diff($dob)->y;
                                                    echo ' (' . $age . ' years)';
                                                } else {
                                                    echo 'Not specified';
                                                }
                                                ?>
                                            </span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Blood Group</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['blood_group'] ?? 'Not specified'); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Address</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['address'] ?? 'Not specified'); ?></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="details-section">
                                    <h3>Medical Information</h3>
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">Allergies</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['allergies'] ?? 'None'); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Current Medications</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['current_medications'] ?? 'None'); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Medical History</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['medical_history'] ?? 'None'); ?></span>
                                        </div>
                                    </div>
                                </div>

                                <div class="details-section">
                                    <h3>Account Information</h3>
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">Username</span>
                                            <span class="detail-value"><?php echo htmlspecialchars($patient['username']); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Account Created</span>
                                            <span class="detail-value"><?php echo date('F d, Y', strtotime($patient['account_created'])); ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Appointments Section -->
                <div class="card">
                    <div class="card-header">
                        <h3>Appointment History</h3>
                        <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> New Appointment
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Doctor</th>
                                        <th>Department</th>
                                        <th>Date & Time</th>
                                        <th>Status</th>
                                        <th>Reason</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($appointments) > 0): ?>
                                        <?php foreach ($appointments as $appointment): ?>
                                            <tr>
                                                <td><?php echo $appointment['appointment_id']; ?></td>
                                                <td>Dr. <?php echo htmlspecialchars($appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']); ?></td>
                                                <td><?php echo htmlspecialchars($appointment['department_name']); ?></td>
                                                <td>
                                                    <?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?><br>
                                                    <span class="text-muted"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></span>
                                                </td>
                                                <td>
                                                    <span class="status-badge status-<?php echo $appointment['status']; ?>">
                                                        <?php echo ucfirst($appointment['status']); ?>
                                                    </span>
                                                </td>
                                                <td><?php echo htmlspecialchars(substr($appointment['reason'], 0, 30)) . (strlen($appointment['reason']) > 30 ? '...' : ''); ?></td>
                                                <td>
                                                    <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <a href="edit_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="Edit">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center">No appointments found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Medical Records Section (if you have this functionality) -->
                <?php if (!empty($medical_records)): ?>
                <div class="card">
                    <div class="card-header">
                        <h3>Medical Records</h3>
                        <a href="add_medical_record.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-sm btn-primary">
                            <i class="fas fa-plus"></i> Add Record
                        </a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Doctor</th>
                                        <th>Diagnosis</th>
                                        <th>Treatment</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($medical_records as $record): ?>
                                        <tr>
                                            <td><?php echo date('M d, Y', strtotime($record['record_date'])); ?></td>
                                            <td>Dr. <?php echo htmlspecialchars($record['doctor_name']); ?></td>
                                            <td><?php echo htmlspecialchars($record['diagnosis']); ?></td>
                                            <td><?php echo htmlspecialchars(substr($record['treatment'], 0, 30)) . (strlen($record['treatment']) > 30 ? '...' : ''); ?></td>
                                            <td>
                                                <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
