-- SQL script to update the prescriptions table for email notifications
-- Add columns for tracking email notifications

-- Check if the columns already exist before adding them
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'prescriptions' AND COLUMN_NAME = 'notification_sent';

-- Add notification_sent column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE prescriptions ADD COLUMN notification_sent TINYINT(1) DEFAULT 0 COMMENT "Flag indicating if notification email was sent"',
    'SELECT "Column notification_sent already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the notification_sent_date column exists
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'prescriptions' AND COLUMN_NAME = 'notification_sent_date';

-- Add notification_sent_date column if it doesn't exist
SET @query = IF(@columnExists = 0, 
    'ALTER TABLE prescriptions ADD COLUMN notification_sent_date DATETIME NULL COMMENT "Date and time when notification email was sent"',
    'SELECT "Column notification_sent_date already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create an index for faster queries when checking notification status
SET @indexExists = 0;
SELECT COUNT(*) INTO @indexExists FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'prescriptions' AND INDEX_NAME = 'idx_prescription_notification';

-- Add index if it doesn't exist
SET @query = IF(@indexExists = 0, 
    'CREATE INDEX idx_prescription_notification ON prescriptions(prescription_date, status, notification_sent)',
    'SELECT "Index idx_prescription_notification already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
