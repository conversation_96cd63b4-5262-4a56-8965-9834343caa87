<?php
// <PERSON><PERSON><PERSON> to fix family medical history consistency
// This script ensures the family_medical_history column exists in medical_records table
// and provides a way to sync data between patients.medical_history and medical_records.family_medical_history

require_once "../../includes/db_connect.php";

echo "<h2>Fixing Family Medical History Consistency</h2>";

// Check if family_medical_history column exists in medical_records table
$check_column_query = "SHOW COLUMNS FROM medical_records LIKE 'family_medical_history'";
$check_column_result = $conn->query($check_column_query);
$column_exists = $check_column_result->num_rows > 0;

if (!$column_exists) {
    echo "<p>Adding family_medical_history column to medical_records table...</p>";
    $add_column_query = "ALTER TABLE medical_records ADD COLUMN family_medical_history TEXT DEFAULT NULL COMMENT 'Family medical history'";
    if ($conn->query($add_column_query)) {
        echo "<p style='color: green;'>✓ Successfully added family_medical_history column to medical_records table</p>";
    } else {
        echo "<p style='color: red;'>✗ Error adding column: " . $conn->error . "</p>";
    }
} else {
    echo "<p style='color: blue;'>ℹ family_medical_history column already exists in medical_records table</p>";
}

// Check current data status
echo "<h3>Data Status Check</h3>";

// Count medical records with family medical history
$count_query = "SELECT COUNT(*) as count FROM medical_records WHERE family_medical_history IS NOT NULL AND family_medical_history != ''";
$count_result = $conn->query($count_query);
$count_row = $count_result->fetch_assoc();
echo "<p>Medical records with family medical history: " . $count_row['count'] . "</p>";

// Count patients with medical history
$patient_count_query = "SELECT COUNT(*) as count FROM patients WHERE medical_history IS NOT NULL AND medical_history != ''";
$patient_count_result = $conn->query($patient_count_query);
$patient_count_row = $patient_count_result->fetch_assoc();
echo "<p>Patients with medical history: " . $patient_count_row['count'] . "</p>";

echo "<h3>Migration Complete</h3>";
echo "<p>The family medical history system is now consistent. All medical records will use the family_medical_history field in the medical_records table.</p>";

$conn->close();
?>
