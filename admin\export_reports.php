<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get export parameters
$type = isset($_GET['type']) ? $_GET['type'] : 'csv';
$startDate = isset($_GET['start']) ? $_GET['start'] : date('Y-m-d', strtotime('-30 days'));
$endDate = isset($_GET['end']) ? $_GET['end'] : date('Y-m-d');

// Set headers based on export type
if ($type == 'csv') {
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="hospital_report_' . date('Y-m-d') . '.csv"');
} elseif ($type == 'pdf') {
    // For PDF, we'd typically use a library like FPDF or TCPDF
    // This is a simplified example
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="hospital_report_' . date('Y-m-d') . '.pdf"');
    
    // In a real implementation, you'd generate a PDF here
    echo "PDF generation would happen here using a library like FPDF or TCPDF";
    exit();
}

// For CSV export
$output = fopen('php://output', 'w');

// Write CSV headers
fputcsv($output, ['Hospital Management System Report', '', '', '']);
fputcsv($output, ['Report Period:', $startDate, 'to', $endDate]);
fputcsv($output, ['Generated on:', date('Y-m-d H:i:s'), '', '']);
fputcsv($output, ['', '', '', '']);

// Get appointment statistics by status
$statusQuery = "CALL sp_appointment_statistics(?, ?)";
$stmt = $conn->prepare($statusQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$statusResult = $stmt->get_result();
$stmt->close();

// Write appointment status section
fputcsv($output, ['APPOINTMENT STATUS', '', '', '']);
fputcsv($output, ['Status', 'Count', '', '']);

$totalAppointments = 0;
while ($row = $statusResult->fetch_assoc()) {
    fputcsv($output, [$row['status'], $row['count'], '', '']);
    $totalAppointments += $row['count'];
}
fputcsv($output, ['Total', $totalAppointments, '', '']);
fputcsv($output, ['', '', '', '']);

// Get appointment statistics by department
$deptQuery = "CALL sp_department_statistics(?, ?)";
$stmt = $conn->prepare($deptQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$deptResult = $stmt->get_result();
$stmt->close();

// Write department section
fputcsv($output, ['APPOINTMENTS BY DEPARTMENT', '', '', '']);
fputcsv($output, ['Department', 'Count', '', '']);

while ($row = $deptResult->fetch_assoc()) {
    fputcsv($output, [$row['department'], $row['count'], '', '']);
}
fputcsv($output, ['', '', '', '']);

// Get top doctors by appointment count
$doctorQuery = "CALL sp_top_doctors(?, ?, 5)";
$stmt = $conn->prepare($doctorQuery);
$stmt->bind_param("ssi", $startDate, $endDate, $limit);
$limit = 5;
$stmt->execute();
$doctorResult = $stmt->get_result();
$stmt->close();

// Write top doctors section
fputcsv($output, ['TOP DOCTORS BY APPOINTMENT COUNT', '', '', '']);
fputcsv($output, ['Doctor Name', 'Specialization', 'Appointment Count', '']);

while ($row = $doctorResult->fetch_assoc()) {
    fputcsv($output, ['Dr. ' . $row['doctor_name'], $row['specialization'], $row['appointment_count'], '']);
}
fputcsv($output, ['', '', '', '']);

// Get daily appointment counts
$dailyQuery = "CALL sp_daily_appointments(?, ?)";
$stmt = $conn->prepare($dailyQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$dailyResult = $stmt->get_result();
$stmt->close();

// Write daily appointments section
fputcsv($output, ['DAILY APPOINTMENT COUNTS', '', '', '']);
fputcsv($output, ['Date', 'Count', '', '']);

while ($row = $dailyResult->fetch_assoc()) {
    fputcsv($output, [date('M d, Y', strtotime($row['date'])), $row['count'], '', '']);
}
fputcsv($output, ['', '', '', '']);

// Get payment statistics
$paymentQuery = "CALL sp_payment_statistics(?, ?)";
$stmt = $conn->prepare($paymentQuery);
$stmt->bind_param("ss", $startDate, $endDate);
$stmt->execute();
$paymentResult = $stmt->get_result();
$stmt->close();

// Write payment statistics section
fputcsv($output, ['PAYMENT STATUS', '', '', '']);
fputcsv($output, ['Status', 'Count', '', '']);

while ($row = $paymentResult->fetch_assoc()) {
    fputcsv($output, [$row['payment'], $row['count'], '', '']);
}

// Close the output stream
fclose($output);
exit();
?>
