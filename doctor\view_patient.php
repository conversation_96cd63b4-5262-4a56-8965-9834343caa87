<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if patient ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: patients.php");
    exit();
}

$patient_id = $_GET['id'];

// Get patient details with user information
$patient = null;
$query = "SELECT p.*, u.email, u.last_login
          FROM patients p
          JOIN users u ON p.user_id = u.user_id
          WHERE p.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("i", $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $patient = $result->fetch_assoc();
} else {
    // Patient not found
    header("Location: patients.php");
    exit();
}

// Check if this doctor has seen this patient
$has_seen_patient = false;
$check_query = "SELECT COUNT(*) as count FROM appointments WHERE doctor_id = ? AND patient_id = ?";
$check_stmt = $conn->prepare($check_query);
$check_stmt->bind_param("ii", $doctor_id, $patient_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();
if ($check_result->fetch_assoc()['count'] > 0) {
    $has_seen_patient = true;
}

// If doctor has not seen this patient, redirect
if (!$has_seen_patient) {
    header("Location: patients.php");
    exit();
}

// Get patient's appointment history with this doctor
$appointments = [];
$appointments_query = "SELECT a.*, dep.department_name
                      FROM appointments a
                      JOIN departments dep ON a.department_id = dep.department_id
                      WHERE a.doctor_id = ? AND a.patient_id = ?
                      ORDER BY a.appointment_date DESC, a.appointment_time DESC";
$appointments_stmt = $conn->prepare($appointments_query);
$appointments_stmt->bind_param("ii", $doctor_id, $patient_id);
$appointments_stmt->execute();
$appointments_result = $appointments_stmt->get_result();
if ($appointments_result->num_rows > 0) {
    while ($row = $appointments_result->fetch_assoc()) {
        $appointments[] = $row;
    }
}

// Get patient's medical records created by this doctor
$medical_records = [];
$records_query = "SELECT mr.*, a.appointment_date
                 FROM medical_records mr
                 LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
                 WHERE mr.doctor_id = ? AND mr.patient_id = ?
                 ORDER BY mr.created_at DESC";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param("ii", $doctor_id, $patient_id);
$records_stmt->execute();
$records_result = $records_stmt->get_result();
if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}

// Get patient's prescriptions created by this doctor
$prescriptions = [];
$prescriptions_query = "SELECT p.*
                       FROM prescriptions p
                       WHERE p.doctor_id = ? AND p.patient_id = ?
                       ORDER BY p.created_at DESC";
$prescriptions_stmt = $conn->prepare($prescriptions_query);
$prescriptions_stmt->bind_param("ii", $doctor_id, $patient_id);
$prescriptions_stmt->execute();
$prescriptions_result = $prescriptions_stmt->get_result();
if ($prescriptions_result->num_rows > 0) {
    while ($row = $prescriptions_result->fetch_assoc()) {
        $prescriptions[] = $row;
    }
}

// Calculate patient age
$age = date_diff(date_create($patient['date_of_birth']), date_create('today'))->y;
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Patient Profile - <?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?> | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .patient-header {
            background: linear-gradient(135deg, #4a7c59 0%, #3a6c49 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .patient-header-content {
            display: flex;
            align-items: center;
            gap: 25px;
        }
        .patient-avatar {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            border: 4px solid rgba(255,255,255,0.3);
            object-fit: cover;
        }
        .patient-basic-info h2 {
            margin: 0 0 10px 0;
            font-size: 28px;
            font-weight: 600;
        }
        .patient-meta {
            display: flex;
            gap: 30px;
            margin-top: 15px;
        }
        .meta-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            opacity: 0.9;
        }
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 30px;
        }
        .info-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
            border-left: 4px solid #4a7c59;
        }
        .info-card h3 {
            margin: 0 0 20px 0;
            color: #4a7c59;
            font-size: 18px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .detail-row {
            display: flex;
            justify-content: space-between;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-row:last-child {
            border-bottom: none;
        }
        .detail-label {
            font-weight: 600;
            color: #555;
            min-width: 120px;
        }
        .detail-value {
            color: #333;
            flex: 1;
            text-align: right;
        }
        .section-tabs {
            display: flex;
            background: #f8f9fa;
            border-radius: 10px;
            padding: 5px;
            margin-bottom: 25px;
        }
        .tab-button {
            flex: 1;
            padding: 12px 20px;
            background: none;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .tab-button.active {
            background: white;
            color: #4a7c59;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .quick-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 25px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.08);
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #4a7c59;
            margin-bottom: 5px;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li class="active">
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user-injured"></i> Patient Profile</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="action-bar">
                    <div class="actions">
                        <a href="patients.php" class="btn btn-secondary"><i class="fas fa-arrow-left"></i> Back to Patients</a>
                        <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-primary"><i class="fas fa-calendar-plus"></i> Schedule Appointment</a>
                        <a href="add_medical_record.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-success"><i class="fas fa-file-medical"></i> Add Medical Record</a>
                        <a href="add_prescription.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-info"><i class="fas fa-prescription"></i> Write Prescription</a>
                    </div>
                </div>

                <!-- Patient Header -->
                <div class="patient-header">
                    <div class="patient-header-content">
                        <img src="../assets/images/<?php echo $patient['profile_image']; ?>" alt="Patient" class="patient-avatar">
                        <div class="patient-basic-info">
                            <h2><?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?></h2>
                            <div class="patient-meta">
                                <div class="meta-item">
                                    <i class="fas fa-id-card"></i>
                                    <span>ID: <?php echo $patient['patient_id']; ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-birthday-cake"></i>
                                    <span><?php echo $age; ?> years old</span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-venus-mars"></i>
                                    <span><?php echo ucfirst($patient['gender']); ?></span>
                                </div>
                                <div class="meta-item">
                                    <i class="fas fa-tint"></i>
                                    <span><?php echo $patient['blood_type'] ?? 'Unknown'; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="quick-stats">
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($appointments); ?></div>
                        <div class="stat-label">Total Appointments</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($medical_records); ?></div>
                        <div class="stat-label">Medical Records</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count($prescriptions); ?></div>
                        <div class="stat-label">Prescriptions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number"><?php echo count(array_filter($appointments, function($a) { return $a['status'] == 'completed'; })); ?></div>
                        <div class="stat-label">Completed Visits</div>
                    </div>
                </div>

                <!-- Patient Information Grid -->
                <div class="info-grid">
                    <!-- Personal Information -->
                    <div class="info-card">
                        <h3><i class="fas fa-user"></i> Personal Information</h3>
                        <div class="detail-row">
                            <span class="detail-label">Full Name:</span>
                            <span class="detail-value"><?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Date of Birth:</span>
                            <span class="detail-value"><?php echo date('F d, Y', strtotime($patient['date_of_birth'])); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Gender:</span>
                            <span class="detail-value"><?php echo ucfirst($patient['gender']); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Blood Type:</span>
                            <span class="detail-value"><?php echo $patient['blood_type'] ?? 'Not specified'; ?></span>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="info-card">
                        <h3><i class="fas fa-phone"></i> Contact Information</h3>
                        <div class="detail-row">
                            <span class="detail-label">Phone:</span>
                            <span class="detail-value"><?php echo $patient['phone']; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Email:</span>
                            <span class="detail-value"><?php echo $patient['email']; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Address:</span>
                            <span class="detail-value"><?php echo $patient['address']; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Emergency Contact:</span>
                            <span class="detail-value"><?php echo $patient['emergency_contact_name'] ?? 'Not provided'; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Emergency Phone:</span>
                            <span class="detail-value"><?php echo $patient['emergency_contact_phone'] ?? 'Not provided'; ?></span>
                        </div>
                    </div>

                    <!-- Medical Information -->
                    <div class="info-card">
                        <h3><i class="fas fa-heartbeat"></i> Medical Information</h3>
                        <div class="detail-row">
                            <span class="detail-label">Allergies:</span>
                            <span class="detail-value"><?php echo $patient['allergies'] ?? 'None reported'; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Current Medications:</span>
                            <span class="detail-value"><?php echo $patient['current_medications'] ?? 'None reported'; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Medical History:</span>
                            <span class="detail-value"><?php echo $patient['medical_history'] ? substr($patient['medical_history'], 0, 100) . '...' : 'None reported'; ?></span>
                        </div>
                    </div>

                    <!-- Account Information -->
                    <div class="info-card">
                        <h3><i class="fas fa-user-cog"></i> Account Information</h3>
                        <div class="detail-row">
                            <span class="detail-label">Registration Date:</span>
                            <span class="detail-value"><?php echo date('F d, Y', strtotime($patient['created_at'])); ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Last Login:</span>
                            <span class="detail-value"><?php echo $patient['last_login'] ? date('F d, Y g:i A', strtotime($patient['last_login'])) : 'Never'; ?></span>
                        </div>
                        <div class="detail-row">
                            <span class="detail-label">Patient Status:</span>
                            <span class="detail-value"><span class="status-badge status-active">Active</span></span>
                        </div>
                    </div>
                </div>

                <!-- Tabbed Content Section -->
                <div class="section-tabs">
                    <button class="tab-button active" onclick="showTab('appointments')">
                        <i class="fas fa-calendar-check"></i> Appointments (<?php echo count($appointments); ?>)
                    </button>
                    <button class="tab-button" onclick="showTab('medical-records')">
                        <i class="fas fa-file-medical"></i> Medical Records (<?php echo count($medical_records); ?>)
                    </button>
                    <button class="tab-button" onclick="showTab('prescriptions')">
                        <i class="fas fa-prescription"></i> Prescriptions (<?php echo count($prescriptions); ?>)
                    </button>
                    <button class="tab-button" onclick="showTab('family-history')">
                        <i class="fas fa-dna"></i> Family History
                    </button>
                </div>

                <!-- Appointments Tab -->
                <div id="appointments" class="tab-content active">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-calendar-check"></i> Appointment History</h3>
                            <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-primary">
                                <i class="fas fa-calendar-plus"></i> Schedule New
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (count($appointments) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Time</th>
                                                <th>Department</th>
                                                <th>Reason</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($appointments as $appointment): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($appointment['appointment_date'])); ?></td>
                                                    <td><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></td>
                                                    <td><?php echo $appointment['department_name']; ?></td>
                                                    <td><?php echo $appointment['reason']; ?></td>
                                                    <td><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></td>
                                                    <td>
                                                        <a href="view_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <?php if ($appointment['status'] == 'scheduled' || $appointment['status'] == 'confirmed'): ?>
                                                            <a href="edit_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn-icon" title="Edit">
                                                                <i class="fas fa-edit"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-calendar-times empty-icon"></i>
                                    <p>No appointments found for this patient.</p>
                                    <a href="add_appointment.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-primary">
                                        <i class="fas fa-calendar-plus"></i> Schedule First Appointment
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Medical Records Tab -->
                <div id="medical-records" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-file-medical"></i> Medical Records</h3>
                            <a href="add_medical_record.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-success">
                                <i class="fas fa-plus"></i> Add Record
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (count($medical_records) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Diagnosis</th>
                                                <th>Treatment</th>
                                                <th>Symptoms</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($medical_records as $record): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                    <td><?php echo substr($record['diagnosis'], 0, 50) . (strlen($record['diagnosis']) > 50 ? '...' : ''); ?></td>
                                                    <td><?php echo substr($record['treatment'], 0, 50) . (strlen($record['treatment']) > 50 ? '...' : ''); ?></td>
                                                    <td><?php echo substr($record['symptoms'], 0, 40) . (strlen($record['symptoms']) > 40 ? '...' : ''); ?></td>
                                                    <td>
                                                        <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="print_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="Print" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-file-medical empty-icon"></i>
                                    <p>No medical records found for this patient.</p>
                                    <a href="add_medical_record.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-success">
                                        <i class="fas fa-plus"></i> Add First Record
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Prescriptions Tab -->
                <div id="prescriptions" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-prescription"></i> Prescriptions</h3>
                            <a href="add_prescription.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-info">
                                <i class="fas fa-plus"></i> Write Prescription
                            </a>
                        </div>
                        <div class="card-body">
                            <?php if (count($prescriptions) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date Created</th>
                                                <th>Prescription ID</th>
                                                <th>Prescription Date</th>
                                                <th>Status</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($prescriptions as $prescription): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($prescription['created_at'])); ?></td>
                                                    <td><?php echo $prescription['prescription_id']; ?></td>
                                                    <td><?php echo $prescription['prescription_date'] ? date('M d, Y', strtotime($prescription['prescription_date'])) : 'N/A'; ?></td>
                                                    <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                    <td>
                                                        <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="print_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Print" target="_blank">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-prescription empty-icon"></i>
                                    <p>No prescriptions found for this patient.</p>
                                    <a href="add_prescription.php?patient_id=<?php echo $patient_id; ?>" class="btn btn-info">
                                        <i class="fas fa-plus"></i> Write First Prescription
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Family History Tab -->
                <div id="family-history" class="tab-content">
                    <div class="card">
                        <div class="card-header">
                            <h3><i class="fas fa-dna"></i> Family Medical History</h3>
                        </div>
                        <div class="card-body">
                            <div class="family-medical-content">
                                <?php if (!empty($patient['medical_history'])): ?>
                                    <div class="family-history-display">
                                        <?php echo nl2br(htmlspecialchars($patient['medical_history'])); ?>
                                    </div>
                                <?php else: ?>
                                    <div class="empty-state">
                                        <i class="fas fa-dna empty-icon"></i>
                                        <p>No family medical history recorded for this patient.</p>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }
    </script>
</body>
</html>