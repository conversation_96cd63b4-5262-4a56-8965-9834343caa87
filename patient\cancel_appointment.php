<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

$error = "";
$success = "";

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = $_GET['id'];

// Get appointment details
$appointment = null;
$query = "SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          dep.department_name
          FROM appointments a
          JOIN doctors d ON a.doctor_id = d.doctor_id
          JOIN departments dep ON a.department_id = dep.department_id
          WHERE a.appointment_id = ? AND a.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $appointment_id, $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $appointment = $result->fetch_assoc();

    // Check if appointment can be cancelled
    if ($appointment['status'] != 'scheduled') {
        $error = "Only scheduled appointments can be cancelled.";
    } elseif (strtotime($appointment['appointment_date']) < time()) {
        $error = "Past appointments cannot be cancelled.";
    }
} else {
    header("Location: appointments.php");
    exit();
}

// Process cancellation
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['cancel_appointment'])) {
    $cancellation_reason = trim($_POST['cancellation_reason']);

    // Validate cancellation reason
    if (empty($cancellation_reason)) {
        $error = "Please provide a reason for cancellation";
    } else {

    // Update appointment status
    $update_query = "UPDATE appointments SET
                    status = 'cancelled',
                    cancellation_reason = ?,
                    cancelled_at = NOW(),
                    updated_at = NOW()
                    WHERE appointment_id = ? AND patient_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("sii", $cancellation_reason, $appointment_id, $patient_id);

    if ($update_stmt->execute()) {
        $success = "Appointment cancelled successfully.";

        // Refresh appointment data
        $stmt = $conn->prepare($query);
        $stmt->bind_param("ii", $appointment_id, $patient_id);
        $stmt->execute();
        $result = $stmt->get_result();
        $appointment = $result->fetch_assoc();

        // Send email notification about cancellation
        try {
            // Get doctor information for the email
            $doctor_query = "SELECT d.* FROM doctors d WHERE d.doctor_id = ?";
            $doctor_stmt = $conn->prepare($doctor_query);
            $doctor_stmt->bind_param("i", $appointment['doctor_id']);
            $doctor_stmt->execute();
            $doctor_result = $doctor_stmt->get_result();
            $doctor_info = $doctor_result->fetch_assoc();

            // Check if Mailer class exists and send email
            if (class_exists('Mailer')) {
                require_once "../classes/Mailer.php";
                $mailer = new Mailer();

                // Check if the sendAppointmentCancellation method exists
                if (method_exists($mailer, 'sendAppointmentCancellation')) {
                    $email_result = $mailer->sendAppointmentCancellation($appointment, $patient_info, $doctor_info);

                    // Log email details
                    error_log("Attempting to send appointment cancellation email");

                    if ($email_result) {
                        error_log("Cancellation email sent successfully");
                    } else {
                        error_log("Failed to send cancellation email");
                    }
                } else {
                    // Fallback to sendAppointmentConfirmation with status = cancelled
                    $email_result = $mailer->sendAppointmentConfirmation($appointment, $patient_info, $doctor_info);
                    error_log("Using confirmation email for cancellation notification");
                }
            }
        } catch (Exception $e) {
            error_log("Exception sending cancellation email: " . $e->getMessage());
        }

        // Redirect to appointments page after successful cancellation
        header("Location: appointments.php?cancelled=1");
        exit();
    } else {
        $error = "Error cancelling appointment: {$conn->error}";
    }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cancel Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .appointment-details {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border-left: 4px solid #dc3545;
        }

        .detail-row {
            display: flex;
            margin-bottom: 10px;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }

        .detail-row:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .detail-label {
            font-weight: bold;
            width: 150px;
            color: #555;
        }

        .detail-value {
            flex: 1;
        }

        .cancellation-warning {
            background-color: #fff5f5;
            border: 1px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
        }

        .cancellation-warning i {
            color: #dc3545;
            font-size: 24px;
            margin-right: 15px;
        }

        .cancellation-warning p {
            margin: 0;
            color: #dc3545;
            font-weight: 500;
        }

        textarea[name="cancellation_reason"] {
            border-left: 3px solid #dc3545;
        }

        .form-actions {
            display: flex;
            justify-content: space-between;
            margin-top: 20px;
        }

        .btn-danger {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-times"></i> Cancel Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="back-link">
                    <a href="appointments.php"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h3>Cancel Appointment #<?php echo $appointment_id; ?></h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                                <div class="mt-3">
                                    <a href="appointments.php" class="btn btn-primary">Back to Appointments</a>
                                </div>
                            </div>
                        <?php elseif (!empty($success)): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                                <div class="mt-3">
                                    <a href="appointments.php" class="btn btn-primary">Back to Appointments</a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="appointment-details">
                                <div class="detail-row">
                                    <div class="detail-label">Doctor:</div>
                                    <div class="detail-value">Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Department:</div>
                                    <div class="detail-value"><?php echo $appointment['department_name']; ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Date:</div>
                                    <div class="detail-value"><?php echo date('F d, Y', strtotime($appointment['appointment_date'])); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Time:</div>
                                    <div class="detail-value"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></div>
                                </div>
                                <div class="detail-row">
                                    <div class="detail-label">Status:</div>
                                    <div class="detail-value"><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></div>
                                </div>
                            </div>

                            <div class="cancellation-warning">
                                <i class="fas fa-exclamation-triangle"></i>
                                <p>Are you sure you want to cancel this appointment? This action cannot be undone.</p>
                            </div>

                            <form action="" method="post">
                                <div class="form-group">
                                    <label for="cancellation_reason">Reason for Cancellation: <span style="color: #dc3545;">*</span></label>
                                    <textarea name="cancellation_reason" id="cancellation_reason" rows="3" required placeholder="Please explain why you need to cancel this appointment"></textarea>
                                    <small class="form-text">This information helps us improve our services.</small>
                                </div>

                                <div class="form-actions">
                                    <a href="appointments.php" class="btn btn-secondary">No, Keep Appointment</a>
                                    <button type="submit" name="cancel_appointment" class="btn btn-danger">Yes, Cancel Appointment</button>
                                </div>
                            </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
