# Database Directory

This directory contains database-related files for the CSUCC Hospital Management System.

## Subdirectories

1. **migrations/**
   - Contains database migration scripts
   - Used to update the database schema when changes are needed
   - See the README in that directory for more information

## Files in This Directory

1. **db_connect.php**
   - Establishes a connection to the MySQL database
   - Contains database credentials and connection parameters
   - This is a copy of the file in the root directory for better organization
   - The original file is still used by most of the application

## Related Files in Root Directory

1. **db_connect.php**
   - Original database connection file
   - Used by most files in the application
   - Kept in the root directory to avoid breaking existing code

2. **sql/**
   - Directory containing SQL scripts
   - Original location of SQL migration files
   - These files have been copied to the migrations directory for better organization

## Database Schema

The CSUCC Hospital Management System uses a MySQL database with the following main tables:

1. **users**
   - Stores user account information
   - Contains fields for username, email, password, role, etc.

2. **patients**
   - Stores patient information
   - Contains fields for personal details, medical history, etc.
   - Linked to the users table

3. **doctors**
   - Stores doctor information
   - Contains fields for specialization, department, etc.
   - Linked to the users table

4. **appointments**
   - Stores appointment information
   - Contains fields for date, time, status, etc.
   - Linked to the patients and doctors tables

5. **departments**
   - Stores department information
   - Contains fields for department name, description, etc.

6. **medical_records**
   - Stores patient medical records
   - Contains fields for diagnosis, treatment, etc.
   - Linked to the patients table

7. **prescriptions**
   - Stores prescription information
   - Contains fields for medication, dosage, etc.
   - Linked to the medical_records table

## Backup and Restore

It's recommended to regularly back up the database to prevent data loss. You can use the following commands:

```bash
# Backup
mysqldump -u username -p database_name > backup_file.sql

# Restore
mysql -u username -p database_name < backup_file.sql
```

Replace `username` with your MySQL username and `database_name` with the name of your database.
