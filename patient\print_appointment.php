<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = $_GET['id'];

// Get appointment details
$appointment = null;
$query = "SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          d.specialization, d.profile_image as doctor_profile_image, d.phone as doctor_phone,
          dep.department_name
          FROM appointments a
          JOIN doctors d ON a.doctor_id = d.doctor_id
          JOIN departments dep ON a.department_id = dep.department_id
          WHERE a.appointment_id = ? AND a.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $appointment_id, $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $appointment = $result->fetch_assoc();
} else {
    header("Location: appointments.php");
    exit();
}

// Get patient's medical history
$medical_query = "SELECT medical_history FROM patients WHERE patient_id = ?";
$medical_stmt = $conn->prepare($medical_query);
$medical_stmt->bind_param("i", $patient_id);
$medical_stmt->execute();
$medical_result = $medical_stmt->get_result();
$medical_info = $medical_result->fetch_assoc();

// Only allow printing for confirmed or scheduled appointments
if ($appointment['status'] != 'confirmed' && $appointment['status'] != 'scheduled') {
    header("Location: view_appointment.php?id=" . $appointment_id);
    exit();
}

// Set hospital information (hardcoded since we don't have a hospitals table)
$appointment['hospital_name'] = "CSUCC Hospital";
$appointment['hospital_address'] = "123 University Ave, Caraga State University, Butuan City";
$appointment['hospital_phone'] = "(*************";

// Generate a unique appointment reference number
$reference_number = strtoupper(substr(md5($appointment_id . $patient_id), 0, 8));
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Confirmation | CSUCC Hospital</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
        }

        .print-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border: 1px solid #ddd;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            border-bottom: 2px solid #4a7c59;
            padding-bottom: 10px;
        }

        .header h1 {
            color: #4a7c59;
            margin-bottom: 3px;
            font-size: 22px;
        }

        .header p {
            color: #666;
            margin: 3px 0;
            font-size: 13px;
        }

        .appointment-info {
            margin-bottom: 15px;
        }

        .appointment-info h2 {
            color: #4a7c59;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
            margin-bottom: 10px;
            font-size: 16px;
        }

        .info-row {
            display: flex;
            margin-bottom: 8px;
        }

        .info-label {
            font-weight: bold;
            width: 150px;
            font-size: 13px;
        }

        .info-value {
            flex: 1;
            font-size: 13px;
        }

        .status {
            display: inline-block;
            padding: 3px 6px;
            border-radius: 3px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 10px;
        }

        .status-confirmed {
            background-color: #d4edda;
            color: #155724;
        }

        .status-scheduled {
            background-color: #fff3cd;
            color: #856404;
        }

        .reference {
            text-align: center;
            margin: 15px 0;
            padding: 8px;
            background-color: #f8f9fa;
            border: 1px dashed #ccc;
            border-radius: 4px;
        }

        .reference h3 {
            margin: 0 0 5px 0;
            color: #4a7c59;
            font-size: 14px;
        }

        .reference p {
            font-size: 18px;
            font-weight: bold;
            letter-spacing: 2px;
            margin: 0;
        }

        .footer {
            margin-top: 20px;
            text-align: center;
            font-size: 11px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 10px;
        }

        .note {
            background-color: #f8f9fa;
            padding: 8px;
            border-left: 4px solid #4a7c59;
            margin: 10px 0;
        }

        .note h3 {
            margin-top: 0;
            margin-bottom: 5px;
            color: #4a7c59;
            font-size: 14px;
        }

        .note p {
            margin: 5px 0;
            font-size: 12px;
        }

        .print-button {
            text-align: center;
            margin: 20px 0;
        }

        .print-button button {
            background-color: #4a7c59;
            color: white;
            border: none;
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border-radius: 4px;
        }

        .print-button button:hover {
            background-color: #3a6c49;
        }

        .back-link {
            text-align: center;
            margin-top: 20px;
        }

        .back-link a {
            color: #4a7c59;
            text-decoration: none;
        }

        .back-link a:hover {
            text-decoration: underline;
        }

        @media print {
            body {
                background-color: #fff;
                padding: 0;
                margin: 0;
                font-size: 12px;
            }

            .print-container {
                box-shadow: none;
                border: none;
                padding: 0;
                max-width: 100%;
                width: 100%;
            }

            .print-button, .back-link {
                display: none;
            }

            /* Ensure page fits on a single page */
            .header {
                margin-bottom: 10px;
                padding-bottom: 5px;
            }

            .reference {
                margin: 10px 0;
                padding: 5px;
            }

            .appointment-info {
                margin-bottom: 10px;
            }

            .info-row {
                margin-bottom: 5px;
            }

            .note {
                margin: 8px 0;
                padding: 5px;
            }

            .footer {
                margin-top: 10px;
                padding-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="print-container">
        <div class="print-button">
            <button onclick="window.print()"><i class="fas fa-print"></i> Print Appointment</button>
        </div>

        <div class="header">
            <h1><?php echo $appointment['hospital_name']; ?></h1>
            <p><?php echo $appointment['hospital_address']; ?></p>
            <p>Phone: <?php echo $appointment['hospital_phone']; ?></p>
        </div>

        <div class="reference">
            <h3>Appointment Reference Number</h3>
            <p><?php echo $reference_number; ?></p>
        </div>

        <div class="appointment-info">
            <h2>Appointment Details</h2>

            <div class="info-row">
                <div class="info-label">Status:</div>
                <div class="info-value">
                    <span class="status status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span>
                </div>
            </div>

            <div class="info-row">
                <div class="info-label">Date:</div>
                <div class="info-value"><?php echo date('l, F d, Y', strtotime($appointment['appointment_date'])); ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Time:</div>
                <div class="info-value"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Department:</div>
                <div class="info-value"><?php echo $appointment['department_name']; ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Doctor:</div>
                <div class="info-value">Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Reason for Visit:</div>
                <div class="info-value"><?php echo $appointment['reason']; ?></div>
            </div>
        </div>

        <div class="appointment-info">
            <h2>Patient Information</h2>

            <div class="info-row">
                <div class="info-label">Name:</div>
                <div class="info-value"><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Date of Birth:</div>
                <div class="info-value"><?php echo date('F d, Y', strtotime($patient_info['date_of_birth'])); ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Gender:</div>
                <div class="info-value"><?php echo ucfirst($patient_info['gender']); ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Contact Number:</div>
                <div class="info-value"><?php echo $patient_info['phone']; ?></div>
            </div>

            <div class="info-row">
                <div class="info-label">Email:</div>
                <div class="info-value"><?php echo $patient_info['email']; ?></div>
            </div>
        </div>

        <div class="note">
            <h3>Important Notes</h3>
            <p>Please arrive 15 minutes early. Bring this confirmation with you. For cancellations, please notify us 24 hours in advance.</p>
        </div>

        <div class="footer">
            <p>Generated: <?php echo date('m/d/Y h:i A'); ?> | &copy; <?php echo date('Y'); ?> <?php echo $appointment['hospital_name']; ?></p>
        </div>

        <div class="back-link">
            <a href="view_appointment.php?id=<?php echo $appointment_id; ?>">← Back to Appointment Details</a>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
</body>
</html>
