<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if prescription ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: prescriptions.php");
    exit();
}

$prescription_id = $_GET['id'];

// Get prescription details
$prescription = null;
$query = "SELECT p.*, pt.first_name as patient_first_name, pt.last_name as patient_last_name,
          pt.date_of_birth, pt.gender, pt.phone, pt.address, pt.profile_image as patient_profile_image,
          pt.allergies, pt.medical_history, pt.blood_type,
          u.email as patient_email
          FROM prescriptions p
          JOIN patients pt ON p.patient_id = pt.patient_id
          JOIN users u ON pt.user_id = u.user_id
          WHERE p.prescription_id = ? AND p.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("si", $prescription_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $prescription = $result->fetch_assoc();
} else {
    header("Location: prescriptions.php");
    exit();
}

// Get prescription medications
$medications = [];
$medications_query = "SELECT * FROM prescription_medications WHERE prescription_id = ? ORDER BY id";
$medications_stmt = $conn->prepare($medications_query);
$medications_stmt->bind_param("s", $prescription_id);
$medications_stmt->execute();
$medications_result = $medications_stmt->get_result();
if ($medications_result->num_rows > 0) {
    while ($row = $medications_result->fetch_assoc()) {
        $medications[] = $row;
    }
}

// Process status update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_status'])) {
    $new_status = $_POST['status'];

    $update_query = "UPDATE prescriptions SET status = ?, updated_at = NOW() WHERE prescription_id = ?";
    $update_stmt = $conn->prepare($update_query);
    $update_stmt->bind_param("ss", $new_status, $prescription_id);

    if ($update_stmt->execute()) {
        // Refresh prescription data
        $prescription['status'] = $new_status;
        $success_message = "Prescription status updated successfully.";
    } else {
        $error_message = "Error updating prescription status: " . $conn->error;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Prescription | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li class="active">
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> Schedule</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-prescription"></i> View Prescription</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['specialization']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if (isset($success_message)): ?>
                    <div class="alert alert-success">
                        <?php echo $success_message; ?>
                    </div>
                <?php endif; ?>

                <?php if (isset($error_message)): ?>
                    <div class="alert alert-danger">
                        <?php echo $error_message; ?>
                    </div>
                <?php endif; ?>

                <div class="back-link">
                    <a href="prescriptions.php"><i class="fas fa-arrow-left"></i> Back to Prescriptions</a>
                </div>

                <div class="prescription-header">
                    <div class="prescription-title">
                        <h3>Prescription #<?php echo $prescription_id; ?></h3>
                        <span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span>
                    </div>
                    <div class="prescription-actions">
                        <a href="print_prescription.php?id=<?php echo $prescription_id; ?>" class="btn btn-primary"><i class="fas fa-print"></i> Print</a>
                        <a href="edit_prescription.php?id=<?php echo $prescription_id; ?>" class="btn btn-secondary"><i class="fas fa-edit"></i> Edit</a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Prescription Details</h3>
                            </div>
                            <div class="card-body">
                                <div class="prescription-details">
                                    <div class="detail-row">
                                        <div class="detail-label">Date:</div>
                                        <div class="detail-value"><?php echo date('F d, Y', strtotime($prescription['prescription_date'])); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Status:</div>
                                        <div class="detail-value">
                                            <form action="" method="post" class="status-form">
                                                <select name="status" id="status" class="form-control-sm" onchange="this.form.submit()">
                                                    <option value="active" <?php echo $prescription['status'] == 'active' ? 'selected' : ''; ?>>Active</option>
                                                    <option value="completed" <?php echo $prescription['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                                    <option value="cancelled" <?php echo $prescription['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                                </select>
                                                <input type="hidden" name="update_status" value="1">
                                            </form>
                                        </div>
                                    </div>
                                    <?php /* Appointment link removed as appointment_id column doesn't exist */ ?>
                                    <?php /* Medical record link removed as record_id column doesn't exist */ ?>
                                    <?php if (!empty($prescription['notes'])): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">Notes:</div>
                                            <div class="detail-value"><?php echo nl2br($prescription['notes']); ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Created:</div>
                                        <div class="detail-value"><?php echo date('F d, Y h:i A', strtotime($prescription['created_at'])); ?></div>
                                    </div>
                                    <?php if (!empty($prescription['notification_sent']) && $prescription['notification_sent'] == 1): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">Notification:</div>
                                            <div class="detail-value">Email notification sent on <?php echo date('F d, Y h:i A', strtotime($prescription['notification_sent_date'])); ?></div>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <div class="medications-section">
                                    <h4>Medications</h4>
                                    <?php if (count($medications) > 0): ?>
                                        <div class="table-responsive">
                                            <table class="table">
                                                <thead>
                                                    <tr>
                                                        <th>Medication</th>
                                                        <th>Dosage</th>
                                                        <th>Frequency</th>
                                                        <th>Duration</th>
                                                        <th>Instructions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php foreach ($medications as $medication): ?>
                                                        <tr>
                                                            <td><?php echo $medication['medication_name']; ?></td>
                                                            <td><?php echo $medication['dosage']; ?></td>
                                                            <td><?php echo $medication['frequency']; ?></td>
                                                            <td><?php echo $medication['duration']; ?></td>
                                                            <td><?php echo $medication['instructions']; ?></td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                            </table>
                                        </div>
                                    <?php else: ?>
                                        <div class="empty-state small">
                                            <p>No medications found for this prescription.</p>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Patient Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="patient-profile">
                                    <div class="patient-image">
                                        <img src="../assets/images/<?php echo $prescription['patient_profile_image']; ?>" alt="Patient">
                                    </div>
                                    <div class="patient-details">
                                        <h4><?php echo $prescription['patient_first_name'] . ' ' . $prescription['patient_last_name']; ?></h4>
                                        <p><i class="fas fa-birthday-cake"></i> <?php echo date('M d, Y', strtotime($prescription['date_of_birth'])); ?> (<?php echo calculateAge($prescription['date_of_birth']); ?> years)</p>
                                        <p><i class="fas fa-venus-mars"></i> <?php echo ucfirst($prescription['gender']); ?></p>
                                        <p><i class="fas fa-phone"></i> <?php echo $prescription['phone']; ?></p>
                                        <p><i class="fas fa-envelope"></i> <?php echo $prescription['patient_email']; ?></p>
                                        <p><i class="fas fa-map-marker-alt"></i> <?php echo $prescription['address']; ?></p>
                                        <a href="view_patient.php?id=<?php echo $prescription['patient_id']; ?>" class="btn btn-sm btn-outline">View Patient Profile</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Medical Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="medical-info">
                                    <?php if (!empty($prescription['blood_type'])): ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Blood Type:</div>
                                        <div class="detail-value"><?php echo $prescription['blood_type']; ?></div>
                                    </div>
                                    <?php endif; ?>

                                    <div class="detail-row">
                                        <div class="detail-label">Allergies:</div>
                                        <div class="detail-value">
                                            <?php echo !empty($prescription['allergies']) ? nl2br($prescription['allergies']) : 'None reported'; ?>
                                        </div>
                                    </div>

                                    <div class="detail-row">
                                        <div class="detail-label">Medical History:</div>
                                        <div class="detail-value">
                                            <?php echo !empty($prescription['medical_history']) ? nl2br($prescription['medical_history']) : 'None reported'; ?>
                                        </div>
                                    </div>

                                    <?php
                                    // Get the most recent diagnosis from medical records
                                    $diagnosis_query = "SELECT diagnosis FROM medical_records
                                                      WHERE patient_id = ?
                                                      ORDER BY created_at DESC LIMIT 1";
                                    $diagnosis_stmt = $conn->prepare($diagnosis_query);
                                    $diagnosis_stmt->bind_param("i", $prescription['patient_id']);
                                    $diagnosis_stmt->execute();
                                    $diagnosis_result = $diagnosis_stmt->get_result();
                                    $diagnosis = '';
                                    if ($diagnosis_result->num_rows > 0) {
                                        $diagnosis = $diagnosis_result->fetch_assoc()['diagnosis'];
                                    }
                                    ?>

                                    <div class="detail-row">
                                        <div class="detail-label">Diagnosis:</div>
                                        <div class="detail-value">
                                            <?php echo !empty($diagnosis) ? nl2br($diagnosis) : 'No diagnosis recorded'; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </main>
    </div>

    <?php
    // Helper function to calculate age
    function calculateAge($dob) {
        $birthDate = new DateTime($dob);
        $today = new DateTime('today');
        $age = $birthDate->diff($today)->y;
        return $age;
    }
    ?>
</body>
</html>
