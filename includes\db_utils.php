<?php
/**
 * Database Utility Class
 * This class provides methods to fetch data from the database
 */

class DBUtils {
    /**
     * @var mysqli Database connection
     */
    private $conn;

    /**
     * Constructor
     *
     * @param mysqli $conn Database connection
     */
    public function __construct($conn) {
        $this->conn = $conn;
    }

    /**
     * Get appointment information by ID
     *
     * @param int $appointment_id Appointment ID
     * @return array|null Appointment information or null if not found
     */
    public function getAppointmentById($appointment_id) {
        $query = "SELECT a.*,
                  d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                  d.doctor_id, d.specialization as doctor_specialization,
                  p.first_name as patient_first_name, p.last_name as patient_last_name,
                  p.patient_id, p.gender as patient_gender, p.phone as patient_phone,
                  dep.department_name, u.email as patient_email
                  FROM appointments a
                  JOIN doctors d ON a.doctor_id = d.doctor_id
                  JOIN patients p ON a.patient_id = p.patient_id
                  JOIN departments dep ON a.department_id = dep.department_id
                  LEFT JOIN users u ON p.user_id = u.user_id
                  WHERE a.appointment_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $appointment_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $appointment = $result->fetch_assoc();

            // Add formatted fields for easier access
            $appointment['doctor_name'] = "Dr. " . $appointment['doctor_first_name'] . " " . $appointment['doctor_last_name'];
            $appointment['patient_name'] = $appointment['patient_first_name'] . " " . $appointment['patient_last_name'];

            return $appointment;
        }

        return null;
    }

    /**
     * Get patient information by ID
     *
     * @param int $patient_id Patient ID
     * @return array|null Patient information or null if not found
     */
    public function getPatientById($patient_id) {
        $query = "SELECT p.*, u.email, u.username, u.created_at as account_created
                  FROM patients p
                  JOIN users u ON p.user_id = u.user_id
                  WHERE p.patient_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $patient_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $patient = $result->fetch_assoc();

            // Add formatted fields for easier access
            $patient['full_name'] = $patient['first_name'] . " " . $patient['last_name'];

            return $patient;
        }

        return null;
    }

    /**
     * Get doctor information by ID
     *
     * @param int $doctor_id Doctor ID
     * @return array|null Doctor information or null if not found
     */
    public function getDoctorById($doctor_id) {
        $query = "SELECT d.*, u.email, u.username, u.created_at as account_created,
                  dep.department_name, dep.department_id
                  FROM doctors d
                  JOIN users u ON d.user_id = u.user_id
                  LEFT JOIN departments dep ON d.department_id = dep.department_id
                  WHERE d.doctor_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $doctor_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $doctor = $result->fetch_assoc();

            // Add formatted fields for easier access
            $doctor['full_name'] = "Dr. " . $doctor['first_name'] . " " . $doctor['last_name'];

            return $doctor;
        }

        return null;
    }

    /**
     * Get department information by ID
     *
     * @param int $department_id Department ID
     * @return array|null Department information or null if not found
     */
    public function getDepartmentById($department_id) {
        $query = "SELECT * FROM departments WHERE department_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $department_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }

        return null;
    }

    /**
     * Update appointment status
     *
     * @param int $appointment_id Appointment ID
     * @param string $status New status
     * @return bool Whether the update was successful
     */
    public function updateAppointmentStatus($appointment_id, $status) {
        $query = "UPDATE appointments SET status = ? WHERE appointment_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("si", $status, $appointment_id);
        return $stmt->execute();
    }

    /**
     * Mark appointment confirmation email as sent
     *
     * @param int $appointment_id Appointment ID
     * @return bool Whether the update was successful
     */
    public function markConfirmationSent($appointment_id) {
        $query = "UPDATE appointments SET confirmation_sent = 1, confirmation_sent_date = NOW() WHERE appointment_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $appointment_id);
        return $stmt->execute();
    }

    /**
     * Mark appointment reminder email as sent
     *
     * @param int $appointment_id Appointment ID
     * @return bool Whether the update was successful
     */
    public function markReminderSent($appointment_id) {
        $query = "UPDATE appointments SET reminder_sent = 1, reminder_sent_date = NOW() WHERE appointment_id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $appointment_id);
        return $stmt->execute();
    }

    /**
     * Get appointments that need confirmation emails
     *
     * @return array Appointments that need confirmation emails
     */
    public function getAppointmentsNeedingConfirmation() {
        $query = "SELECT a.appointment_id
                  FROM appointments a
                  WHERE a.status = 'Confirmed' AND a.confirmation_sent = 0";

        $result = $this->conn->query($query);
        $appointments = [];

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $appointments[] = $row['appointment_id'];
            }
        }

        return $appointments;
    }

    /**
     * Get appointments that need reminder emails
     *
     * @param int $hours_before Hours before appointment to send reminder
     * @return array Appointments that need reminder emails
     */
    public function getAppointmentsNeedingReminder($hours_before = 24) {
        $query = "SELECT a.appointment_id
                  FROM appointments a
                  WHERE a.status = 'Confirmed'
                  AND a.reminder_sent = 0
                  AND a.appointment_date = DATE(DATE_ADD(NOW(), INTERVAL ? HOUR))";

        $stmt = $this->conn->prepare($query);
        $stmt->bind_param("i", $hours_before);
        $stmt->execute();
        $result = $stmt->get_result();

        $appointments = [];

        if ($result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $appointments[] = $row['appointment_id'];
            }
        }

        return $appointments;
    }
}
?>
