<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = $_GET['id'];

// Get appointment details
$appointment = null;
$query = "SELECT a.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          d.specialization, d.profile_image as doctor_profile_image, d.phone as doctor_phone,
          dep.department_name
          FROM appointments a
          JOIN doctors d ON a.doctor_id = d.doctor_id
          JOIN departments dep ON a.department_id = dep.department_id
          WHERE a.appointment_id = ? AND a.patient_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $appointment_id, $patient_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $appointment = $result->fetch_assoc();
} else {
    header("Location: appointments.php");
    exit();
}

// Get medical records related to this appointment
$medical_records = [];
$records_query = "SELECT * FROM medical_records WHERE appointment_id = ?";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param("i", $appointment_id);
$records_stmt->execute();
$records_result = $records_stmt->get_result();
if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}

// Get prescriptions related to this appointment
$prescriptions = [];
$prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                      d.specialization, d.profile_image as doctor_profile_image
                      FROM prescriptions p
                      JOIN doctors d ON p.doctor_id = d.doctor_id
                      WHERE p.appointment_id = ?";
// Check if the appointment_id column exists in the prescriptions table
try {
    $prescriptions_stmt = $conn->prepare($prescriptions_query);
    $prescriptions_stmt->bind_param("i", $appointment_id);
    $prescriptions_stmt->execute();
    $prescriptions_result = $prescriptions_stmt->get_result();
    if ($prescriptions_result->num_rows > 0) {
        while ($row = $prescriptions_result->fetch_assoc()) {
            $prescriptions[] = $row;
        }
    }
} catch (mysqli_sql_exception $e) {
    // If the column doesn't exist, use an alternative query
    $prescriptions_query = "SELECT p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                          d.specialization, d.profile_image as doctor_profile_image
                          FROM prescriptions p
                          JOIN doctors d ON p.doctor_id = d.doctor_id
                          WHERE p.patient_id = ?";
    $prescriptions_stmt = $conn->prepare($prescriptions_query);
    $prescriptions_stmt->bind_param("i", $patient_id);
    $prescriptions_stmt->execute();
    $prescriptions_result = $prescriptions_stmt->get_result();
    if ($prescriptions_result->num_rows > 0) {
        while ($row = $prescriptions_result->fetch_assoc()) {
            $prescriptions[] = $row;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Appointment Details | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        .appointment-actions {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        @media print {
            .sidebar, .header, .back-link, .appointment-actions {
                display: none;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-alt"></i> Appointment Details</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if (isset($_GET['success'])): ?>
                    <div class="alert alert-success">
                        Appointment booked successfully!
                    </div>
                <?php endif; ?>

                <div class="back-link">
                    <a href="appointments.php"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                </div>

                <div class="appointment-header">
                    <div class="appointment-title">
                        <h3>Appointment Details</h3>
                        <span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span>
                    </div>
                    <div class="appointment-actions">
                        <?php if ($appointment['status'] == 'scheduled' && strtotime($appointment['appointment_date']) > time()): ?>
                            <a href="cancel_appointment.php?id=<?php echo $appointment_id; ?>" class="btn btn-danger"><i class="fas fa-times"></i> Cancel Appointment</a>
                        <?php endif; ?>

                        <?php if ($appointment['status'] == 'confirmed' || $appointment['status'] == 'scheduled'): ?>
                            <a href="print_appointment.php?id=<?php echo $appointment_id; ?>" class="btn btn-primary" target="_blank"><i class="fas fa-print"></i> Print Appointment</a>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Appointment Details</h3>
                            </div>
                            <div class="card-body">
                                <div class="appointment-details">
                                    <div class="detail-row">
                                        <div class="detail-label">Date:</div>
                                        <div class="detail-value"><?php echo date('F d, Y', strtotime($appointment['appointment_date'])); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Time:</div>
                                        <div class="detail-value"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Department:</div>
                                        <div class="detail-value"><?php echo $appointment['department_name']; ?></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Status:</div>
                                        <div class="detail-value"><span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span></div>
                                    </div>
                                    <div class="detail-row">
                                        <div class="detail-label">Reason for Visit:</div>
                                        <div class="detail-value"><?php echo $appointment['reason']; ?></div>
                                    </div>
                                    <?php if ($appointment['status'] == 'cancelled' && !empty($appointment['cancellation_reason'])): ?>
                                        <div class="detail-row">
                                            <div class="detail-label">Cancellation Reason:</div>
                                            <div class="detail-value"><?php echo $appointment['cancellation_reason']; ?></div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="detail-row">
                                        <div class="detail-label">Booked On:</div>
                                        <div class="detail-value"><?php echo date('F d, Y h:i A', strtotime($appointment['created_at'])); ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Family Medical History Section -->
                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Family Medical History</h3>
                            </div>
                            <div class="card-body">
                                <div class="medical-info">
                                    <?php
                                    // Get patient's medical history
                                    $medical_query = "SELECT medical_history FROM patients WHERE patient_id = ?";
                                    $medical_stmt = $conn->prepare($medical_query);
                                    $medical_stmt->bind_param("i", $patient_id);
                                    $medical_stmt->execute();
                                    $medical_result = $medical_stmt->get_result();
                                    $medical_info = $medical_result->fetch_assoc();
                                    ?>

                                    <div class="detail-row">
                                        <div class="detail-label">Family Medical History:</div>
                                        <div class="detail-value"><?php echo !empty($medical_info['medical_history']) ? $medical_info['medical_history'] : 'None reported'; ?></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <?php if (count($medical_records) > 0): ?>
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h3>Medical Records</h3>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($medical_records as $record): ?>
                                                    <tr>
                                                        <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                        <td>
                                                            <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>

                        <?php if (count($prescriptions) > 0): ?>
                            <div class="card mt-4">
                                <div class="card-header">
                                    <h3>Prescriptions</h3>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table">
                                            <thead>
                                                <tr>
                                                    <th>Date</th>
                                                    <th>Doctor</th>
                                                    <th>Status</th>
                                                    <th>Email</th>
                                                    <th>Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($prescriptions as $prescription): ?>
                                                    <tr>
                                                        <td><?php echo date('M d, Y', strtotime($prescription['prescription_date'])); ?></td>
                                                        <td>Dr. <?php echo $prescription['doctor_first_name'] . ' ' . $prescription['doctor_last_name']; ?></td>
                                                        <td><span class="status-badge status-<?php echo $prescription['status']; ?>"><?php echo ucfirst($prescription['status']); ?></span></td>
                                                        <td>
                                                            <?php if (!empty($prescription['notification_sent']) && $prescription['notification_sent'] == 1): ?>
                                                                <span class="text-success"><i class="fas fa-check-circle"></i> Sent</span>
                                                                <small class="d-block"><?php echo date('M d, Y', strtotime($prescription['notification_sent_date'])); ?></small>
                                                            <?php else: ?>
                                                                <span class="text-muted"><i class="fas fa-envelope"></i> Not sent</span>
                                                            <?php endif; ?>
                                                        </td>
                                                        <td>
                                                            <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                                <i class="fas fa-eye"></i>
                                                            </a>
                                                            <a href="download_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Download">
                                                                <i class="fas fa-download"></i>
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h3>Doctor Information</h3>
                            </div>
                            <div class="card-body">
                                <div class="doctor-profile">
                                    <div class="doctor-image">
                                        <img src="../assets/images/<?php echo $appointment['doctor_profile_image']; ?>" alt="Doctor">
                                    </div>
                                    <div class="doctor-details">
                                        <h4>Dr. <?php echo $appointment['doctor_first_name'] . ' ' . $appointment['doctor_last_name']; ?></h4>
                                        <p><i class="fas fa-stethoscope"></i> <?php echo $appointment['specialization']; ?></p>
                                        <p><i class="fas fa-hospital"></i> <?php echo $appointment['department_name']; ?></p>
                                        <p><i class="fas fa-phone"></i> <?php echo $appointment['doctor_phone']; ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
