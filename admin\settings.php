<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get admin information
$admin_id = 0;
$admin_info = [];

$admin_query = "SELECT a.*, u.email, u.username, u.last_login
                FROM admins a
                JOIN users u ON a.user_id = u.user_id
                WHERE a.user_id = ?";
$admin_stmt = $conn->prepare($admin_query);
$admin_stmt->bind_param("i", $_SESSION['user_id']);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows > 0) {
    $admin_info = $admin_result->fetch_assoc();
    $admin_id = $admin_info['admin_id'];
}

$error = "";
$success = "";

// Process profile update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_profile'])) {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $address = trim($_POST['address']);

    // Validate input
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error = "Required fields cannot be empty";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email already exists (excluding current user)
        $check_query = "SELECT * FROM users WHERE email = ? AND user_id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $_SESSION['user_id']);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Update user email
                $user_query = "UPDATE users SET email = ? WHERE user_id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("si", $email, $_SESSION['user_id']);
                $user_stmt->execute();

                // Update admin information
                $admin_query = "UPDATE admins SET first_name = ?, last_name = ?, phone = ?, address = ? WHERE admin_id = ?";
                $admin_stmt = $conn->prepare($admin_query);
                $admin_stmt->bind_param("ssssi", $first_name, $last_name, $phone, $address, $admin_id);
                $admin_stmt->execute();

                // Handle profile image upload
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    $max_size = 2 * 1024 * 1024; // 2MB

                    if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
                        throw new Exception("Invalid file type. Only JPG, PNG, and GIF are allowed.");
                    }

                    if ($_FILES['profile_image']['size'] > $max_size) {
                        throw new Exception("File size too large. Maximum size is 2MB.");
                    }

                    // Generate unique filename
                    $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'admin_' . $admin_id . '_' . time() . '.' . $file_extension;
                    $upload_path = '../assets/images/' . $new_filename;

                    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                        // Update profile image in database
                        $image_query = "UPDATE admins SET profile_image = ? WHERE admin_id = ?";
                        $image_stmt = $conn->prepare($image_query);
                        $image_stmt->bind_param("si", $new_filename, $admin_id);
                        $image_stmt->execute();
                    } else {
                        throw new Exception("Failed to upload image.");
                    }
                }

                // Commit transaction
                $conn->commit();

                $success = "Profile updated successfully";

                // Refresh admin data
                $admin_stmt = $conn->prepare($admin_query);
                $admin_stmt->bind_param("i", $_SESSION['user_id']);
                $admin_stmt->execute();
                $admin_result = $admin_stmt->get_result();
                $admin_info = $admin_result->fetch_assoc();
            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Error updating profile: " . $e->getMessage();
            }
        }
    }
}

// Process password change
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = "All password fields are required";
    } elseif ($new_password !== $confirm_password) {
        $error = "New passwords do not match";
    } elseif (strlen($new_password) < 6) {
        $error = "New password must be at least 6 characters long";
    } else {
        // Verify current password
        $password_query = "SELECT password FROM users WHERE user_id = ?";
        $password_stmt = $conn->prepare($password_query);
        $password_stmt->bind_param("i", $_SESSION['user_id']);
        $password_stmt->execute();
        $password_result = $password_stmt->get_result();

        if ($password_result->num_rows > 0) {
            $user_data = $password_result->fetch_assoc();

            if (password_verify($current_password, $user_data['password'])) {
                // Hash new password
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);

                // Update password
                $update_query = "UPDATE users SET password = ? WHERE user_id = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("si", $hashed_password, $_SESSION['user_id']);

                if ($update_stmt->execute()) {
                    $success = "Password changed successfully";
                } else {
                    $error = "Error changing password: " . $conn->error;
                }
            } else {
                $error = "Current password is incorrect";
            }
        } else {
            $error = "User not found";
        }
    }
}

// Process system settings
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_settings'])) {
    $hospital_name = trim($_POST['hospital_name']);
    $hospital_address = trim($_POST['hospital_address']);
    $hospital_phone = trim($_POST['hospital_phone']);
    $hospital_email = trim($_POST['hospital_email']);
    $appointment_interval = $_POST['appointment_interval'];

    // Validate input
    if (empty($hospital_name)) {
        $error = "Hospital name is required";
    } else {
        // Check if settings table exists
        $table_query = "SHOW TABLES LIKE 'settings'";
        $table_result = $conn->query($table_query);

        if ($table_result->num_rows == 0) {
            // Create settings table
            $create_table_query = "CREATE TABLE settings (
                setting_id INT AUTO_INCREMENT PRIMARY KEY,
                setting_key VARCHAR(50) NOT NULL UNIQUE,
                setting_value TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->query($create_table_query);
        }

        // Update settings
        $settings = [
            'hospital_name' => $hospital_name,
            'hospital_address' => $hospital_address,
            'hospital_phone' => $hospital_phone,
            'hospital_email' => $hospital_email,
            'appointment_interval' => $appointment_interval
        ];

        foreach ($settings as $key => $value) {
            // Check if setting exists
            $check_query = "SELECT * FROM settings WHERE setting_key = ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("s", $key);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                // Update setting
                $update_query = "UPDATE settings SET setting_value = ? WHERE setting_key = ?";
                $update_stmt = $conn->prepare($update_query);
                $update_stmt->bind_param("ss", $value, $key);
                $update_stmt->execute();
            } else {
                // Insert setting
                $insert_query = "INSERT INTO settings (setting_key, setting_value) VALUES (?, ?)";
                $insert_stmt = $conn->prepare($insert_query);
                $insert_stmt->bind_param("ss", $key, $value);
                $insert_stmt->execute();
            }
        }

        $success = "Settings updated successfully";
    }
}

// Get current settings
$settings = [];
$settings_query = "SHOW TABLES LIKE 'settings'";
$settings_result = $conn->query($settings_query);

if ($settings_result->num_rows > 0) {
    $get_settings_query = "SELECT * FROM settings";
    $get_settings_result = $conn->query($get_settings_query);

    if ($get_settings_result->num_rows > 0) {
        while ($row = $get_settings_result->fetch_assoc()) {
            $settings[$row['setting_key']] = $row['setting_value'];
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li class="active">
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-cog"></i> Settings</h2>
                </div>
            </header>

            <div class="content-wrapper">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <div class="settings-tabs">
                    <div class="tabs">
                        <button class="tab-btn active" data-tab="profile">
                            <i class="fas fa-user"></i> Profile
                        </button>
                        <button class="tab-btn" data-tab="password">
                            <i class="fas fa-lock"></i> Password
                        </button>
                        <button class="tab-btn" data-tab="system">
                            <i class="fas fa-cogs"></i> System Settings
                        </button>
                    </div>

                    <div class="tab-content">
                        <!-- Profile Tab -->
                        <div class="tab-pane active" id="profile">
                            <div class="card">
                                <div class="card-header">
                                    <h3>Profile Information</h3>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>" enctype="multipart/form-data">
                                        <div class="profile-image-upload">
                                            <div class="current-image">
                                                <img src="../assets/images/<?php echo $admin_info['profile_image']; ?>" alt="Profile Image">
                                            </div>
                                            <div class="upload-controls">
                                                <label for="profile_image" class="btn btn-outline">
                                                    <i class="fas fa-upload"></i> Upload New Image
                                                </label>
                                                <input type="file" id="profile_image" name="profile_image" accept="image/*" style="display: none;">
                                                <p class="help-text">Allowed formats: JPG, PNG, GIF. Max size: 2MB</p>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="username">Username</label>
                                                <input type="text" id="username" value="<?php echo htmlspecialchars($admin_info['username']); ?>" disabled>
                                                <small class="form-text">Username cannot be changed</small>
                                            </div>

                                            <div class="form-group">
                                                <label for="email">Email <span class="required">*</span></label>
                                                <input type="email" id="email" name="email" value="<?php echo htmlspecialchars($admin_info['email']); ?>" required>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="first_name">First Name <span class="required">*</span></label>
                                                <input type="text" id="first_name" name="first_name" value="<?php echo htmlspecialchars($admin_info['first_name']); ?>" required>
                                            </div>

                                            <div class="form-group">
                                                <label for="last_name">Last Name <span class="required">*</span></label>
                                                <input type="text" id="last_name" name="last_name" value="<?php echo htmlspecialchars($admin_info['last_name']); ?>" required>
                                            </div>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="phone">Phone Number</label>
                                                <input type="text" id="phone" name="phone" value="<?php echo htmlspecialchars($admin_info['phone']); ?>">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="address">Address</label>
                                            <textarea id="address" name="address" rows="3"><?php echo htmlspecialchars($admin_info['address'] ?? ''); ?></textarea>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" name="update_profile" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Save Changes
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- Password Tab -->
                        <div class="tab-pane" id="password">
                            <div class="card">
                                <div class="card-header">
                                    <h3>Change Password</h3>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                        <div class="form-group">
                                            <label for="current_password">Current Password <span class="required">*</span></label>
                                            <input type="password" id="current_password" name="current_password" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="new_password">New Password <span class="required">*</span></label>
                                            <input type="password" id="new_password" name="new_password" required>
                                            <small class="form-text">Password must be at least 6 characters long</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="confirm_password">Confirm New Password <span class="required">*</span></label>
                                            <input type="password" id="confirm_password" name="confirm_password" required>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" name="change_password" class="btn btn-primary">
                                                <i class="fas fa-key"></i> Change Password
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- System Settings Tab -->
                        <div class="tab-pane" id="system">
                            <div class="card">
                                <div class="card-header">
                                    <h3>System Settings</h3>
                                </div>
                                <div class="card-body">
                                    <form method="POST" action="<?php echo htmlspecialchars($_SERVER["PHP_SELF"]); ?>">
                                        <div class="form-group">
                                            <label for="hospital_name">Hospital Name <span class="required">*</span></label>
                                            <input type="text" id="hospital_name" name="hospital_name" value="<?php echo htmlspecialchars($settings['hospital_name'] ?? 'Hospital Management System'); ?>" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="hospital_address">Hospital Address</label>
                                            <textarea id="hospital_address" name="hospital_address" rows="2"><?php echo htmlspecialchars($settings['hospital_address'] ?? ''); ?></textarea>
                                        </div>

                                        <div class="form-row">
                                            <div class="form-group">
                                                <label for="hospital_phone">Hospital Phone</label>
                                                <input type="text" id="hospital_phone" name="hospital_phone" value="<?php echo htmlspecialchars($settings['hospital_phone'] ?? ''); ?>">
                                            </div>

                                            <div class="form-group">
                                                <label for="hospital_email">Hospital Email</label>
                                                <input type="email" id="hospital_email" name="hospital_email" value="<?php echo htmlspecialchars($settings['hospital_email'] ?? ''); ?>">
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="appointment_interval">Appointment Interval (minutes)</label>
                                            <select id="appointment_interval" name="appointment_interval">
                                                <option value="15" <?php echo (isset($settings['appointment_interval']) && $settings['appointment_interval'] == '15') ? 'selected' : ''; ?>>15 minutes</option>
                                                <option value="30" <?php echo (isset($settings['appointment_interval']) && $settings['appointment_interval'] == '30') ? 'selected' : ''; ?>>30 minutes</option>
                                                <option value="45" <?php echo (isset($settings['appointment_interval']) && $settings['appointment_interval'] == '45') ? 'selected' : ''; ?>>45 minutes</option>
                                                <option value="60" <?php echo (isset($settings['appointment_interval']) && $settings['appointment_interval'] == '60') ? 'selected' : ''; ?>>60 minutes</option>
                                            </select>
                                        </div>

                                        <div class="form-actions">
                                            <button type="submit" name="update_settings" class="btn btn-primary">
                                                <i class="fas fa-save"></i> Save Settings
                                            </button>
                                        </div>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons and panes
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));

                    // Add active class to clicked button and corresponding pane
                    button.classList.add('active');
                    const tabId = button.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');
                });
            });

            // Profile image preview
            const profileImageInput = document.getElementById('profile_image');
            const profileImage = document.querySelector('.current-image img');

            if (profileImageInput && profileImage) {
                profileImageInput.addEventListener('change', function() {
                    if (this.files && this.files[0]) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            profileImage.src = e.target.result;
                        }

                        reader.readAsDataURL(this.files[0]);
                    }
                });
            }
        });
    </script>
</body>
</html>