<?php
session_start();
include "../db_connect.php";
// Check if simple_mailer.php exists, if not, create a basic version
if (!file_exists("../includes/simple_mailer.php")) {
    include "../includes/mailer.php"; // Try to include the regular mailer instead
} else {
    include "../includes/simple_mailer.php";
}

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

// Get admin information
$admin_id = 0;
$admin_info = [];

$admin_query = "SELECT a.*, u.email, u.last_login
               FROM admins a
               JOIN users u ON a.user_id = u.user_id
               WHERE a.user_id = ?";
$admin_stmt = $conn->prepare($admin_query);
$admin_stmt->bind_param("i", $_SESSION['user_id']);
$admin_stmt->execute();
$admin_result = $admin_stmt->get_result();

if ($admin_result->num_rows > 0) {
    $admin_info = $admin_result->fetch_assoc();
    $admin_id = $admin_info['admin_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = intval($_GET['id']);

// Get appointment details
$appointment = [];
$appointment_query = "SELECT a.*,
                     p.first_name as patient_first_name, p.last_name as patient_last_name,
                     p.patient_id,
                     d.doctor_id, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                     dep.department_id, dep.department_name
                     FROM appointments a
                     JOIN patients p ON a.patient_id = p.patient_id
                     JOIN doctors d ON a.doctor_id = d.doctor_id
                     JOIN departments dep ON a.department_id = dep.department_id
                     WHERE a.appointment_id = ?";
$appointment_stmt = $conn->prepare($appointment_query);
$appointment_stmt->bind_param("i", $appointment_id);
$appointment_stmt->execute();
$appointment_result = $appointment_stmt->get_result();

if ($appointment_result->num_rows === 0) {
    header("Location: appointments.php");
    exit();
}

$appointment = $appointment_result->fetch_assoc();

// Get all departments
$departments = [];
// Check if departments table exists
$table_check = $conn->query("SHOW TABLES LIKE 'departments'");
if ($table_check->num_rows > 0) {
    // Check if departments table has a status column
    $column_check = $conn->query("SHOW COLUMNS FROM departments LIKE 'status'");
    if ($column_check->num_rows > 0) {
        $dept_query = "SELECT * FROM departments WHERE status = 'active' ORDER BY department_name";
    } else {
        $dept_query = "SELECT * FROM departments ORDER BY department_name";
    }
    $dept_result = $conn->query($dept_query);
    if ($dept_result->num_rows > 0) {
        while ($row = $dept_result->fetch_assoc()) {
            $departments[] = $row;
        }
    }
}

// Get all doctors
$doctors = [];
// Check if doctors table exists
$table_check = $conn->query("SHOW TABLES LIKE 'doctors'");
if ($table_check->num_rows > 0) {
    // Check if doctors table has a status column
    $column_check = $conn->query("SHOW COLUMNS FROM doctors LIKE 'status'");
    if ($column_check->num_rows > 0) {
        $doctor_query = "SELECT d.*, dep.department_name
                        FROM doctors d
                        JOIN departments dep ON d.department_id = dep.department_id
                        WHERE d.status = 'active'
                        ORDER BY d.first_name, d.last_name";
    } else {
        $doctor_query = "SELECT d.*, dep.department_name
                        FROM doctors d
                        JOIN departments dep ON d.department_id = dep.department_id
                        ORDER BY d.first_name, d.last_name";
    }
    $doctor_result = $conn->query($doctor_query);
    if ($doctor_result->num_rows > 0) {
        while ($row = $doctor_result->fetch_assoc()) {
            $doctors[] = $row;
        }
    }
}

// Get all patients
$patients = [];
// Check if patients table exists
$table_check = $conn->query("SHOW TABLES LIKE 'patients'");
if ($table_check->num_rows > 0) {
    // Check if users table exists and has email column
    $users_check = $conn->query("SHOW TABLES LIKE 'users'");
    $email_check = $conn->query("SHOW COLUMNS FROM users LIKE 'email'");

    if ($users_check->num_rows > 0 && $email_check->num_rows > 0) {
        $patient_query = "SELECT p.*, u.email
                         FROM patients p
                         JOIN users u ON p.user_id = u.user_id
                         ORDER BY p.first_name, p.last_name";
    } else {
        $patient_query = "SELECT p.*
                         FROM patients p
                         ORDER BY p.first_name, p.last_name";
    }

    $patient_result = $conn->query($patient_query);
    if ($patient_result->num_rows > 0) {
        while ($row = $patient_result->fetch_assoc()) {
            $patients[] = $row;
        }
    }
}

// Process form submission
$error = "";
$success = "";

if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_appointment'])) {
    // Get form data
    $patient_id = $_POST['patient_id'];
    $doctor_id = $_POST['doctor_id'];
    $department_id = $_POST['department_id'];
    $appointment_date = $_POST['appointment_date'];
    $appointment_time = $_POST['appointment_time'];
    $reason = $_POST['reason'];
    $status = $_POST['status'];

    // Validate input
    if (empty($patient_id) || empty($doctor_id) || empty($department_id) ||
        empty($appointment_date) || empty($appointment_time) || empty($status)) {
        $error = "All fields are required";
    } else {
        // Check if the slot is still available (if date/time/doctor changed)
        if ($appointment_date != $appointment['appointment_date'] ||
            $appointment_time != $appointment['appointment_time'] ||
            $doctor_id != $appointment['doctor_id']) {

            $check_query = "SELECT * FROM appointments
                           WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ?
                           AND status != 'cancelled' AND appointment_id != ?";
            $check_stmt = $conn->prepare($check_query);
            $check_stmt->bind_param("issi", $doctor_id, $appointment_date, $appointment_time, $appointment_id);
            $check_stmt->execute();
            $check_result = $check_stmt->get_result();

            if ($check_result->num_rows > 0) {
                $error = "This time slot is already booked. Please select another time.";
            }
        }

        if (empty($error)) {
            // Update appointment
            $update_query = "UPDATE appointments SET
                           patient_id = ?,
                           doctor_id = ?,
                           department_id = ?,
                           appointment_date = ?,
                           appointment_time = ?,
                           reason = ?,
                           status = ?,
                           updated_at = NOW()
                           WHERE appointment_id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("iiissssi", $patient_id, $doctor_id, $department_id,
                                   $appointment_date, $appointment_time, $reason, $status, $appointment_id);

            if ($update_stmt->execute()) {
                // Send notification if status changed to confirmed or cancelled
                $status_changed = $status != $appointment['status'];
                if ($status_changed && ($status == 'confirmed' || $status == 'cancelled')) {
                    try {
                        // Get patient email
                        $patient_query = "SELECT p.*, u.email FROM patients p JOIN users u ON p.user_id = u.user_id WHERE p.patient_id = ?";
                        $patient_stmt = $conn->prepare($patient_query);
                        $patient_stmt->bind_param("i", $patient_id);
                        $patient_stmt->execute();
                        $patient_result = $patient_stmt->get_result();
                        $patient_info = $patient_result->fetch_assoc();

                        // Get doctor info
                        $doctor_query = "SELECT d.* FROM doctors d WHERE d.doctor_id = ?";
                        $doctor_stmt = $conn->prepare($doctor_query);
                        $doctor_stmt->bind_param("i", $doctor_id);
                        $doctor_stmt->execute();
                        $doctor_result = $doctor_stmt->get_result();
                        $doctor_info = $doctor_result->fetch_assoc();

                        // Get updated appointment info
                        $updated_appointment_query = "SELECT * FROM appointments WHERE appointment_id = ?";
                        $updated_appointment_stmt = $conn->prepare($updated_appointment_query);
                        $updated_appointment_stmt->bind_param("i", $appointment_id);
                        $updated_appointment_stmt->execute();
                        $updated_appointment_result = $updated_appointment_stmt->get_result();
                        $updated_appointment = $updated_appointment_result->fetch_assoc();

                        // Send email notification
                        $mailer = new SimpleMailer();
                        if ($status == 'confirmed') {
                            $mailer->sendAppointmentConfirmation($updated_appointment, $patient_info, $doctor_info);
                        } else if ($status == 'cancelled') {
                            // If you have a cancellation method, use it
                            if (method_exists($mailer, 'sendAppointmentCancellation')) {
                                $mailer->sendAppointmentCancellation($updated_appointment, $patient_info, $doctor_info);
                            } else {
                                // Otherwise, just send a generic confirmation with cancelled status
                                $updated_appointment['status'] = 'cancelled'; // Set status for email
                                $mailer->sendAppointmentConfirmation($updated_appointment, $patient_info, $doctor_info);
                            }
                        }
                    } catch (Exception $e) {
                        // Log error but continue
                        error_log("Failed to send notification email: " . $e->getMessage());
                    }
                }

                $success = "Appointment updated successfully!";
            } else {
                $error = "Error updating appointment: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="departments.php"><i class="fas fa-hospital"></i> Departments</a>
                    </li>
                    <li>
                        <a href="doctors.php"><i class="fas fa-user-md"></i> Doctors</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> Patients</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="reports.php"><i class="fas fa-chart-bar"></i> Reports</a>
                    </li>
                    <li>
                        <a href="settings.php"><i class="fas fa-cog"></i> Settings</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-edit"></i> Edit Appointment</h2>
                </div>
                <div class="header-right">
                    <a href="appointments.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Appointments
                    </a>
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $admin_info['profile_image']; ?>" alt="Admin" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $admin_info['first_name'] . ' ' . $admin_info['last_name']; ?></h4>
                            <p>Administrator</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Edit Appointment #<?php echo $appointment_id; ?></h3>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <form action="" method="post" class="form">
                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="patient_id">Patient</label>
                                    <select name="patient_id" id="patient_id" required>
                                        <option value="">Select Patient</option>
                                        <?php foreach ($patients as $patient): ?>
                                            <option value="<?php echo $patient['patient_id']; ?>" <?php echo ($patient['patient_id'] == $appointment['patient_id']) ? 'selected' : ''; ?>>
                                                <?php echo $patient['first_name'] . ' ' . $patient['last_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="department_id">Department</label>
                                    <select name="department_id" id="department_id" required>
                                        <option value="">Select Department</option>
                                        <?php foreach ($departments as $department): ?>
                                            <option value="<?php echo $department['department_id']; ?>" <?php echo ($department['department_id'] == $appointment['department_id']) ? 'selected' : ''; ?>>
                                                <?php echo $department['department_name']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="doctor_id">Doctor</label>
                                    <select name="doctor_id" id="doctor_id" required>
                                        <option value="">Select Doctor</option>
                                        <?php foreach ($doctors as $doctor): ?>
                                            <option value="<?php echo $doctor['doctor_id']; ?>" <?php echo ($doctor['doctor_id'] == $appointment['doctor_id']) ? 'selected' : ''; ?>>
                                                Dr. <?php echo $doctor['first_name'] . ' ' . $doctor['last_name']; ?> (<?php echo $doctor['department_name']; ?>)
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="status">Status</label>
                                    <select name="status" id="status" required>
                                        <option value="pending" <?php echo $appointment['status'] == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="confirmed" <?php echo $appointment['status'] == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                        <option value="completed" <?php echo $appointment['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo $appointment['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                        <option value="no_show" <?php echo $appointment['status'] == 'no_show' ? 'selected' : ''; ?>>No Show</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group col-md-6">
                                    <label for="appointment_date">Date</label>
                                    <input type="date" name="appointment_date" id="appointment_date" value="<?php echo $appointment['appointment_date']; ?>" required>
                                </div>

                                <div class="form-group col-md-6">
                                    <label for="appointment_time">Time</label>
                                    <input type="time" name="appointment_time" id="appointment_time" value="<?php echo $appointment['appointment_time']; ?>" required>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="reason">Reason for Visit</label>
                                <textarea name="reason" id="reason" rows="3" required><?php echo $appointment['reason']; ?></textarea>
                            </div>

                            <div class="form-actions">
                                <button type="submit" name="update_appointment" class="btn btn-primary">Update Appointment</button>
                                <a href="appointments.php" class="btn btn-secondary">Cancel</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Filter doctors based on selected department
        document.getElementById('department_id').addEventListener('change', function() {
            const departmentId = this.value;
            const doctorSelect = document.getElementById('doctor_id');
            const selectedDoctorId = '<?php echo $appointment['doctor_id']; ?>';

            // Enable all options first
            for (let i = 0; i < doctorSelect.options.length; i++) {
                const option = doctorSelect.options[i];
                option.style.display = 'block';

                // If this is not the placeholder and department doesn't match, hide it
                if (option.value !== '' && option.getAttribute('data-department-id') !== departmentId) {
                    option.style.display = 'none';
                }
            }

            // If the currently selected doctor is not in the selected department, reset selection
            let validSelection = false;
            for (let i = 0; i < doctorSelect.options.length; i++) {
                const option = doctorSelect.options[i];
                if (option.value === selectedDoctorId && option.style.display !== 'none') {
                    validSelection = true;
                    break;
                }
            }

            if (!validSelection) {
                doctorSelect.value = '';
            }
        });

        // Add data-department-id attribute to doctor options
        window.addEventListener('DOMContentLoaded', function() {
            const doctorSelect = document.getElementById('doctor_id');

            <?php foreach ($doctors as $doctor): ?>
                const option<?php echo $doctor['doctor_id']; ?> = doctorSelect.querySelector('option[value="<?php echo $doctor['doctor_id']; ?>"]');
                if (option<?php echo $doctor['doctor_id']; ?>) {
                    option<?php echo $doctor['doctor_id']; ?>.setAttribute('data-department-id', '<?php echo $doctor['department_id']; ?>');
                }
            <?php endforeach; ?>
        });
    </script>
</body>
</html>
