<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
<title>Link</title>
<link type="text/css" rel="stylesheet" href="../fpdf.css">
</head>
<body>
<h1>Link</h1>
<code>Link(<b>float</b> x, <b>float</b> y, <b>float</b> w, <b>float</b> h, <b>mixed</b> link)</code>
<h2>Description</h2>
Puts a link on a rectangular area of the page. Text or image links are generally put via Cell(),
Write() or Image(), but this method can be useful for instance to define a clickable area inside
an image.
<h2>Parameters</h2>
<dl class="param">
<dt><code>x</code></dt>
<dd>
Abscissa of the upper-left corner of the rectangle.
</dd>
<dt><code>y</code></dt>
<dd>
Ordinate of the upper-left corner of the rectangle.
</dd>
<dt><code>w</code></dt>
<dd>
Width of the rectangle.
</dd>
<dt><code>h</code></dt>
<dd>
Height of the rectangle.
</dd>
<dt><code>link</code></dt>
<dd>
URL or identifier returned by AddLink().
</dd>
</dl>
<h2>See also</h2>
<a href="addlink.htm">AddLink</a>,
<a href="cell.htm">Cell</a>,
<a href="write.htm">Write</a>,
<a href="image.htm">Image</a>
<hr style="margin-top:1.5em">
<div style="text-align:center"><a href="index.htm">Index</a></div>
</body>
</html>
