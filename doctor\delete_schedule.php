<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if schedule ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: schedule.php");
    exit();
}

$schedule_id = $_GET['id'];

// Get schedule details
$schedule = null;
$query = "SELECT * FROM doctor_schedules WHERE schedule_id = ? AND doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $schedule_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $schedule = $result->fetch_assoc();
} else {
    // Schedule not found or doesn't belong to this doctor
    header("Location: schedule.php");
    exit();
}

// Get days of week for display
$days_of_week = [
    1 => 'Monday',
    2 => 'Tuesday',
    3 => 'Wednesday',
    4 => 'Thursday',
    5 => 'Friday',
    6 => 'Saturday',
    7 => 'Sunday'
];

// Process form submission
$success_message = '';
$error_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['confirm_delete']) && $_POST['confirm_delete'] === 'yes') {
        // Check if there are appointments scheduled during this time
        $has_appointments = false;

        // Get day of week as string (e.g., 'Monday')
        $day_name = $days_of_week[$schedule['day_of_week']];

        // Check for appointments on this day and time
        $appointments_query = "SELECT COUNT(*) as count FROM appointments
                              WHERE doctor_id = ?
                              AND DAYNAME(appointment_date) = ?
                              AND TIME(appointment_time) >= ?
                              AND TIME(appointment_time) <= ?
                              AND appointment_date >= CURDATE()
                              AND status NOT IN ('cancelled', 'completed')";
        $appointments_stmt = $conn->prepare($appointments_query);
        $appointments_stmt->bind_param("isss", $doctor_id, $day_name, $schedule['start_time'], $schedule['end_time']);
        $appointments_stmt->execute();
        $appointments_result = $appointments_stmt->get_result();
        $appointments_count = $appointments_result->fetch_assoc()['count'];

        if ($appointments_count > 0 && !isset($_POST['force_delete'])) {
            $has_appointments = true;
            $error_message = "There are upcoming appointments scheduled during this time slot. Please reschedule or cancel these appointments first, or confirm deletion.";
        } else {
            // Delete the schedule
            $delete_query = "DELETE FROM doctor_schedules WHERE schedule_id = ? AND doctor_id = ?";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->bind_param("ii", $schedule_id, $doctor_id);

            if ($delete_stmt->execute()) {
                $success_message = "Schedule slot deleted successfully!";
                // Redirect after a short delay
                header("Refresh: 2; URL=schedule.php");
            } else {
                $error_message = "Error deleting schedule slot: " . $conn->error;
            }
        }
    } else {
        // User clicked "No" or didn't confirm
        header("Location: schedule.php");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delete Schedule | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li class="active">
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-trash"></i> Delete Schedule</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Confirm Deletion</h3>
                    <div class="actions">
                        <a href="schedule.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Schedule</a>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle"></i> <?php echo $error_message; ?>
                    </div>

                    <?php if (strpos($error_message, "upcoming appointments") !== false): ?>
                        <div class="card">
                            <div class="card-body">
                                <div class="alert alert-warning">
                                    <p>Deleting this schedule slot may affect upcoming appointments. Are you sure you want to proceed?</p>
                                </div>

                                <form action="delete_schedule.php?id=<?php echo $schedule_id; ?>" method="post">
                                    <input type="hidden" name="confirm_delete" value="yes">
                                    <input type="hidden" name="force_delete" value="yes">

                                    <div class="form-actions">
                                        <button type="submit" class="btn btn-danger"><i class="fas fa-trash"></i> Yes, Delete Anyway</button>
                                        <a href="schedule.php" class="btn btn-outline">Cancel</a>
                                    </div>
                                </form>
                            </div>
                        </div>
                    <?php endif; ?>
                <?php elseif ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i> <?php echo $success_message; ?>
                        <p>Redirecting to schedule list...</p>
                    </div>
                <?php else: ?>
                    <div class="card">
                        <div class="card-body">
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle"></i> Are you sure you want to delete this schedule slot?
                            </div>

                            <div class="schedule-summary">
                                <p><strong>Day:</strong> <?php echo $days_of_week[$schedule['day_of_week']]; ?></p>
                                <p><strong>Time:</strong> <?php echo date('h:i A', strtotime($schedule['start_time'])); ?> - <?php echo date('h:i A', strtotime($schedule['end_time'])); ?></p>
                                <p><strong>Status:</strong> <?php echo $schedule['is_active'] ? 'Active' : 'Inactive'; ?></p>
                            </div>

                            <form action="delete_schedule.php?id=<?php echo $schedule_id; ?>" method="post">
                                <input type="hidden" name="confirm_delete" value="yes">

                                <div class="form-actions">
                                    <button type="submit" class="btn btn-danger"><i class="fas fa-trash"></i> Yes, Delete Schedule</button>
                                    <a href="schedule.php" class="btn btn-outline">No, Cancel</a>
                                </div>
                            </form>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </main>
    </div>
</body>
</html>
