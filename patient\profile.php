<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

$patient_query = "SELECT p.*, u.email, u.username, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

$error = "";
$success = "";

// Process profile update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_profile'])) {
    $first_name = trim($_POST['first_name']);
    $last_name = trim($_POST['last_name']);
    $email = trim($_POST['email']);
    $phone = trim($_POST['phone']);
    $gender = $_POST['gender'];
    $date_of_birth = $_POST['date_of_birth'];
    $blood_group = $_POST['blood_group'];
    $address = trim($_POST['address']);
    $allergies = trim($_POST['allergies']);
    $medical_history = trim($_POST['medical_history']);

    // Validate input
    if (empty($first_name) || empty($last_name) || empty($email)) {
        $error = "First name, last name, and email are required fields";
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = "Invalid email format";
    } else {
        // Check if email already exists (excluding current user)
        $check_query = "SELECT * FROM users WHERE email = ? AND user_id != ?";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("si", $email, $_SESSION['user_id']);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "Email already exists";
        } else {
            // Start transaction
            $conn->begin_transaction();

            try {
                // Update user email
                $user_query = "UPDATE users SET email = ? WHERE user_id = ?";
                $user_stmt = $conn->prepare($user_query);
                $user_stmt->bind_param("si", $email, $_SESSION['user_id']);
                $user_stmt->execute();

                // Update patient information
                $patient_query = "UPDATE patients SET
                                first_name = ?,
                                last_name = ?,
                                gender = ?,
                                date_of_birth = ?,
                                blood_group = ?,
                                phone = ?,
                                address = ?,
                                allergies = ?,
                                medical_history = ?
                                WHERE patient_id = ?";
                $patient_stmt = $conn->prepare($patient_query);
                $patient_stmt->bind_param("sssssssssi",
                                        $first_name,
                                        $last_name,
                                        $gender,
                                        $date_of_birth,
                                        $blood_group,
                                        $phone,
                                        $address,
                                        $allergies,
                                        $medical_history,
                                        $patient_id);
                $patient_stmt->execute();

                // Handle profile image upload
                if (isset($_FILES['profile_image']) && $_FILES['profile_image']['error'] == 0) {
                    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
                    $max_size = 2 * 1024 * 1024; // 2MB

                    if (!in_array($_FILES['profile_image']['type'], $allowed_types)) {
                        throw new Exception("Only JPG, PNG, and GIF images are allowed.");
                    }

                    if ($_FILES['profile_image']['size'] > $max_size) {
                        throw new Exception("Image size should be less than 2MB.");
                    }

                    // Generate unique filename
                    $file_extension = pathinfo($_FILES['profile_image']['name'], PATHINFO_EXTENSION);
                    $new_filename = 'patient_' . $patient_id . '_' . time() . '.' . $file_extension;
                    $upload_path = '../assets/images/' . $new_filename;

                    if (move_uploaded_file($_FILES['profile_image']['tmp_name'], $upload_path)) {
                        // Update profile image in database
                        $image_query = "UPDATE patients SET profile_image = ? WHERE patient_id = ?";
                        $image_stmt = $conn->prepare($image_query);
                        $image_stmt->bind_param("si", $new_filename, $patient_id);
                        $image_stmt->execute();
                    } else {
                        throw new Exception("Failed to upload image.");
                    }
                }

                // Commit transaction
                $conn->commit();

                $success = "Profile updated successfully";

                // Update session email
                $_SESSION['email'] = $email;

                // Refresh patient data
                // Reset patient_query to the SELECT query
                $patient_query = "SELECT p.*, u.email, u.username, u.last_login
                                FROM patients p
                                JOIN users u ON p.user_id = u.user_id
                                WHERE p.user_id = ?";
                $patient_stmt = $conn->prepare($patient_query);
                $patient_stmt->bind_param("i", $_SESSION['user_id']);
                $patient_stmt->execute();
                $patient_result = $patient_stmt->get_result();
                $patient_info = $patient_result->fetch_assoc();

            } catch (Exception $e) {
                // Rollback transaction on error
                $conn->rollback();
                $error = "Error updating profile: " . $e->getMessage();
            }
        }
    }
}

// Process password update
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['update_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];

    // Validate input
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = "All password fields are required";
    } elseif ($new_password !== $confirm_password) {
        $error = "New passwords do not match";
    } elseif (strlen($new_password) < 6) {
        $error = "New password must be at least 6 characters long";
    } else {
        // Verify current password
        $password_query = "SELECT password FROM users WHERE user_id = ?";
        $password_stmt = $conn->prepare($password_query);
        $password_stmt->bind_param("i", $_SESSION['user_id']);
        $password_stmt->execute();
        $password_result = $password_stmt->get_result();
        $user_data = $password_result->fetch_assoc();

        if (!password_verify($current_password, $user_data['password'])) {
            $error = "Current password is incorrect";
        } else {
            // Update password
            $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
            $update_query = "UPDATE users SET password = ? WHERE user_id = ?";
            $update_stmt = $conn->prepare($update_query);
            $update_stmt->bind_param("si", $hashed_password, $_SESSION['user_id']);

            if ($update_stmt->execute()) {
                $success = "Password updated successfully";
            } else {
                $error = "Error updating password: " . $conn->error;
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Profile | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li class="active">
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-user"></i> My Profile</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <?php if (!empty($error)): ?>
                    <div class="alert alert-danger">
                        <?php echo $error; ?>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="alert alert-success">
                        <?php echo $success; ?>
                    </div>
                <?php endif; ?>

                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <div class="profile-image-container">
                                    <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Profile Image" class="profile-image">
                                    <div class="profile-info">
                                        <h3><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h3>
                                        <p><i class="fas fa-envelope"></i> <?php echo $patient_info['email']; ?></p>
                                        <p><i class="fas fa-phone"></i> <?php echo $patient_info['phone']; ?></p>
                                    </div>
                                </div>

                                <div class="profile-stats">
                                    <div class="stat-item">
                                        <div class="stat-value"><?php echo $patient_info['patient_id']; ?></div>
                                        <div class="stat-label">Patient ID</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?php echo $patient_info['blood_group']; ?></div>
                                        <div class="stat-label">Blood Group</div>
                                    </div>
                                    <div class="stat-item">
                                        <div class="stat-value"><?php echo date('Y') - date('Y', strtotime($patient_info['date_of_birth'])); ?></div>
                                        <div class="stat-label">Age</div>
                                    </div>
                                </div>

                                <div class="account-info">
                                    <h4>Account Information</h4>
                                    <p><strong>Username:</strong> <?php echo $patient_info['username']; ?></p>
                                    <p><strong>Last Login:</strong> <?php echo date('M d, Y h:i A', strtotime($patient_info['last_login'])); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <h3>Edit Profile</h3>
                            </div>
                            <div class="card-body">
                                <form action="" method="post" enctype="multipart/form-data">
                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="first_name">First Name</label>
                                            <input type="text" name="first_name" id="first_name" value="<?php echo $patient_info['first_name']; ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="last_name">Last Name</label>
                                            <input type="text" name="last_name" id="last_name" value="<?php echo $patient_info['last_name']; ?>" required>
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="email">Email</label>
                                            <input type="email" name="email" id="email" value="<?php echo $patient_info['email']; ?>" required>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="phone">Phone</label>
                                            <input type="text" name="phone" id="phone" value="<?php echo $patient_info['phone']; ?>">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="gender">Gender</label>
                                            <select name="gender" id="gender">
                                                <option value="male" <?php echo $patient_info['gender'] == 'male' ? 'selected' : ''; ?>>Male</option>
                                                <option value="female" <?php echo $patient_info['gender'] == 'female' ? 'selected' : ''; ?>>Female</option>
                                                <option value="other" <?php echo $patient_info['gender'] == 'other' ? 'selected' : ''; ?>>Other</option>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="date_of_birth">Date of Birth</label>
                                            <input type="date" name="date_of_birth" id="date_of_birth" value="<?php echo $patient_info['date_of_birth']; ?>">
                                        </div>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group col-md-6">
                                            <label for="blood_group">Blood Group</label>
                                            <select name="blood_group" id="blood_group">
                                                <option value="" <?php echo empty($patient_info['blood_group']) ? 'selected' : ''; ?>>Select Blood Group</option>
                                                <option value="A+" <?php echo $patient_info['blood_group'] == 'A+' ? 'selected' : ''; ?>>A+</option>
                                                <option value="A-" <?php echo $patient_info['blood_group'] == 'A-' ? 'selected' : ''; ?>>A-</option>
                                                <option value="B+" <?php echo $patient_info['blood_group'] == 'B+' ? 'selected' : ''; ?>>B+</option>
                                                <option value="B-" <?php echo $patient_info['blood_group'] == 'B-' ? 'selected' : ''; ?>>B-</option>
                                                <option value="AB+" <?php echo $patient_info['blood_group'] == 'AB+' ? 'selected' : ''; ?>>AB+</option>
                                                <option value="AB-" <?php echo $patient_info['blood_group'] == 'AB-' ? 'selected' : ''; ?>>AB-</option>
                                                <option value="O+" <?php echo $patient_info['blood_group'] == 'O+' ? 'selected' : ''; ?>>O+</option>
                                                <option value="O-" <?php echo $patient_info['blood_group'] == 'O-' ? 'selected' : ''; ?>>O-</option>
                                            </select>
                                        </div>
                                        <div class="form-group col-md-6">
                                            <label for="profile_image">Profile Image</label>
                                            <input type="file" name="profile_image" id="profile_image" accept="image/*">
                                            <small class="form-text">Max size: 2MB. Allowed formats: JPG, PNG, GIF</small>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="address">Address</label>
                                        <textarea name="address" id="address" rows="2"><?php echo $patient_info['address']; ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="allergies">Allergies</label>
                                        <textarea name="allergies" id="allergies" rows="2"><?php echo $patient_info['allergies']; ?></textarea>
                                    </div>

                                    <div class="form-group">
                                        <label for="medical_history">Medical History</label>
                                        <textarea name="medical_history" id="medical_history" rows="3"><?php echo $patient_info['medical_history']; ?></textarea>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" name="update_profile" class="btn btn-primary">Update Profile</button>
                                    </div>
                                </form>
                            </div>
                        </div>

                        <div class="card mt-4">
                            <div class="card-header">
                                <h3>Change Password</h3>
                            </div>
                            <div class="card-body">
                                <form action="" method="post">
                                    <div class="form-group">
                                        <label for="current_password">Current Password</label>
                                        <input type="password" name="current_password" id="current_password" required>
                                    </div>

                                    <div class="form-group">
                                        <label for="new_password">New Password</label>
                                        <input type="password" name="new_password" id="new_password" required>
                                        <small class="form-text">Password must be at least 6 characters long</small>
                                    </div>

                                    <div class="form-group">
                                        <label for="confirm_password">Confirm New Password</label>
                                        <input type="password" name="confirm_password" id="confirm_password" required>
                                    </div>

                                    <div class="form-actions">
                                        <button type="submit" name="update_password" class="btn btn-primary">Change Password</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
