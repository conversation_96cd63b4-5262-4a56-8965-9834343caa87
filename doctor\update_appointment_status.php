<?php
session_start();
include "../db_connect.php";
include "../includes/direct_gmail_mailer.php"; // Include the Direct Gmail mailer class

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_query = "SELECT d.doctor_id, d.first_name, d.last_name, d.specialization, dep.department_name
                FROM doctors d
                JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
    $status_changed_by = [
        'first_name' => $doctor_info['first_name'],
        'last_name' => $doctor_info['last_name'],
        'specialization' => $doctor_info['specialization'],
        'department_name' => $doctor_info['department_name']
    ];
} else {
    header("Location: ../index.php");
    exit();
}

// Check if form was submitted
if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Get form data
    $appointment_id = $_POST['appointment_id'];
    $status = $_POST['status'];
    $notes = $_POST['notes'];

    // Verify that the appointment belongs to this doctor
    $check_query = "SELECT * FROM appointments WHERE appointment_id = ? AND doctor_id = ?";
    $check_stmt = $conn->prepare($check_query);
    $check_stmt->bind_param("ii", $appointment_id, $doctor_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();

    if ($check_result->num_rows > 0) {
        // Update appointment status
        $update_query = "UPDATE appointments SET status = ?, notes = ?, updated_at = NOW() WHERE appointment_id = ?";
        $update_stmt = $conn->prepare($update_query);
        $update_stmt->bind_param("ssi", $status, $notes, $appointment_id);

        if ($update_stmt->execute()) {
            // If status is confirmed or cancelled, send email notification
            if ($status == 'confirmed' || $status == 'cancelled') {
                try {
                    // Get appointment details
                    $appointment_query = "SELECT a.*, p.first_name as patient_first_name, p.last_name as patient_last_name,
                                         d.first_name as doctor_first_name, d.last_name as doctor_last_name,
                                         d.specialization, dep.department_name, u.email as patient_email
                                         FROM appointments a
                                         JOIN patients p ON a.patient_id = p.patient_id
                                         JOIN doctors d ON a.doctor_id = d.doctor_id
                                         JOIN departments dep ON a.department_id = dep.department_id
                                         LEFT JOIN users u ON p.user_id = u.user_id
                                         WHERE a.appointment_id = ?";
                    $appointment_stmt = $conn->prepare($appointment_query);
                    $appointment_stmt->bind_param("i", $appointment_id);
                    $appointment_stmt->execute();
                    $appointment_result = $appointment_stmt->get_result();
                    $appointment_info = $appointment_result->fetch_assoc();

                    // Get patient information
                    $patient_info = [
                        'first_name' => $appointment_info['patient_first_name'],
                        'last_name' => $appointment_info['patient_last_name'],
                        'email' => $appointment_info['patient_email']
                    ];

                    // Get doctor information
                    $doctor_info = [
                        'first_name' => $appointment_info['doctor_first_name'],
                        'last_name' => $appointment_info['doctor_last_name'],
                        'specialization' => $appointment_info['specialization'],
                        'department_name' => $appointment_info['department_name']
                    ];

                    // Send email notification
                    $mailer = new Mailer();
                    $email_result = $mailer->sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info, $status_changed_by);

                    // Log detailed information about the email attempt
                    error_log("Email notification attempt for appointment #" . $appointment_id . " with status: " . $status);
                    error_log("Patient email: " . $patient_info['email'] . " (will be <NAME_EMAIL>)");
                    error_log("Doctor: Dr. " . $doctor_info['first_name'] . " " . $doctor_info['last_name']);

                    // Log email result
                    error_log("Email notification sent for appointment #" . $appointment_id . ": " . ($email_result ? "Success" : "Failed"));

                    // Update appointment record to mark confirmation as sent
                    if ($email_result) {
                        $update_email_query = "UPDATE appointments SET confirmation_sent = 1, confirmation_sent_date = NOW() WHERE appointment_id = ?";
                        $update_email_stmt = $conn->prepare($update_email_query);
                        $update_email_stmt->bind_param("i", $appointment_id);
                        $update_email_stmt->execute();
                    }
                } catch (Exception $e) {
                    // Log error but continue
                    error_log("Failed to send notification email: " . $e->getMessage());
                }
            }

            // Success
            header("Location: view_appointment.php?id=" . $appointment_id . "&status_updated=1");
            exit();
        } else {
            // Error
            header("Location: view_appointment.php?id=" . $appointment_id . "&error=1");
            exit();
        }
    } else {
        // Appointment doesn't belong to this doctor
        header("Location: appointments.php");
        exit();
    }
} else {
    // Not a POST request
    header("Location: appointments.php");
    exit();
}
?>
