<?php
/**
 * Prescription Notification Email Template
 * 
 * Variables available:
 * $patient_name - Patient's full name
 * $doctor_name - Doctor's full name with title
 * $prescription_date - Formatted prescription date
 * $prescription_id - Prescription ID
 * $status - Prescription status
 * $notes - Prescription notes
 * $medications - Array of medications
 * $hospital_name - Hospital name
 * $hospital_address - Hospital address
 * $hospital_phone - Hospital phone number
 */

// Set email subject
$subject = "New Prescription #$prescription_id from $hospital_name";

// Plain text version
$text_message = "New Prescription\n\n";
$text_message .= "Dear $patient_name,\n\n";
$text_message .= "A new prescription has been issued for you by $doctor_name on $prescription_date.\n\n";
$text_message .= "Prescription Details:\n";
$text_message .= "Prescription ID: #$prescription_id\n";
$text_message .= "Date: $prescription_date\n";
$text_message .= "Status: $status\n";
$text_message .= "Doctor: $doctor_name\n\n";

if (!empty($notes)) {
    $text_message .= "Notes: $notes\n\n";
}

if (!empty($medications)) {
    $text_message .= "Medications:\n";
    
    foreach ($medications as $medication) {
        $text_message .= "- " . $medication['medication_name'] . ": ";
        $text_message .= $medication['dosage'] . ", ";
        $text_message .= $medication['frequency'] . ", ";
        $text_message .= $medication['duration'] . "\n";
        $text_message .= "  Instructions: " . $medication['instructions'] . "\n";
    }
}

$text_message .= "\nYou can view your prescription details by logging into your patient portal at " . SITE_URL . "/patient/view_prescription.php?id=$prescription_id\n\n";
$text_message .= "Thank you for choosing $hospital_name for your healthcare needs.\n\n";
$text_message .= "Best regards,\n";
$text_message .= "$hospital_name\n";
$text_message .= "$hospital_address\n";
$text_message .= "Phone: $hospital_phone\n";
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>New Prescription</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            background-color: #3498db;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
            background-color: #fff;
        }
        .footer {
            background-color: #f8f9fa;
            padding: 15px;
            text-align: center;
            font-size: 12px;
            color: #666;
        }
        .button {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 15px;
            text-decoration: none;
            border-radius: 5px;
            margin-top: 15px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .status-badge {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            color: white;
        }
        .status-active {
            background-color: #28a745;
        }
        .status-completed {
            background-color: #6c757d;
        }
        .status-cancelled {
            background-color: #dc3545;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo $hospital_name; ?></h1>
        </div>
        <div class="content">
            <h2>New Prescription</h2>
            <p>Dear <?php echo $patient_name; ?>,</p>
            <p>A new prescription has been issued for you by <?php echo $doctor_name; ?> on <?php echo $prescription_date; ?>.</p>
            
            <h3>Prescription Details</h3>
            <p><strong>Prescription ID:</strong> #<?php echo $prescription_id; ?></p>
            <p><strong>Date:</strong> <?php echo $prescription_date; ?></p>
            <p><strong>Status:</strong> <span class="status-badge status-<?php echo strtolower($status); ?>"><?php echo $status; ?></span></p>
            <p><strong>Doctor:</strong> <?php echo $doctor_name; ?></p>
            
            <?php if (!empty($notes)): ?>
                <p><strong>Notes:</strong> <?php echo nl2br($notes); ?></p>
            <?php endif; ?>
            
            <?php if (!empty($medications)): ?>
                <h3>Medications</h3>
                <table>
                    <tr>
                        <th>Medication</th>
                        <th>Dosage</th>
                        <th>Frequency</th>
                        <th>Duration</th>
                        <th>Instructions</th>
                    </tr>
                    <?php foreach ($medications as $medication): ?>
                        <tr>
                            <td><?php echo $medication['medication_name']; ?></td>
                            <td><?php echo $medication['dosage']; ?></td>
                            <td><?php echo $medication['frequency']; ?></td>
                            <td><?php echo $medication['duration']; ?></td>
                            <td><?php echo $medication['instructions']; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </table>
            <?php endif; ?>
            
            <p>You can view your prescription details by logging into your patient portal.</p>
            <p><a href="<?php echo SITE_URL; ?>/patient/view_prescription.php?id=<?php echo $prescription_id; ?>" class="button">View Prescription</a></p>
            
            <p>Thank you for choosing <?php echo $hospital_name; ?> for your healthcare needs.</p>
            <p>Best regards,<br>
            <?php echo $hospital_name; ?> Team</p>
        </div>
        <div class="footer">
            <p><?php echo $hospital_name; ?></p>
            <p><?php echo $hospital_address; ?></p>
            <p>Phone: <?php echo $hospital_phone; ?></p>
            <p>This is an automated message. Please do not reply to this email.</p>
        </div>
    </div>
</body>
</html>
