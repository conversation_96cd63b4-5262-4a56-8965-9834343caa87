# CSUCC Hospital Management System

A comprehensive hospital management system for CSUCC Hospital, designed to streamline patient management, appointments, medical records, and more.

## Directory Structure

### Core Directories

- **admin/** - Admin panel and administrative functionality
- **doctor/** - Doctor portal and doctor-specific functionality
- **patient/** - Patient portal and patient-specific functionality
- **includes/** - Core PHP includes and utility functions
- **database/** - Database-related files and migrations
- **assets/** - Static assets (CSS, JavaScript, images)
- **vendor/** - Composer dependencies
- **tests/** - Test scripts and utilities

### Support Directories

- **docs/** - Documentation files
- **emails/** - Saved email templates and logs
- **fpdf/** - PDF generation library
- **sql/** - SQL scripts (original location, now copied to database/migrations)

## Key Features

1. **User Management**
   - Role-based access control (Ad<PERSON>, Doctor, Patient)
   - User registration and authentication

2. **Patient Management**
   - Patient registration and profile management
   - Medical history tracking
   - Appointment scheduling

3. **Doctor Management**
   - Doctor profiles and specializations
   - Appointment scheduling and management
   - Patient record access

4. **Appointment System**
   - Appointment scheduling and management
   - Email notifications for appointment status changes
   - Calendar integration

5. **Medical Records**
   - Electronic medical records
   - Prescription management
   - Medical history tracking

6. **Email Notifications**
   - Appointment confirmations
   - Appointment reminders
   - Status updates

## Technology Stack

- **Backend**: PHP
- **Database**: MySQL
- **Frontend**: HTML, CSS, JavaScript, Bootstrap
- **Email**: PHPMailer with Gmail SMTP
- **PDF Generation**: FPDF

## Installation

1. Clone the repository
2. Set up a web server (Apache, Nginx) with PHP support
3. Create a MySQL database and import the schema
4. Configure database connection in `db_connect.php`
5. Configure email settings in `includes/simple_gmail_mailer.php`
6. Access the application through your web server

## Email Configuration

The system uses Gmail SMTP for sending emails. To configure email functionality:

1. Update the Gmail credentials in `includes/simple_gmail_mailer.php`
2. Ensure your Gmail account has "Less secure app access" enabled or use an App Password
3. Test the email functionality using the test scripts in `tests/email/`

## Database Configuration

Database connection settings are stored in `db_connect.php`. Update the following variables:

```php
$host = "127.0.0.1"; // Database host
$username = "root"; // Database username
$password = ""; // Database password
$database = "hospital_management_system"; // Database name
```

## Credits

Developed by CSUCC Hospital Management System Team
