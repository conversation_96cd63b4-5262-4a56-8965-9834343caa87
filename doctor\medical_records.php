<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name as department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if filtering by patient
$patient_id = isset($_GET['patient_id']) ? intval($_GET['patient_id']) : 0;
$patient_name = '';

if ($patient_id > 0) {
    $patient_query = "SELECT CONCAT(first_name, ' ', last_name) as full_name FROM patients WHERE patient_id = ?";
    $patient_stmt = $conn->prepare($patient_query);
    $patient_stmt->bind_param("i", $patient_id);
    $patient_stmt->execute();
    $patient_result = $patient_stmt->get_result();
    if ($patient_result->num_rows > 0) {
        $patient_name = $patient_result->fetch_assoc()['full_name'];
    }
}

// Get all medical records for this doctor
$records = [];
$query = "SELECT mr.*, a.appointment_date, a.appointment_time,
          p.first_name as patient_first_name, p.last_name as patient_last_name
          FROM medical_records mr
          JOIN patients p ON mr.patient_id = p.patient_id
          LEFT JOIN appointments a ON mr.appointment_id = a.appointment_id
          WHERE mr.doctor_id = ?";

// Add patient filter if specified
if ($patient_id > 0) {
    $query .= " AND mr.patient_id = ?";
}

$query .= " ORDER BY mr.created_at DESC";

$stmt = $conn->prepare($query);

if ($patient_id > 0) {
    $stmt->bind_param("ii", $doctor_id, $patient_id);
} else {
    $stmt->bind_param("i", $doctor_id);
}

$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    while ($row = $result->fetch_assoc()) {
        $records[] = $row;
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medical Records | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li>
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li class="active">
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-file-medical"></i> Medical Records</h2>
                    <?php if ($patient_name): ?>
                        <span class="subtitle">Patient: <?php echo $patient_name; ?></span>
                    <?php endif; ?>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>All Medical Records</h3>
                    <div class="actions">
                        <a href="add_medical_record.php" class="btn btn-primary"><i class="fas fa-plus"></i> Add Medical Record</a>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <div class="filter-row">
                            <div class="search-group">
                                <input type="text" id="search-input" class="form-control" placeholder="Search records...">
                                <button id="search-btn" class="btn btn-outline"><i class="fas fa-search"></i></button>
                            </div>
                            <?php if ($patient_id > 0): ?>
                                <a href="medical_records.php" class="btn btn-outline"><i class="fas fa-times"></i> Clear Patient Filter</a>
                            <?php endif; ?>
                        </div>

                        <div class="table-responsive">
                            <table class="table" id="recordsTable">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Patient</th>
                                        <th>Diagnosis</th>
                                        <th>Symptoms</th>
                                        <th>Treatment</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (count($records) > 0): ?>
                                        <?php foreach ($records as $record): ?>
                                            <tr>
                                                <td>
                                                    <?php
                                                    if (!empty($record['appointment_date'])) {
                                                        echo date('M d, Y', strtotime($record['appointment_date']));
                                                    } else {
                                                        echo date('M d, Y', strtotime($record['created_at']));
                                                    }
                                                    ?>
                                                </td>
                                                <td><?php echo $record['patient_first_name'] . ' ' . $record['patient_last_name']; ?></td>
                                                <td><?php echo $record['diagnosis']; ?></td>
                                                <td><?php echo substr($record['symptoms'], 0, 50) . (strlen($record['symptoms']) > 50 ? '...' : ''); ?></td>
                                                <td><?php echo substr($record['treatment'], 0, 50) . (strlen($record['treatment']) > 50 ? '...' : ''); ?></td>
                                                <td>
                                                    <div class="action-buttons">
                                                        <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="edit_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="Edit">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                        <a href="delete_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon btn-danger" title="Delete" onclick="return confirm('Are you sure you want to delete this medical record?');">
                                                            <i class="fas fa-trash"></i>
                                                        </a>
                                                        <a href="print_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="Print">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="6" class="text-center">No medical records found</td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Search functionality
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('search-input');
            const searchBtn = document.getElementById('search-btn');
            const tableRows = document.querySelectorAll('#recordsTable tbody tr');

            function performSearch() {
                const searchValue = searchInput.value.toLowerCase();

                tableRows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    let found = false;

                    cells.forEach(cell => {
                        if (cell.textContent.toLowerCase().includes(searchValue)) {
                            found = true;
                        }
                    });

                    if (found) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            }

            searchBtn.addEventListener('click', performSearch);
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        });
    </script>
</body>
</html>
