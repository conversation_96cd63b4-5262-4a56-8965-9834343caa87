-- SQL script to create or update the prescription_medications table
-- This script ensures the prescription_medications table has the correct structure

-- Check if the table exists
SET @tableExists = 0;
SELECT COUNT(*) INTO @tableExists FROM information_schema.tables 
WHERE table_schema = DATABASE() AND table_name = 'prescription_medications';

-- Create the table if it doesn't exist
SET @query = IF(@tableExists = 0, 
    'CREATE TABLE prescription_medications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prescription_id VARCHAR(20) NOT NULL,
        medication_name VARCHAR(255) NOT NULL,
        dosage VARCHAR(100) NOT NULL,
        frequency VARCHAR(100) NOT NULL,
        duration VARCHAR(100) NOT NULL,
        instructions TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        CONSTRAINT fk_prescription_id FOREIGN KEY (prescription_id) REFERENCES prescriptions(prescription_id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci',
    'SELECT "Table prescription_medications already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- If the table exists, check if it has the correct structure
-- Check if the prescription_id column has the correct type
SET @columnExists = 0;
SELECT COUNT(*) INTO @columnExists FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'prescription_medications' AND COLUMN_NAME = 'prescription_id' AND DATA_TYPE = 'varchar';

-- Modify the column if it doesn't have the correct type
SET @query = IF(@columnExists = 0 AND @tableExists = 1, 
    'ALTER TABLE prescription_medications MODIFY COLUMN prescription_id VARCHAR(20) NOT NULL',
    'SELECT "Column prescription_id already has the correct type" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check if the foreign key constraint exists
SET @constraintExists = 0;
SELECT COUNT(*) INTO @constraintExists FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'prescription_medications' 
AND REFERENCED_TABLE_NAME = 'prescriptions' AND CONSTRAINT_NAME = 'fk_prescription_id';

-- Add the foreign key constraint if it doesn't exist
SET @query = IF(@constraintExists = 0 AND @tableExists = 1, 
    'ALTER TABLE prescription_medications ADD CONSTRAINT fk_prescription_id FOREIGN KEY (prescription_id) REFERENCES prescriptions(prescription_id) ON DELETE CASCADE',
    'SELECT "Foreign key constraint already exists" AS message');
PREPARE stmt FROM @query;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;
