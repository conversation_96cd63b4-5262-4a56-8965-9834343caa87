<?php
session_start();
include "../db_connect.php";

// Check if user is logged in and is doctor
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'doctor') {
    header("Location: ../index.php");
    exit();
}

// Get doctor information
$doctor_id = 0;
$doctor_info = [];

$doctor_query = "SELECT d.*, u.email, u.last_login, dep.department_name as department_name
                FROM doctors d
                JOIN users u ON d.user_id = u.user_id
                LEFT JOIN departments dep ON d.department_id = dep.department_id
                WHERE d.user_id = ?";
$doctor_stmt = $conn->prepare($doctor_query);
$doctor_stmt->bind_param("i", $_SESSION['user_id']);
$doctor_stmt->execute();
$doctor_result = $doctor_stmt->get_result();

if ($doctor_result->num_rows > 0) {
    $doctor_info = $doctor_result->fetch_assoc();
    $doctor_id = $doctor_info['doctor_id'];
}

// Check if appointment ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header("Location: appointments.php");
    exit();
}

$appointment_id = $_GET['id'];

// Check if medical_history column exists in patients table
$check_column_query = "SHOW COLUMNS FROM patients LIKE 'medical_history'";
$check_column_result = $conn->query($check_column_query);
$column_exists = $check_column_result->num_rows > 0;

// If the column doesn't exist, add it
if (!$column_exists) {
    $add_column_query = "ALTER TABLE patients ADD COLUMN medical_history TEXT DEFAULT NULL COMMENT 'Patient family medical history'";
    $conn->query($add_column_query);
}

// Get appointment details
$appointment = null;
$query = "SELECT a.*, p.*, d.first_name as doctor_first_name, d.last_name as doctor_last_name,
          dep.department_name, u.email
          FROM appointments a
          JOIN patients p ON a.patient_id = p.patient_id
          JOIN doctors d ON a.doctor_id = d.doctor_id
          JOIN departments dep ON a.department_id = dep.department_id
          LEFT JOIN users u ON p.user_id = u.user_id
          WHERE a.appointment_id = ? AND a.doctor_id = ?";
$stmt = $conn->prepare($query);
$stmt->bind_param("ii", $appointment_id, $doctor_id);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    $appointment = $result->fetch_assoc();
} else {
    // Appointment not found or doesn't belong to this doctor
    header("Location: appointments.php");
    exit();
}

// Get medical records related to this appointment
$medical_records = [];
$records_query = "SELECT * FROM medical_records WHERE appointment_id = ?";
$records_stmt = $conn->prepare($records_query);
$records_stmt->bind_param("i", $appointment_id);
$records_stmt->execute();
$records_result = $records_stmt->get_result();
if ($records_result->num_rows > 0) {
    while ($row = $records_result->fetch_assoc()) {
        $medical_records[] = $row;
    }
}

// Get prescriptions related to this appointment
$prescriptions = [];
// Check if prescriptions table exists
$table_check = $conn->query("SHOW TABLES LIKE 'prescriptions'");
if ($table_check->num_rows > 0) {
    // Check if appointment_id column exists in prescriptions table
    $column_check = $conn->query("SHOW COLUMNS FROM prescriptions LIKE 'appointment_id'");

    if ($column_check->num_rows > 0) {
        // If appointment_id column exists, use it in the query
        $prescriptions_query = "SELECT * FROM prescriptions WHERE appointment_id = ?";
        $prescriptions_stmt = $conn->prepare($prescriptions_query);
        $prescriptions_stmt->bind_param("i", $appointment_id);
    } else {
        // If appointment_id column doesn't exist, just filter by patient_id and doctor_id
        $prescriptions_query = "SELECT * FROM prescriptions WHERE patient_id = ? AND doctor_id = ?";
        $prescriptions_stmt = $conn->prepare($prescriptions_query);
        $prescriptions_stmt->bind_param("ii", $appointment['patient_id'], $doctor_id);
    }

    $prescriptions_stmt->execute();
    $prescriptions_result = $prescriptions_stmt->get_result();
    if ($prescriptions_result->num_rows > 0) {
        while ($row = $prescriptions_result->fetch_assoc()) {
            $prescriptions[] = $row;
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>
                    <li>
                        <a href="patients.php"><i class="fas fa-user-injured"></i> My Patients</a>
                    </li>
                    <li>
                        <a href="schedule.php"><i class="fas fa-clock"></i> My Schedule</a>
                    </li>
                    <li>
                        <a href="medical_records.php"><i class="fas fa-file-medical"></i> Medical Records</a>
                    </li>
                    <li>
                        <a href="prescriptions.php"><i class="fas fa-prescription"></i> Prescriptions</a>
                    </li>
                    <li>
                        <a href="profile.php"><i class="fas fa-user-md"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-check"></i> View Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $doctor_info['profile_image']; ?>" alt="Doctor" class="user-image">
                        <div class="user-details">
                            <h4>Dr. <?php echo $doctor_info['first_name'] . ' ' . $doctor_info['last_name']; ?></h4>
                            <p><?php echo $doctor_info['department_name']; ?></p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="content-wrapper">
                <div class="content-header">
                    <h3>Appointment Details</h3>
                    <div class="actions">
                        <a href="appointments.php" class="btn btn-outline"><i class="fas fa-arrow-left"></i> Back to Appointments</a>
                        <a href="edit_appointment.php?id=<?php echo $appointment['appointment_id']; ?>" class="btn btn-primary"><i class="fas fa-edit"></i> Edit Appointment</a>
                    </div>
                </div>

                <div class="appointment-details">
                    <div class="card">
                        <div class="card-header">
                            <h3>Appointment #<?php echo $appointment['appointment_id']; ?></h3>
                            <span class="status-badge status-<?php echo $appointment['status']; ?>"><?php echo ucfirst($appointment['status']); ?></span>
                        </div>
                        <div class="card-body">
                            <div class="details-grid">
                                <div class="detail-item">
                                    <span class="detail-label">Date:</span>
                                    <span class="detail-value"><?php echo date('F d, Y', strtotime($appointment['appointment_date'])); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Time:</span>
                                    <span class="detail-value"><?php echo date('h:i A', strtotime($appointment['appointment_time'])); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Department:</span>
                                    <span class="detail-value"><?php echo $appointment['department_name']; ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Created On:</span>
                                    <span class="detail-value"><?php echo date('F d, Y', strtotime($appointment['created_at'])); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Last Updated:</span>
                                    <span class="detail-value"><?php echo date('F d, Y', strtotime($appointment['updated_at'])); ?></span>
                                </div>
                                <div class="detail-item">
                                    <span class="detail-label">Reason for Visit:</span>
                                    <span class="detail-value"><?php echo $appointment['reason']; ?></span>
                                </div>
                                <div class="detail-item full-width">
                                    <span class="detail-label">Notes:</span>
                                    <span class="detail-value"><?php echo $appointment['notes']; ?></span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card">
                        <div class="card-header">
                            <h3>Patient Information</h3>
                            <a href="view_patient.php?id=<?php echo $appointment['patient_id']; ?>" class="btn btn-sm btn-outline">View Full Profile</a>
                        </div>
                        <div class="card-body">
                            <div class="patient-info">
                                <div class="patient-image">
                                    <img src="../assets/images/<?php echo $appointment['profile_image']; ?>" alt="Patient">
                                </div>
                                <div class="patient-details">
                                    <div class="details-grid">
                                        <div class="detail-item">
                                            <span class="detail-label">Name:</span>
                                            <span class="detail-value"><?php echo $appointment['first_name'] . ' ' . $appointment['last_name']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Gender:</span>
                                            <span class="detail-value"><?php echo ucfirst($appointment['gender']); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Date of Birth:</span>
                                            <span class="detail-value"><?php echo date('F d, Y', strtotime($appointment['date_of_birth'])); ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Age:</span>
                                            <span class="detail-value"><?php echo date_diff(date_create($appointment['date_of_birth']), date_create('today'))->y; ?> years</span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Phone:</span>
                                            <span class="detail-value"><?php echo $appointment['phone']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Email:</span>
                                            <span class="detail-value"><?php echo $appointment['email']; ?></span>
                                        </div>
                                        <div class="detail-item">
                                            <span class="detail-label">Blood Type:</span>
                                            <span class="detail-value"><?php echo $appointment['blood_type']; ?></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Patient Family Medical History -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Family Medical History</h3>
                        </div>
                        <div class="card-body">
                            <div class="medical-info-section">
                                <div class="detail-item">
                                    <span class="detail-label">Family Medical History:</span>
                                    <span class="detail-value">
                                        <?php
                                        if (isset($appointment['medical_history']) && !empty($appointment['medical_history'])) {
                                            echo $appointment['medical_history'];
                                        } else {
                                            echo 'None reported';
                                        }
                                        ?>
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Records Section -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Medical Records</h3>
                            <a href="add_medical_record.php?appointment_id=<?php echo $appointment['appointment_id']; ?>" class="btn btn-sm btn-primary">Add Medical Record</a>
                        </div>
                        <div class="card-body">
                            <?php if (count($medical_records) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Diagnosis</th>
                                                <th>Treatment</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($medical_records as $record): ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($record['created_at'])); ?></td>
                                                    <td><?php echo $record['diagnosis']; ?></td>
                                                    <td><?php echo $record['treatment']; ?></td>
                                                    <td>
                                                        <a href="view_medical_record.php?id=<?php echo $record['record_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-file-medical empty-icon"></i>
                                    <p>No medical records found for this appointment.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Prescriptions Section -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Prescriptions</h3>
                            <a href="add_prescription.php?appointment_id=<?php echo $appointment['appointment_id']; ?>" class="btn btn-sm btn-primary">Add Prescription</a>
                        </div>
                        <div class="card-body">
                            <?php if (count($prescriptions) > 0): ?>
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>Date</th>
                                                <th>Medication</th>
                                                <th>Dosage</th>
                                                <th>Duration</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($prescriptions as $prescription):
                                                // Get first medication for this prescription
                                                $med_query = "SELECT * FROM prescription_medications WHERE prescription_id = ? LIMIT 1";
                                                $med_stmt = $conn->prepare($med_query);
                                                $med_stmt->bind_param("s", $prescription['prescription_id']);
                                                $med_stmt->execute();
                                                $med_result = $med_stmt->get_result();
                                                $medication = $med_result->fetch_assoc();
                                            ?>
                                                <tr>
                                                    <td><?php echo date('M d, Y', strtotime($prescription['created_at'])); ?></td>
                                                    <td><?php echo $medication ? $medication['medication_name'] : 'N/A'; ?></td>
                                                    <td><?php echo $medication ? $medication['dosage'] : 'N/A'; ?></td>
                                                    <td><?php echo $medication ? $medication['duration'] : 'N/A'; ?></td>
                                                    <td>
                                                        <a href="view_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="View">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                        <a href="print_prescription.php?id=<?php echo $prescription['prescription_id']; ?>" class="btn-icon" title="Print">
                                                            <i class="fas fa-print"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="empty-state">
                                    <i class="fas fa-prescription empty-icon"></i>
                                    <p>No prescriptions found for this appointment.</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Update Status Section -->
                    <div class="card">
                        <div class="card-header">
                            <h3>Update Appointment Status</h3>
                        </div>
                        <div class="card-body">
                            <form action="update_appointment_status.php" method="post">
                                <input type="hidden" name="appointment_id" value="<?php echo $appointment['appointment_id']; ?>">
                                <div class="form-group">
                                    <label for="status">Status:</label>
                                    <select name="status" id="status" class="form-control" required>
                                        <option value="pending" <?php echo $appointment['status'] == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="confirmed" <?php echo $appointment['status'] == 'confirmed' ? 'selected' : ''; ?>>Confirmed</option>
                                        <option value="completed" <?php echo $appointment['status'] == 'completed' ? 'selected' : ''; ?>>Completed</option>
                                        <option value="cancelled" <?php echo $appointment['status'] == 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                                        <option value="no_show" <?php echo $appointment['status'] == 'no_show' ? 'selected' : ''; ?>>No Show</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="notes">Additional Notes:</label>
                                    <textarea name="notes" id="notes" class="form-control" rows="3"><?php echo $appointment['notes']; ?></textarea>
                                </div>
                                <div class="form-actions">
                                    <button type="submit" class="btn btn-primary">Update Status</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
