<?php
session_start();
include "../db_connect.php";

// Include required files
include "../includes/direct_gmail_mailer.php";

// Check if user is logged in and is patient
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'patient') {
    header("Location: ../index.php");
    exit();
}

// Get patient information
$patient_id = 0;
$patient_info = [];

// Check if medical_history column exists in patients table
$check_column_query = "SHOW COLUMNS FROM patients LIKE 'medical_history'";
$check_column_result = $conn->query($check_column_query);
$column_exists = $check_column_result->num_rows > 0;

// If the column doesn't exist, add it
if (!$column_exists) {
    $add_column_query = "ALTER TABLE patients ADD COLUMN medical_history TEXT DEFAULT NULL COMMENT 'Patient family medical history'";
    $conn->query($add_column_query);
}

$patient_query = "SELECT p.*, u.email, u.last_login
                 FROM patients p
                 JOIN users u ON p.user_id = u.user_id
                 WHERE p.user_id = ?";
$patient_stmt = $conn->prepare($patient_query);
$patient_stmt->bind_param("i", $_SESSION['user_id']);
$patient_stmt->execute();
$patient_result = $patient_stmt->get_result();

if ($patient_result->num_rows > 0) {
    $patient_info = $patient_result->fetch_assoc();
    $patient_id = $patient_info['patient_id'];
}

// Get all departments
$departments = [];
$dept_query = "SELECT * FROM departments WHERE status = 'active' ORDER BY department_name";
$dept_result = $conn->query($dept_query);
if ($dept_result->num_rows > 0) {
    while ($row = $dept_result->fetch_assoc()) {
        $departments[] = $row;
    }
}

// Get doctors based on selected department
$doctors = [];
if (isset($_GET['department_id']) && !empty($_GET['department_id'])) {
    $department_id = $_GET['department_id'];
    $doctor_query = "SELECT d.*, dep.department_name
                    FROM doctors d
                    JOIN departments dep ON d.department_id = dep.department_id
                    WHERE d.department_id = ? AND d.status = 'active'
                    ORDER BY d.first_name, d.last_name";
    $doctor_stmt = $conn->prepare($doctor_query);
    $doctor_stmt->bind_param("i", $department_id);
    $doctor_stmt->execute();
    $doctor_result = $doctor_stmt->get_result();
    if ($doctor_result->num_rows > 0) {
        while ($row = $doctor_result->fetch_assoc()) {
            $doctors[] = $row;
        }
    }
}

// Get available time slots for selected doctor and date
$available_slots = [];
if (isset($_GET['doctor_id']) && !empty($_GET['doctor_id']) && isset($_GET['date']) && !empty($_GET['date'])) {
    $doctor_id = $_GET['doctor_id'];
    $selected_date = $_GET['date'];
    $day_of_week = date('N', strtotime($selected_date)); // 1 (Monday) to 7 (Sunday)

    // Get doctor's schedule for the selected day
    $schedule_query = "SELECT * FROM doctor_schedules
                      WHERE doctor_id = ? AND day_of_week = ? AND is_active = 1";
    $schedule_stmt = $conn->prepare($schedule_query);
    $schedule_stmt->bind_param("ii", $doctor_id, $day_of_week);
    $schedule_stmt->execute();
    $schedule_result = $schedule_stmt->get_result();

    if ($schedule_result->num_rows > 0) {
        $schedule = $schedule_result->fetch_assoc();
        $start_time = strtotime($schedule['start_time']);
        $end_time = strtotime($schedule['end_time']);

        // Check if patient already has an appointment with this doctor on the selected date
        $patient_has_appointment = false;
        $check_patient_query = "SELECT * FROM appointments
                              WHERE patient_id = ? AND doctor_id = ? AND appointment_date = ? AND status != 'cancelled'";
        $check_patient_stmt = $conn->prepare($check_patient_query);
        $check_patient_stmt->bind_param("iis", $patient_id, $doctor_id, $selected_date);
        $check_patient_stmt->execute();
        $check_patient_result = $check_patient_stmt->get_result();

        if ($check_patient_result->num_rows > 0) {
            $patient_has_appointment = true;
            // Add a message to be displayed
            $error = "You already have an appointment with this doctor on the selected date. You can only have one appointment per day with the same doctor.";
        }

        // Get booked appointments for the selected doctor and date
        $booked_slots = [];
        $booked_query = "SELECT appointment_time FROM appointments
                        WHERE doctor_id = ? AND appointment_date = ? AND status != 'cancelled'";
        $booked_stmt = $conn->prepare($booked_query);
        $booked_stmt->bind_param("is", $doctor_id, $selected_date);
        $booked_stmt->execute();
        $booked_result = $booked_stmt->get_result();

        if ($booked_result->num_rows > 0) {
            while ($row = $booked_result->fetch_assoc()) {
                $booked_slots[] = $row['appointment_time'];
            }
        }

        // If patient already has an appointment with this doctor on this date, clear available slots
        if ($patient_has_appointment) {
            $available_slots = [];
        }

        // Generate available time slots (30-minute intervals)
        $slot_interval = 30 * 60; // 30 minutes in seconds
        for ($time = $start_time; $time < $end_time; $time += $slot_interval) {
            $slot_time = date('H:i:s', $time);
            if (!in_array($slot_time, $booked_slots)) {
                $available_slots[] = $slot_time;
            }
        }
    }
}

$error = "";
$success = "";

// Debug information
$debug_info = [];

// Process appointment booking
if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['book_appointment'])) {
    // Log form submission
    $debug_info['form_submitted'] = true;
    $debug_info['post_data'] = $_POST;

    // Get form data
    $doctor_id = $_POST['doctor_id'] ?? '';
    $department_id = $_POST['department_id'] ?? '';
    $appointment_date = $_POST['appointment_date'] ?? '';
    $appointment_time = $_POST['appointment_time'] ?? '';
    $reason = $_POST['reason'] ?? '';

    // Get family medical history
    $medical_history = $_POST['medical_history'] ?? '';

    // Validate input
    if (empty($doctor_id) || empty($department_id) || empty($appointment_date) || empty($appointment_time) || empty($reason) || empty($medical_history)) {
        $error = "All fields are required, including Reason for Visit and Family Medical History";
    } else {
        // Check if the slot is still available
        $check_query = "SELECT * FROM appointments
                       WHERE doctor_id = ? AND appointment_date = ? AND appointment_time = ? AND status != 'cancelled'";
        $check_stmt = $conn->prepare($check_query);
        $check_stmt->bind_param("iss", $doctor_id, $appointment_date, $appointment_time);
        $check_stmt->execute();
        $check_result = $check_stmt->get_result();

        if ($check_result->num_rows > 0) {
            $error = "This time slot is no longer available. Please select another time.";
        } else {
            // Check if patient already has an appointment with this doctor on the same day
            $check_same_day_query = "SELECT * FROM appointments
                                   WHERE patient_id = ? AND doctor_id = ? AND appointment_date = ? AND status != 'cancelled'";
            $check_same_day_stmt = $conn->prepare($check_same_day_query);
            $check_same_day_stmt->bind_param("iis", $patient_id, $doctor_id, $appointment_date);
            $check_same_day_stmt->execute();
            $check_same_day_result = $check_same_day_stmt->get_result();

            if ($check_same_day_result->num_rows > 0) {
                $error = "You already have an appointment with this doctor on the selected date. You can only have one appointment per day with the same doctor.";
            } else {
            // Insert appointment
            $debug_info['patient_id'] = $patient_id;

            $insert_query = "INSERT INTO appointments (patient_id, doctor_id, department_id, appointment_date, appointment_time, reason, status, created_at)
                           VALUES (?, ?, ?, ?, ?, ?, 'scheduled', NOW())";
            $insert_stmt = $conn->prepare($insert_query);

            if (!$insert_stmt) {
                $debug_info['prepare_error'] = $conn->error;
                $error = "Database error: Failed to prepare statement - {$conn->error}";
            } else {
                $insert_stmt->bind_param("iiisss", $patient_id, $doctor_id, $department_id, $appointment_date, $appointment_time, $reason);
                $debug_info['bind_params'] = [
                    'patient_id' => $patient_id,
                    'doctor_id' => $doctor_id,
                    'department_id' => $department_id,
                    'appointment_date' => $appointment_date,
                    'appointment_time' => $appointment_time,
                    'reason' => $reason
                ];

                if ($insert_stmt->execute()) {
                    $appointment_id = $conn->insert_id;
                    $debug_info['insert_success'] = true;
                    $debug_info['appointment_id'] = $appointment_id;
                    $success = "Appointment booked successfully!";

                    // Update patient's family medical history if provided
                    if (!empty($medical_history)) {
                        $update_patient_query = "UPDATE patients SET
                                              medical_history = ?
                                              WHERE patient_id = ?";
                        $update_patient_stmt = $conn->prepare($update_patient_query);
                        $update_patient_stmt->bind_param("si", $medical_history, $patient_id);
                        $update_patient_stmt->execute();
                        $debug_info['patient_info_updated'] = true;
                    }

                    // Get doctor information for the email
                    $doctor_query = "SELECT d.* FROM doctors d WHERE d.doctor_id = ?";
                    $doctor_stmt = $conn->prepare($doctor_query);
                    $doctor_stmt->bind_param("i", $doctor_id);
                    $doctor_stmt->execute();
                    $doctor_result = $doctor_stmt->get_result();
                    $doctor_info = $doctor_result->fetch_assoc();

                    // Get appointment details
                    $appointment_query = "SELECT * FROM appointments WHERE appointment_id = ?";
                    $appointment_stmt = $conn->prepare($appointment_query);
                    $appointment_stmt->bind_param("i", $appointment_id);
                    $appointment_stmt->execute();
                    $appointment_result = $appointment_stmt->get_result();
                    $appointment_info = $appointment_result->fetch_assoc();

                    // Send email notification
                    $debug_info['mailer_class_exists'] = class_exists('Mailer');

                    try {
                        // Email will be <NAME_EMAIL> as configured in the Mailer class
                        $debug_info['patient_email'] = $patient_info['email'];

                        $mailer = new Mailer();
                        $email_result = $mailer->sendAppointmentConfirmation($appointment_info, $patient_info, $doctor_info);
                        $debug_info['email_sent'] = $email_result;

                        // Log email details
                        error_log("Attempting to send appointment confirmation email to: " . $patient_info['email']);

                        // Update the appointment record to mark confirmation as sent
                        if ($email_result) {
                            $update_query = "UPDATE appointments SET confirmation_sent = 1, confirmation_sent_date = NOW() WHERE appointment_id = ?";
                            $update_stmt = $conn->prepare($update_query);
                            $update_stmt->bind_param("i", $appointment_id);
                            $update_stmt->execute();
                            $debug_info['confirmation_updated'] = true;
                            error_log("Email sent successfully to: " . $patient_info['email']);
                        } else {
                            error_log("Failed to send email to: " . $patient_info['email']);
                        }
                    } catch (Exception $e) {
                        $debug_info['email_error'] = $e->getMessage();
                        error_log("Exception sending email: " . $e->getMessage());
                    }

                    // Redirect to view appointment page
                    header("Location: view_appointment.php?id={$appointment_id}&success=1");
                    exit();
                } else {
                    $error = "Error booking appointment: {$conn->error}";
                }
            }
        }
    }
}
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Book Appointment | CSUCC Hospital</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.ico" type="image/x-icon">
    <style>
        /* General Styling Improvements */
        body {
            background-color: #f5f7fa;
        }

        .dashboard-content {
            padding: 20px;
        }

        .card {
            border-radius: 12px;
            box-shadow: 0 6px 18px rgba(0,0,0,0.08);
            border: none;
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.12);
        }

        .card-header {
            background: linear-gradient(135deg, #4a7c59 0%, #3a6c49 100%);
            color: white;
            padding: 20px 25px;
            border-bottom: none;
        }

        .card-header h3 {
            margin: 0;
            font-size: 22px;
            font-weight: 600;
        }

        .card-body {
            padding: 30px;
            background-color: #fff;
        }

        /* Form Steps Styling */
        .form-step {
            position: relative;
            padding: 20px;
            border-radius: 10px;
            background-color: #fff;
            margin-bottom: 30px;
            transition: all 0.3s ease;
        }

        .form-step h4 {
            color: #4a7c59;
            font-size: 20px;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
            position: relative;
        }

        .form-step h4:after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 80px;
            height: 2px;
            background-color: #4a7c59;
        }

        /* Department Selection Info */
        .department-selection-info {
            background-color: #f8fcf9;
            border-radius: 10px;
            padding: 15px 20px;
            margin-bottom: 25px;
            border-left: 4px solid #4a7c59;
        }

        .department-selection-info p {
            margin: 0;
            color: #555;
            line-height: 1.5;
            font-size: 15px;
        }

        /* Department Grid Styling */
        .department-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .department-card {
            background-color: #fff;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            display: flex;
            flex-direction: column;
            align-items: center;
            height: 100%;
        }

        .department-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
            background-color: #f8fcf9;
            color: #4a7c59;
        }

        .department-icon {
            width: 70px;
            height: 70px;
            background-color: #edf7f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .department-card:hover .department-icon {
            background-color: #4a7c59;
            color: white;
        }

        .department-icon i {
            font-size: 28px;
            color: #4a7c59;
            transition: all 0.3s ease;
        }

        .department-card:hover .department-icon i {
            color: white;
        }

        .department-card h4 {
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            border: none;
        }

        .department-card h4:after {
            display: none;
        }

        /* Doctor Grid Styling */
        .doctor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 25px;
            margin-top: 20px;
        }

        .doctor-card {
            background-color: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            text-decoration: none;
            color: #333;
            display: flex;
            flex-direction: column;
            height: 100%;
            border: 1px solid #e9ecef;
        }

        .doctor-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
            border-color: #4a7c59;
        }

        .doctor-image {
            height: 180px;
            overflow: hidden;
            position: relative;
        }

        .doctor-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .doctor-card:hover .doctor-image img {
            transform: scale(1.05);
        }

        .doctor-info {
            padding: 20px;
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .doctor-info h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
            color: #333;
            border: none;
        }

        .doctor-info h4:after {
            display: none;
        }

        .doctor-info p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
        }

        .doctor-info p i {
            color: #4a7c59;
            margin-right: 5px;
            width: 16px;
            text-align: center;
        }

        /* Form Controls Styling */
        .form-group {
            margin-bottom: 25px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        input[type="date"],
        select,
        textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 15px;
            transition: all 0.3s ease;
            background-color: #f9f9f9;
        }

        input[type="date"]:focus,
        select:focus,
        textarea:focus {
            border-color: #4a7c59;
            box-shadow: 0 0 0 3px rgba(74, 124, 89, 0.2);
            outline: none;
            background-color: #fff;
        }

        .btn {
            padding: 12px 25px;
            font-size: 16px;
            font-weight: 500;
            border-radius: 8px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .btn-primary {
            background-color: #4a7c59;
            border: none;
            color: white;
        }

        .btn-primary:hover {
            background-color: #3a6c49;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(74, 124, 89, 0.3);
        }

        .btn-outline {
            background-color: transparent;
            border: 2px solid #4a7c59;
            color: #4a7c59;
        }

        .btn-outline:hover {
            background-color: #4a7c59;
            color: white;
        }

        /* Back Link Styling */
        .back-link {
            margin-bottom: 20px;
        }

        .back-link a {
            display: inline-flex;
            align-items: center;
            color: #4a7c59;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .back-link a:hover {
            color: #3a6c49;
            text-decoration: underline;
        }

        .back-link a i {
            margin-right: 5px;
        }

        /* Time Slot Selection */
        #appointment_time {
            appearance: none;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%234a7c59' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
            background-repeat: no-repeat;
            background-position: right 15px center;
            background-size: 16px;
            padding-right: 40px;
        }

        /* Medical Info Section */
        .medical-info-section {
            background-color: #f8fcf9;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            border-left: 5px solid #4a7c59;
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .medical-info-section h4 {
            color: #4a7c59;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 18px;
            border: none;
        }

        .medical-info-section h4:after {
            display: none;
        }

        .info-text {
            font-size: 15px;
            color: #555;
            margin-bottom: 20px;
            line-height: 1.5;
        }

        .form-text {
            display: block;
            font-size: 13px;
            color: #666;
            margin-top: 8px;
            line-height: 1.4;
        }

        .required {
            color: #dc3545;
            font-weight: bold;
            margin-left: 3px;
        }

        textarea[required] {
            border-left: 4px solid #4a7c59;
        }

        /* Empty State Styling */
        .empty-state {
            text-align: center;
            padding: 40px 20px;
            background-color: #f8fcf9;
            border-radius: 12px;
            margin: 20px 0;
        }

        .empty-icon {
            font-size: 60px;
            color: #4a7c59;
            opacity: 0.7;
            margin-bottom: 20px;
        }

        .empty-state p {
            font-size: 16px;
            color: #555;
            margin-bottom: 25px;
        }

        /* Alert Styling */
        .alert {
            padding: 15px 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            position: relative;
        }

        .alert-danger {
            background-color: #fdf2f2;
            border-left: 4px solid #dc3545;
            color: #842029;
        }

        .alert-success {
            background-color: #f0f9f4;
            border-left: 4px solid #4a7c59;
            color: #0f5132;
        }

        /* Progress Indicator */
        .booking-progress {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            position: relative;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        .booking-progress:before {
            content: '';
            position: absolute;
            top: 15px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #e9ecef;
            z-index: 1;
        }

        .progress-step {
            position: relative;
            z-index: 2;
            text-align: center;
            width: 25%;
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background-color: #e9ecef;
            color: #666;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .step-label {
            font-size: 13px;
            color: #666;
            transition: all 0.3s ease;
        }

        .progress-step.active .step-number {
            background-color: #4a7c59;
            color: white;
        }

        .progress-step.active .step-label {
            color: #4a7c59;
            font-weight: 600;
        }

        .progress-step.completed .step-number {
            background-color: #4a7c59;
            color: white;
        }

        /* Date Selection Styling */
        .date-selection-container {
            background-color: #f8fcf9;
            border-radius: 12px;
            padding: 25px;
            margin-top: 20px;
        }

        .date-info-box {
            display: flex;
            background-color: #e8f5ee;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #4a7c59;
        }

        .date-info-icon {
            font-size: 24px;
            color: #4a7c59;
            margin-right: 15px;
            display: flex;
            align-items: center;
        }

        .date-info-content h5 {
            margin-top: 0;
            margin-bottom: 10px;
            color: #4a7c59;
            font-size: 16px;
        }

        .date-info-content p {
            margin: 0 0 8px 0;
            font-size: 14px;
            color: #555;
            line-height: 1.4;
        }

        .date-picker {
            font-size: 16px;
            padding: 12px 15px;
            border-radius: 8px;
            border: 2px solid #ddd;
            transition: all 0.3s ease;
        }

        .date-picker:focus {
            border-color: #4a7c59;
            box-shadow: 0 0 0 3px rgba(74, 124, 89, 0.2);
        }

        .date-picker-help {
            margin-top: 8px;
            color: #666;
            font-size: 13px;
        }

        /* Time Slots Styling */
        .time-slots-container {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-top: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .time-slots-header {
            background-color: #f8fcf9;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid #e0e0e0;
        }

        .time-slots-date {
            font-weight: 600;
            color: #333;
            font-size: 16px;
        }

        .time-slots-date i {
            color: #4a7c59;
            margin-right: 8px;
        }

        .time-slots-count .badge {
            background-color: #4a7c59;
            color: white;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .time-slots-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 10px;
            padding: 20px;
            background-color: white;
        }

        .time-slot {
            position: relative;
        }

        .time-slot input[type="radio"] {
            position: absolute;
            opacity: 0;
            width: 0;
            height: 0;
        }

        .time-slot-label {
            display: block;
            padding: 12px 10px;
            text-align: center;
            background-color: #f5f5f5;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .time-slot input[type="radio"]:checked + .time-slot-label {
            background-color: #4a7c59;
            color: white;
            border-color: #4a7c59;
            box-shadow: 0 2px 8px rgba(74, 124, 89, 0.3);
            transform: translateY(-2px);
        }

        .time-slot-label:hover {
            background-color: #e8f5ee;
            border-color: #4a7c59;
        }

        .time-slot input[type="radio"]:focus + .time-slot-label {
            box-shadow: 0 0 0 3px rgba(74, 124, 89, 0.3);
        }

        .no-slots-message {
            grid-column: 1 / -1;
            text-align: center;
            padding: 30px;
            color: #666;
        }

        .no-slots-message i {
            font-size: 40px;
            color: #ccc;
            margin-bottom: 15px;
        }

        /* Custom Alert Styling */
        .custom-alert {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 350px;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
            padding: 15px;
            display: flex;
            align-items: center;
            z-index: 1000;
            transform: translateX(120%);
            transition: transform 0.3s ease;
            border-left: 4px solid;
        }

        .custom-alert.show {
            transform: translateX(0);
        }

        .custom-alert.alert-warning {
            border-color: #ffc107;
        }

        .custom-alert.alert-success {
            border-color: #4a7c59;
        }

        .custom-alert.alert-error {
            border-color: #dc3545;
        }

        .alert-icon {
            margin-right: 15px;
            font-size: 20px;
        }

        .alert-warning .alert-icon {
            color: #ffc107;
        }

        .alert-success .alert-icon {
            color: #4a7c59;
        }

        .alert-error .alert-icon {
            color: #dc3545;
        }

        .alert-message {
            flex-grow: 1;
            font-size: 14px;
        }

        .alert-close {
            background: none;
            border: none;
            color: #999;
            cursor: pointer;
            font-size: 16px;
            padding: 0;
            margin-left: 10px;
        }

        .alert-close:hover {
            color: #333;
        }

        /* Button Animation */
        .btn-pulse {
            animation: pulse 1s;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(74, 124, 89, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(74, 124, 89, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(74, 124, 89, 0);
            }
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .department-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }

            .doctor-grid {
                grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
            }

            .card-body {
                padding: 20px;
            }

            .form-step {
                padding: 15px;
            }

            .time-slots-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }
        }

        @media (max-width: 576px) {
            .department-grid {
                grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
                gap: 15px;
            }

            .doctor-grid {
                grid-template-columns: 1fr;
            }

            .card-body {
                padding: 15px;
            }

            .btn {
                width: 100%;
                margin-bottom: 10px;
            }

            .time-slots-grid {
                grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
                padding: 15px;
            }

            .time-slots-header {
                flex-direction: column;
                align-items: flex-start;
            }

            .time-slots-count {
                margin-top: 8px;
            }

            .date-info-box {
                flex-direction: column;
            }

            .date-info-icon {
                margin-bottom: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <h3><i class="fas fa-hospital"></i> CSUCC Hospital</h3>
            </div>
            <nav class="sidebar-nav">
                <ul>
                    <li>
                        <a href="dashboard.php"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                    </li>
                    <li class="active">
                        <a href="appointments.php"><i class="fas fa-calendar-check"></i> Appointments</a>
                    </li>

                    <li>
                        <a href="profile.php"><i class="fas fa-user"></i> My Profile</a>
                    </li>
                    <li>
                        <a href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <h2><i class="fas fa-calendar-plus"></i> Book Appointment</h2>
                </div>
                <div class="header-right">
                    <div class="user-info">
                        <img src="../assets/images/<?php echo $patient_info['profile_image']; ?>" alt="Patient" class="user-image">
                        <div class="user-details">
                            <h4><?php echo $patient_info['first_name'] . ' ' . $patient_info['last_name']; ?></h4>
                            <p>Patient</p>
                        </div>
                    </div>
                </div>
            </header>

            <div class="dashboard-content">
                <div class="card">
                    <div class="card-header">
                        <h3>Book New Appointment</h3>
                    </div>
                    <div class="card-body">
                        <!-- Progress Indicator -->
                        <div class="booking-progress">
                            <div class="progress-step <?php echo !isset($_GET['department_id']) ? 'active' : (isset($_GET['department_id']) ? 'completed' : ''); ?>">
                                <div class="step-number">1</div>
                                <div class="step-label">Department</div>
                            </div>
                            <div class="progress-step <?php echo (isset($_GET['department_id']) && !isset($_GET['doctor_id'])) ? 'active' : (isset($_GET['doctor_id']) ? 'completed' : ''); ?>">
                                <div class="step-number">2</div>
                                <div class="step-label">Doctor</div>
                            </div>
                            <div class="progress-step <?php echo (isset($_GET['doctor_id']) && !isset($_GET['date'])) ? 'active' : (isset($_GET['date']) ? 'completed' : ''); ?>">
                                <div class="step-number">3</div>
                                <div class="step-label">Date</div>
                            </div>
                            <div class="progress-step <?php echo isset($_GET['date']) ? 'active' : ''; ?>">
                                <div class="step-number">4</div>
                                <div class="step-label">Complete</div>
                            </div>
                        </div>

                        <?php if (!empty($error) && !isset($_GET['date'])): ?>
                            <div class="alert alert-danger">
                                <?php echo $error; ?>
                            </div>
                        <?php endif; ?>

                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success">
                                <?php echo $success; ?>
                            </div>
                        <?php endif; ?>

                        <?php if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['book_appointment']) && !empty($debug_info)): ?>
                            <div class="alert alert-info">
                                <h4>Debug Information</h4>
                                <pre><?php print_r($debug_info); ?></pre>
                            </div>
                        <?php endif; ?>

                        <form action="" method="post" class="booking-form">
                            <div class="form-step" id="step1" <?php echo isset($_GET['department_id']) ? 'style="display:none;"' : ''; ?>>
                                <h4>Step 1: Select Department</h4>

                                <div class="department-selection-info">
                                    <p>Please select a medical department that best matches your healthcare needs. Each department specializes in different areas of medicine.</p>
                                </div>

                                <div class="department-grid">
                                    <?php foreach ($departments as $department): ?>
                                        <?php
                                            // Define icons for departments
                                            $icon = 'fa-stethoscope'; // Default icon

                                            // Map department names to specific icons
                                            $dept_name = strtolower($department['department_name']);
                                            if (strpos($dept_name, 'cardio') !== false) {
                                                $icon = 'fa-heartbeat';
                                            } elseif (strpos($dept_name, 'neuro') !== false) {
                                                $icon = 'fa-brain';
                                            } elseif (strpos($dept_name, 'ortho') !== false) {
                                                $icon = 'fa-bone';
                                            } elseif (strpos($dept_name, 'pediatric') !== false) {
                                                $icon = 'fa-baby';
                                            } elseif (strpos($dept_name, 'gynecology') !== false || strpos($dept_name, 'obstetrics') !== false) {
                                                $icon = 'fa-female';
                                            } elseif (strpos($dept_name, 'dermatology') !== false) {
                                                $icon = 'fa-allergies';
                                            } elseif (strpos($dept_name, 'ophthalmology') !== false) {
                                                $icon = 'fa-eye';
                                            } elseif (strpos($dept_name, 'dental') !== false) {
                                                $icon = 'fa-tooth';
                                            } elseif (strpos($dept_name, 'ent') !== false) {
                                                $icon = 'fa-ear';
                                            } elseif (strpos($dept_name, 'psychiatry') !== false) {
                                                $icon = 'fa-brain';
                                            } elseif (strpos($dept_name, 'radiology') !== false) {
                                                $icon = 'fa-x-ray';
                                            } elseif (strpos($dept_name, 'oncology') !== false) {
                                                $icon = 'fa-ribbon';
                                            } elseif (strpos($dept_name, 'urology') !== false) {
                                                $icon = 'fa-kidneys';
                                            } elseif (strpos($dept_name, 'gastro') !== false) {
                                                $icon = 'fa-stomach';
                                            } elseif (strpos($dept_name, 'pulmonology') !== false) {
                                                $icon = 'fa-lungs';
                                            }
                                        ?>
                                        <a href="?department_id=<?php echo $department['department_id']; ?>" class="department-card">
                                            <div class="department-icon">
                                                <i class="fas <?php echo $icon; ?>"></i>
                                            </div>
                                            <h4><?php echo $department['department_name']; ?></h4>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>

                            <div class="form-step" id="step2" <?php echo !isset($_GET['department_id']) || isset($_GET['doctor_id']) ? 'style="display:none;"' : ''; ?>>
                                <h4>Step 2: Select Doctor</h4>
                                <div class="back-link">
                                    <a href="book_appointment.php"><i class="fas fa-arrow-left"></i> Back to Departments</a>
                                </div>

                                <div class="doctor-grid">
                                    <?php foreach ($doctors as $doctor): ?>
                                        <a href="?department_id=<?php echo $_GET['department_id']; ?>&doctor_id=<?php echo $doctor['doctor_id']; ?>" class="doctor-card">
                                            <div class="doctor-image">
                                                <img src="../assets/images/<?php echo $doctor['profile_image']; ?>" alt="Doctor">
                                            </div>
                                            <div class="doctor-info">
                                                <h4>Dr. <?php echo $doctor['first_name'] . ' ' . $doctor['last_name']; ?></h4>
                                                <p><?php echo $doctor['specialization']; ?></p>
                                                <p><i class="fas fa-hospital"></i> <?php echo $doctor['department_name']; ?></p>
                                            </div>
                                        </a>
                                    <?php endforeach; ?>
                                </div>

                                <?php if (count($doctors) == 0): ?>
                                    <div class="empty-state">
                                        <i class="fas fa-user-md empty-icon"></i>
                                        <p>No doctors available in this department.</p>
                                        <a href="book_appointment.php" class="btn btn-primary">Select Another Department</a>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <div class="form-step" id="step3" <?php echo !isset($_GET['doctor_id']) || isset($_GET['date']) ? 'style="display:none;"' : ''; ?>>
                                <h4>Step 3: Select Date</h4>
                                <div class="back-link">
                                    <a href="?department_id=<?php echo $_GET['department_id']; ?>"><i class="fas fa-arrow-left"></i> Back to Doctors</a>
                                </div>

                                <div class="date-selection-container">
                                    <div class="date-info-box">
                                        <div class="date-info-icon">
                                            <i class="fas fa-info-circle"></i>
                                        </div>
                                        <div class="date-info-content">
                                            <h5>Appointment Availability</h5>
                                            <p>Please select a date to check for available time slots. You can book appointments up to 30 days in advance.</p>
                                            <p>The doctor's availability varies by day of the week. After selecting a date, we'll show you all available time slots.</p>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="appointment_date">Select Date:</label>
                                        <input type="date" id="appointment_date" name="appointment_date" min="<?php echo date('Y-m-d'); ?>" max="<?php echo date('Y-m-d', strtotime('+30 days')); ?>" class="date-picker">
                                        <div class="date-picker-help">
                                            <small>Select a date between <?php echo date('M d, Y'); ?> and <?php echo date('M d, Y', strtotime('+30 days')); ?></small>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="button" id="check_availability" class="btn btn-primary">
                                            <i class="fas fa-calendar-check"></i> Check Availability
                                        </button>
                                    </div>
                                </div>

                                <script>
                                    document.addEventListener('DOMContentLoaded', function() {
                                        const dateInput = document.getElementById('appointment_date');
                                        const checkBtn = document.getElementById('check_availability');

                                        // Set default date to tomorrow
                                        const tomorrow = new Date();
                                        tomorrow.setDate(tomorrow.getDate() + 1);
                                        dateInput.valueAsDate = tomorrow;

                                        // Add event listener for date input
                                        dateInput.addEventListener('change', function() {
                                            // Enable button when date is selected
                                            checkBtn.classList.add('btn-pulse');
                                            setTimeout(() => {
                                                checkBtn.classList.remove('btn-pulse');
                                            }, 1000);
                                        });

                                        checkBtn.addEventListener('click', function() {
                                            const date = dateInput.value;

                                            if (!date) {
                                                showAlert('Please select a date', 'warning');
                                                dateInput.focus();
                                                return;
                                            }

                                            // Validate date is within allowed range
                                            const today = new Date();
                                            today.setHours(0, 0, 0, 0);

                                            const selectedDate = new Date(date);
                                            selectedDate.setHours(0, 0, 0, 0);

                                            const maxDate = new Date();
                                            maxDate.setDate(maxDate.getDate() + 30);
                                            maxDate.setHours(0, 0, 0, 0);

                                            if (selectedDate < today) {
                                                showAlert('Please select a date from today onwards', 'warning');
                                                return;
                                            }

                                            if (selectedDate > maxDate) {
                                                showAlert('Please select a date within the next 30 days', 'warning');
                                                return;
                                            }

                                            // Show loading indicator
                                            checkBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
                                            checkBtn.disabled = true;

                                            // Proceed to next step after a short delay to show loading effect
                                            setTimeout(() => {
                                                window.location.href = `?department_id=<?php echo $_GET['department_id']; ?>&doctor_id=<?php echo $_GET['doctor_id']; ?>&date=${date}`;
                                            }, 800);
                                        });

                                        // Function to show custom alerts
                                        function showAlert(message, type) {
                                            const alertDiv = document.createElement('div');
                                            alertDiv.className = `custom-alert alert-${type}`;
                                            alertDiv.innerHTML = `
                                                <div class="alert-icon"><i class="fas fa-exclamation-circle"></i></div>
                                                <div class="alert-message">${message}</div>
                                                <button class="alert-close"><i class="fas fa-times"></i></button>
                                            `;
                                            document.body.appendChild(alertDiv);

                                            // Show the alert with animation
                                            setTimeout(() => alertDiv.classList.add('show'), 10);

                                            // Auto close after 3 seconds
                                            setTimeout(() => {
                                                alertDiv.classList.remove('show');
                                                setTimeout(() => alertDiv.remove(), 300);
                                            }, 3000);

                                            // Close button functionality
                                            alertDiv.querySelector('.alert-close').addEventListener('click', () => {
                                                alertDiv.classList.remove('show');
                                                setTimeout(() => alertDiv.remove(), 300);
                                            });
                                        }
                                    });
                                </script>
                            </div>

                            <div class="form-step" id="step4" <?php echo !isset($_GET['date']) ? 'style="display:none;"' : ''; ?>>
                                <h4>Step 4: Complete Booking</h4>
                                <div class="back-link">
                                    <a href="?department_id=<?php echo $_GET['department_id']; ?>&doctor_id=<?php echo $_GET['doctor_id']; ?>"><i class="fas fa-arrow-left"></i> Back to Date Selection</a>
                                </div>

                                <?php if (!empty($error)): ?>
                                    <div class="alert alert-danger">
                                        <?php echo $error; ?>
                                    </div>
                                    <div class="empty-state">
                                        <i class="fas fa-calendar-times empty-icon"></i>
                                        <p>Please select another date or doctor.</p>
                                        <a href="?department_id=<?php echo $_GET['department_id']; ?>&doctor_id=<?php echo $_GET['doctor_id']; ?>" class="btn btn-primary">Select Another Date</a>
                                        <a href="?department_id=<?php echo $_GET['department_id']; ?>" class="btn btn-outline">Select Another Doctor</a>
                                    </div>
                                <?php elseif (count($available_slots) > 0): ?>
                                    <input type="hidden" name="department_id" value="<?php echo $_GET['department_id']; ?>">
                                    <input type="hidden" name="doctor_id" value="<?php echo $_GET['doctor_id']; ?>">
                                    <input type="hidden" name="appointment_date" value="<?php echo $_GET['date']; ?>">

                                    <div class="form-group">
                                        <label for="appointment_time">Select Time:</label>
                                        <div class="time-slots-container">
                                            <div class="time-slots-header">
                                                <div class="time-slots-date">
                                                    <i class="fas fa-calendar-day"></i>
                                                    <?php echo date('l, F j, Y', strtotime($_GET['date'])); ?>
                                                </div>
                                                <div class="time-slots-count">
                                                    <span class="badge"><?php echo count($available_slots); ?> available slots</span>
                                                </div>
                                            </div>
                                            <div class="time-slots-grid">
                                                <?php if (count($available_slots) > 0): ?>
                                                    <?php foreach ($available_slots as $slot): ?>
                                                        <div class="time-slot">
                                                            <input type="radio" name="appointment_time" id="time_<?php echo str_replace(':', '_', $slot); ?>"
                                                                value="<?php echo $slot; ?>" required>
                                                            <label for="time_<?php echo str_replace(':', '_', $slot); ?>" class="time-slot-label">
                                                                <span class="time-display"><?php echo date('h:i A', strtotime($slot)); ?></span>
                                                            </label>
                                                        </div>
                                                    <?php endforeach; ?>
                                                <?php else: ?>
                                                    <div class="no-slots-message">
                                                        <i class="fas fa-calendar-times"></i>
                                                        <p>No available time slots for this date.</p>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="reason">Reason for Visit: <span class="required">*</span></label>
                                        <textarea name="reason" id="reason" rows="3" required></textarea>
                                        <small class="form-text">Please provide detailed information about why you're seeking medical attention.</small>
                                    </div>

                                    <div class="medical-info-section">
                                        <h4>Family Medical History</h4>
                                        <p class="info-text">Please provide your family medical history to help the doctor better understand your condition. This information will be shared with your doctor.</p>

                                        <div class="form-group">
                                            <label for="medical_history">Family Medical History: <span class="required">*</span></label>
                                            <textarea name="medical_history" id="medical_history" rows="4" required><?php echo $patient_info['medical_history'] ?? ''; ?></textarea>
                                            <small class="form-text">Include any relevant family medical conditions, hereditary diseases, or other important health information about your family members.</small>
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <button type="submit" name="book_appointment" class="btn btn-primary">Book Appointment</button>
                                    </div>
                                <?php else: ?>
                                    <div class="empty-state">
                                        <i class="fas fa-calendar-times empty-icon"></i>
                                        <p>No available time slots for the selected date.</p>
                                        <a href="?department_id=<?php echo $_GET['department_id']; ?>&doctor_id=<?php echo $_GET['doctor_id']; ?>" class="btn btn-primary">Select Another Date</a>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
